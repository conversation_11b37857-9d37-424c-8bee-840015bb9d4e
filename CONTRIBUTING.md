# Contributing to MedyTrack Mobile 🤝

Thank you for your interest in contributing to MedyTrack Mobile! We welcome contributions from developers of all skill levels. This document provides guidelines and information for contributors.

## 📋 Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Coding Standards](#coding-standards)
- [Testing Guidelines](#testing-guidelines)
- [Pull Request Process](#pull-request-process)
- [Issue Reporting](#issue-reporting)
- [Architecture Guidelines](#architecture-guidelines)

## 🤝 Code of Conduct

This project adheres to a code of conduct that we expect all contributors to follow:

- Be respectful and inclusive
- Focus on constructive feedback
- Help others learn and grow
- Maintain a professional environment
- Report any unacceptable behavior

## 🚀 Getting Started

### Prerequisites

- Flutter SDK 3.24.0+
- Dart SDK 3.5.0+
- Git
- Android Studio or VS Code with Flutter extensions
- Basic knowledge of Flutter, Dart, and BLoC pattern

### Development Setup

1. **Fork the repository** on GitHub
2. **Clone your fork** locally:
   ```bash
   git clone https://github.com/YOUR_USERNAME/MedyTrack-Mobile.git
   cd MedyTrack-Mobile
   ```
3. **Add upstream remote**:
   ```bash
   git remote add upstream https://github.com/BeeGaat/MedyTrack-Mobile.git
   ```
4. **Install dependencies**:
   ```bash
   flutter pub get
   ```
5. **Set up Supabase** (see README.md for details)

## 🔄 Development Workflow

### Branch Strategy

- `main` - Production-ready code
- `develop` - Integration branch for features
- `feature/feature-name` - New features
- `bugfix/bug-description` - Bug fixes
- `hotfix/critical-fix` - Critical production fixes

### Workflow Steps

1. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes** following our coding standards

3. **Test your changes**:
   ```bash
   flutter test
   flutter analyze
   ```

4. **Commit your changes**:
   ```bash
   git commit -m "feat: add new medicine search functionality"
   ```

5. **Push to your fork**:
   ```bash
   git push origin feature/your-feature-name
   ```

6. **Create a Pull Request** on GitHub

## 📝 Coding Standards

### Dart/Flutter Guidelines

- Follow [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- Use `flutter analyze` to check code quality
- Format code with `dart format`
- Use meaningful variable and function names
- Add documentation comments for public APIs

### File Organization

```
lib/
├── core/
│   ├── config/         # App configuration
│   ├── di/            # Dependency injection
│   ├── router/        # Navigation setup
│   └── theme/         # UI theming
├── data/
│   ├── datasources/   # API and local data sources
│   ├── models/        # Data models
│   └── repositories/ # Repository implementations
├── domain/
│   ├── entities/      # Business entities
│   ├── repositories/ # Repository interfaces
│   └── usecases/     # Business logic
└── presentation/
    ├── bloc/         # State management
    ├── pages/        # Screen widgets
    └── widgets/      # Reusable components
```

### Naming Conventions

- **Files**: `snake_case.dart`
- **Classes**: `PascalCase`
- **Variables/Functions**: `camelCase`
- **Constants**: `SCREAMING_SNAKE_CASE`
- **Private members**: `_leadingUnderscore`

### Code Documentation

```dart
/// Manages medicine data and provides CRUD operations.
/// 
/// This repository handles all medicine-related data operations
/// including fetching, creating, updating, and deleting medicines.
/// It integrates with Supabase backend and local caching.
class MedicineRepository {
  /// Fetches all medicines for the current household.
  /// 
  /// Returns a [Stream] of [List<Medicine>] that updates in real-time
  /// when medicines are added, updated, or deleted.
  /// 
  /// Throws [MedicineException] if the operation fails.
  Stream<List<Medicine>> getMedicines() {
    // Implementation
  }
}
```

## 🧪 Testing Guidelines

### Test Structure

- **Unit Tests**: `test/unit/`
- **Widget Tests**: `test/widget/`
- **Integration Tests**: `test/integration/`

### Writing Tests

```dart
group('MedicineBloc', () {
  late MedicineBloc medicineBloc;
  late MockMedicineRepository mockRepository;

  setUp(() {
    mockRepository = MockMedicineRepository();
    medicineBloc = MedicineBloc(repository: mockRepository);
  });

  test('should emit loading then loaded when medicines are fetched', () {
    // Arrange
    final medicines = [Medicine(id: '1', name: 'Aspirin')];
    when(() => mockRepository.getMedicines())
        .thenAnswer((_) => Stream.value(medicines));

    // Act
    medicineBloc.add(LoadMedicines());

    // Assert
    expectLater(
      medicineBloc.stream,
      emitsInOrder([
        MedicineLoading(),
        MedicineLoaded(medicines: medicines),
      ]),
    );
  });
});
```

### Test Requirements

- All new features must include tests
- Aim for >80% code coverage
- Test both success and error scenarios
- Mock external dependencies

## 📥 Pull Request Process

### Before Submitting

1. **Update documentation** if needed
2. **Add tests** for new functionality
3. **Run all tests**: `flutter test`
4. **Check code quality**: `flutter analyze`
5. **Format code**: `dart format .`

### PR Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Widget tests added/updated
- [ ] Manual testing completed

## Screenshots (if applicable)
Add screenshots for UI changes

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Tests pass
- [ ] Documentation updated
```

### Review Process

1. **Automated checks** must pass
2. **Code review** by maintainers
3. **Testing** on multiple devices
4. **Approval** from at least one maintainer
5. **Merge** to develop branch

## 🐛 Issue Reporting

### Bug Reports

Use the bug report template:

```markdown
**Bug Description**
Clear description of the bug

**Steps to Reproduce**
1. Go to '...'
2. Click on '...'
3. See error

**Expected Behavior**
What should happen

**Screenshots**
Add screenshots if applicable

**Environment**
- Device: [e.g. iPhone 12, Pixel 5]
- OS: [e.g. iOS 15.0, Android 12]
- App Version: [e.g. 1.0.0]
```

### Feature Requests

Use the feature request template:

```markdown
**Feature Description**
Clear description of the feature

**Problem Statement**
What problem does this solve?

**Proposed Solution**
How should this work?

**Alternatives Considered**
Other solutions you've considered

**Additional Context**
Any other context or screenshots
```

## 🏗️ Architecture Guidelines

### BLoC Pattern

- Use BLoC for state management
- Separate events, states, and business logic
- Follow single responsibility principle
- Use repositories for data access

### Clean Architecture

- **Presentation Layer**: UI components and BLoCs
- **Domain Layer**: Business logic and entities
- **Data Layer**: Repositories and data sources

### Dependency Injection

- Use GetIt for dependency injection
- Register dependencies in `core/di/`
- Use interfaces for testability

## 🎯 Contribution Areas

We welcome contributions in these areas:

- **Features**: New functionality and improvements
- **Bug Fixes**: Resolving issues and edge cases
- **Documentation**: Improving guides and API docs
- **Testing**: Adding test coverage
- **Performance**: Optimizations and improvements
- **UI/UX**: Design enhancements and accessibility
- **Localization**: Adding new language support

## 📞 Getting Help

- **GitHub Discussions**: For questions and ideas
- **GitHub Issues**: For bugs and feature requests
- **Code Review**: Ask for feedback on PRs

Thank you for contributing to MedyTrack Mobile! 🙏

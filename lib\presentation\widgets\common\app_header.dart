import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

/// Modern app header component matching the mockup design
/// Features: Logo/branding, hamburger menu, and clean layout
class AppHeader extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final bool showHamburgerMenu;
  final bool showBackButton;
  final VoidCallback? onMenuTap;
  final VoidCallback? onBackTap;
  final List<Widget>? actions;
  final Color? backgroundColor;
  final bool showLogo;

  const AppHeader({
    super.key,
    this.title,
    this.subtitle,
    this.showHamburgerMenu = true,
    this.showBackButton = false,
    this.onMenuTap,
    this.onBackTap,
    this.actions,
    this.backgroundColor,
    this.showLogo = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Row(
            children: [
              // Left side: Logo or Back button
              if (showBackButton)
                _buildBackButton()
              else if (showLogo)
                _buildLogo(),
              
              // Center: Title and subtitle
              if (title != null) ...[
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        title!,
                        style: AppTextStyles.titleLarge.copyWith(
                          color: AppColors.navy,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          subtitle!,
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.grey600,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ] else
                const Spacer(),
              
              // Right side: Actions and hamburger menu
              if (actions != null) ...actions!,
              if (showHamburgerMenu) ...[
                const SizedBox(width: 8),
                _buildHamburgerMenu(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: AppColors.teal,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.medical_services,
            color: Colors.white,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          'MEDYTRACK',
          style: AppTextStyles.titleMedium.copyWith(
            color: AppColors.navy,
            fontWeight: FontWeight.bold,
            letterSpacing: 0.5,
          ),
        ),
      ],
    );
  }

  Widget _buildBackButton() {
    return GestureDetector(
      onTap: onBackTap,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppColors.grey100,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(
          Icons.arrow_back_ios_new,
          color: AppColors.navy,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildHamburgerMenu() {
    return GestureDetector(
      onTap: onMenuTap,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppColors.grey100,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(
          Icons.menu,
          color: AppColors.navy,
          size: 24,
        ),
      ),
    );
  }
}

/// Profile header component for user-specific screens
class ProfileHeader extends StatelessWidget {
  final String? userName;
  final String? userTitle;
  final String? userSubtitle;
  final String? avatarUrl;
  final VoidCallback? onProfileTap;
  final VoidCallback? onMenuTap;
  final bool showNotificationBadge;
  final int notificationCount;

  const ProfileHeader({
    super.key,
    this.userName,
    this.userTitle,
    this.userSubtitle,
    this.avatarUrl,
    this.onProfileTap,
    this.onMenuTap,
    this.showNotificationBadge = false,
    this.notificationCount = 0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Row(
            children: [
              // Profile avatar and info
              GestureDetector(
                onTap: onProfileTap,
                child: Row(
                  children: [
                    _buildAvatar(),
                    const SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (userName != null)
                          Text(
                            userName!,
                            style: AppTextStyles.titleMedium.copyWith(
                              color: AppColors.navy,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        if (userTitle != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            userTitle!,
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.grey600,
                            ),
                          ),
                        ],
                        if (userSubtitle != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            userSubtitle!,
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.grey500,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
              
              const Spacer(),
              
              // Hamburger menu
              GestureDetector(
                onTap: onMenuTap,
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.grey100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Stack(
                    children: [
                      Center(
                        child: Icon(
                          Icons.menu,
                          color: AppColors.navy,
                          size: 24,
                        ),
                      ),
                      if (showNotificationBadge && notificationCount > 0)
                        Positioned(
                          right: 6,
                          top: 6,
                          child: Container(
                            width: 12,
                            height: 12,
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: Text(
                                notificationCount > 9 ? '9+' : notificationCount.toString(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 8,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        color: AppColors.tealLight,
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.teal,
          width: 2,
        ),
      ),
      child: avatarUrl != null
          ? ClipOval(
              child: Image.network(
                avatarUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildDefaultAvatar(),
              ),
            )
          : _buildDefaultAvatar(),
    );
  }

  Widget _buildDefaultAvatar() {
    return Icon(
      Icons.person,
      color: AppColors.teal,
      size: 32,
    );
  }
}

import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../core/di/injection_container.dart';
import '../core/services/supabase_service.dart';
import 'logger.dart';

/// Centralized error reporting utility for MedyTrack Mobile
/// 
/// Reports errors to Supabase app_errors table with device context
/// Handles network failures gracefully without crashing the app
class ErrorReporter {
  static final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  static String? _cachedDeviceInfo;
  static String? _cachedPlatform;

  /// Capture and report an error to Supabase
  /// 
  /// [error] - The error object to report
  /// [stackTrace] - Optional stack trace
  /// [context] - Optional additional context as key-value pairs
  static Future<void> captureError(
    Object error, 
    StackTrace? stackTrace, {
    Map<String, dynamic>? context,
  }) async {
    try {
      // Get device and platform info
      final deviceInfo = await _getDeviceInfo();
      final platform = await _getPlatform();
      
      // Get current user ID if available
      String? userId;
      try {
        final supabaseService = getIt<SupabaseService>();
        final user = supabaseService.client.auth.currentUser;
        userId = user?.id;
      } catch (e) {
        // Ignore auth errors when reporting other errors
        AppLogger.warning('Could not get user ID for error reporting', error: e);
      }

      // Prepare error data
      final errorData = {
        'user_id': userId,
        'device': deviceInfo,
        'platform': platform,
        'error_message': error.toString(),
        'stack_trace': stackTrace?.toString(),
        'context': context,
        'created_at': DateTime.now().toIso8601String(),
      };

      // Report to Supabase
      await _reportToSupabase(errorData);
      
      // Log locally for debugging
      AppLogger.error('Error reported to Supabase', error: error, stackTrace: stackTrace);
      
    } catch (reportingError) {
      // Never crash the app due to error reporting failures
      AppLogger.warning('Failed to report error to Supabase', error: reportingError);
      
      // Still log the original error locally
      AppLogger.error('Original error (reporting failed)', error: error, stackTrace: stackTrace);
    }
  }

  /// Report error data to Supabase app_errors table
  static Future<void> _reportToSupabase(Map<String, dynamic> errorData) async {
    try {
      final supabaseService = getIt<SupabaseService>();
      
      await supabaseService.executeWithRetry(
        () async {
          await supabaseService.client
              .from('app_errors')
              .insert(errorData);
        },
        maxRetries: 2,
        operationName: 'error_reporting',
      );
      
    } catch (e) {
      // Log but don't rethrow - error reporting should never crash the app
      AppLogger.warning('Supabase error reporting failed', error: e);
    }
  }

  /// Get device information string
  static Future<String> _getDeviceInfo() async {
    if (_cachedDeviceInfo != null) {
      return _cachedDeviceInfo!;
    }

    try {
      if (kIsWeb) {
        final webInfo = await _deviceInfo.webBrowserInfo;
        _cachedDeviceInfo = '${webInfo.browserName} ${webInfo.appVersion}';
      } else if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        _cachedDeviceInfo = '${androidInfo.brand} ${androidInfo.model} (Android ${androidInfo.version.release})';
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        _cachedDeviceInfo = '${iosInfo.name} ${iosInfo.model} (iOS ${iosInfo.systemVersion})';
      } else {
        _cachedDeviceInfo = 'Unknown Device';
      }
    } catch (e) {
      _cachedDeviceInfo = 'Device Info Unavailable';
      AppLogger.warning('Failed to get device info', error: e);
    }

    return _cachedDeviceInfo!;
  }

  /// Get platform information string
  static Future<String> _getPlatform() async {
    if (_cachedPlatform != null) {
      return _cachedPlatform!;
    }

    try {
      if (kIsWeb) {
        _cachedPlatform = 'Web';
      } else if (Platform.isAndroid) {
        _cachedPlatform = 'Android';
      } else if (Platform.isIOS) {
        _cachedPlatform = 'iOS';
      } else if (Platform.isMacOS) {
        _cachedPlatform = 'macOS';
      } else if (Platform.isWindows) {
        _cachedPlatform = 'Windows';
      } else if (Platform.isLinux) {
        _cachedPlatform = 'Linux';
      } else {
        _cachedPlatform = 'Unknown';
      }
    } catch (e) {
      _cachedPlatform = 'Platform Unknown';
      AppLogger.warning('Failed to get platform info', error: e);
    }

    return _cachedPlatform!;
  }

  /// Capture error with additional context for specific scenarios
  static Future<void> captureAuthError(Object error, StackTrace? stackTrace) async {
    await captureError(error, stackTrace, context: {'category': 'authentication'});
  }

  static Future<void> captureDatabaseError(Object error, StackTrace? stackTrace, {String? operation}) async {
    await captureError(error, stackTrace, context: {
      'category': 'database',
      'operation': operation,
    });
  }

  static Future<void> captureNetworkError(Object error, StackTrace? stackTrace, {String? endpoint}) async {
    await captureError(error, stackTrace, context: {
      'category': 'network',
      'endpoint': endpoint,
    });
  }

  static Future<void> captureUIError(Object error, StackTrace? stackTrace, {String? widget}) async {
    await captureError(error, stackTrace, context: {
      'category': 'ui',
      'widget': widget,
    });
  }

  static Future<void> captureBlocError(Object error, StackTrace? stackTrace, {String? bloc, String? event}) async {
    await captureError(error, stackTrace, context: {
      'category': 'bloc',
      'bloc': bloc,
      'event': event,
    });
  }

  /// Clear cached device info (useful for testing)
  static void clearCache() {
    _cachedDeviceInfo = null;
    _cachedPlatform = null;
  }
}

import 'package:flutter/material.dart';

/// Application color palette following MedyTrack brand guidelines
/// Mirrors the web app's color system exactly
class AppColors {
  // Primary Brand Colors (matching web app CSS variables)
  static const Color navy = Color(0xFF2D4A8E); // --navy: 220 45% 36%
  static const Color teal = Color(0xFF0DCDB7); // --teal: 174 100% 29%

  // Navy Variations
  static const Color navyLight = Color(0xFF4A6BB8);
  static const Color navyDark = Color(0xFF1E3366);
  static const Color navyExtraLight = Color(0xFF6B8DD6);

  // Teal Variations
  static const Color tealLight = Color(0xFF3DDDC7);
  static const Color tealDark = Color(0xFF0AA896);
  static const Color tealExtraLight = Color(0xFF6EEDD7);

  // Neutral Colors (matching web app)
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);

  // Gray Scale (matching Tailwind CSS)
  static const Color grey50 = Color(0xFFF9FAFB);
  static const Color grey100 = Color(0xFFF3F4F6);
  static const Color grey200 = Color(0xFFE5E7EB);
  static const Color grey300 = Color(0xFFD1D5DB);
  static const Color grey400 = Color(0xFF9CA3AF);
  static const Color grey500 = Color(0xFF6B7280);
  static const Color grey600 = Color(0xFF4B5563);
  static const Color grey700 = Color(0xFF374151);
  static const Color grey800 = Color(0xFF1F2937);
  static const Color grey900 = Color(0xFF111827);

  // Semantic Colors
  static const Color success = Color(0xFF10B981);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);
  static const Color errorLight = Color(0xFFFED7D7);
  static const Color info = Color(0xFF3B82F6);
  static const Color blue = Color(0xFF3B82F6);
  static const Color purple = Color(0xFF8B5CF6);
  static const Color orange = Color(0xFFFF8C00);

  // Medicine Status Colors (matching web app)
  static const Color expired = Color(0xFFEF4444); // Red
  static const Color expiringSoon = Color(0xFF3B82F6); // Blue (as seen in UI)
  static const Color lowStock = Color(0xFFF59E0B); // Orange (as seen in UI)
  static const Color adequate = Color(0xFF10B981); // Green
  static const Color outOfStock = Color(0xFF6B7280); // Gray

  // Therapeutic Class Colors (matching web app)
  static const Color antibiotique = Color(0xFFEF4444); // Red
  static const Color antalgique = Color(0xFF3B82F6); // Blue
  static const Color antiInflammatoire = Color(0xFF8B5CF6); // Purple
  static const Color cardiovasculaire = Color(0xFFEC4899); // Pink
  static const Color digestif = Color(0xFF10B981); // Green
  static const Color respiratoire = Color(0xFF06B6D4); // Cyan
  static const Color neurologique = Color(0xFF8B5CF6); // Purple
  static const Color endocrinien = Color(0xFFF59E0B); // Orange
  static const Color dermatologique = Color(0xFFEC4899); // Pink
  static const Color ophtalmologique = Color(0xFF06B6D4); // Cyan
  static const Color gynecologie = Color(0xFFED64A6); // Pink
  static const Color dermatologie = Color(0xFFF6AD55); // Light Orange

  // Web App Specific Colors - Matching Tailwind config
  static const Color mintLight = Color(0xFFE0F5E9);
  static const Color mint = Color(0xFFA8E0C0);
  static const Color mintDark = Color(0xFF5DC288);

  static const Color skyLight = Color(0xFFE1F5FE);
  static const Color sky = Color(0xFF81D4FA);
  static const Color skyDark = Color(0xFF4FC3F7);

  static const Color peachLight = Color(0xFFFFE0D5);
  static const Color peach = Color(0xFFFFBC9E);
  static const Color peachDark = Color(0xFFFF9B69);

  // Background Colors - Matching web app CSS variables
  static const Color background = Color(0xFFF0F9FF); // hsl(180 30% 96%)
  static const Color backgroundLight = Color(0xFFFAFAFA);
  static const Color backgroundDark = Color(0xFF121212);
  static const Color surfaceLight = Color(0xFFFFFFFF);
  static const Color surfaceDark = Color(0xFF1E1E1E);

  // Card Colors
  static const Color cardLight = Color(0xFFFFFFFF);
  static const Color cardDark = Color(0xFF2D2D2D);

  // Border Colors - Matching web app
  static const Color border = Color(0xFFE2E8F0); // hsl(214.3 31.8% 91.4%)
  static const Color borderLight = Color(0xFFE0E0E0);
  static const Color borderDark = Color(0xFF404040);

  // Muted Colors - Matching web app
  static const Color muted = Color(0xFFF1F5F9); // hsl(210 40% 96.1%)
  static const Color mutedForeground =
      Color(0xFF64748B); // hsl(215.4 16.3% 46.9%)

  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowDark = Color(0x3A000000);

  // Overlay Colors
  static const Color overlayLight = Color(0x80FFFFFF);
  static const Color overlayDark = Color(0x80000000);

  // Gradient Colors (matching web app header gradients)
  static const LinearGradient navyGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [navy, navyDark],
  );

  static const LinearGradient tealGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [teal, tealDark],
  );

  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [navy, teal],
  );

  // Light gradient for headers (matching web app's header-gradient-light)
  static const LinearGradient lightGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFF8FAFC), // Light gray-blue
      Color(0xFFE2E8F0), // Slightly darker gray-blue
    ],
  );

  /// Get medicine status color based on status string
  static Color getMedicineStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'expired':
        return expired;
      case 'expiring_soon':
        return expiringSoon;
      case 'low_stock':
        return lowStock;
      case 'adequate':
        return adequate;
      case 'out_of_stock':
        return outOfStock;
      default:
        return grey500;
    }
  }

  /// Get therapeutic class color based on class name
  static Color getTherapeuticClassColor(String className) {
    switch (className.toLowerCase()) {
      case 'antibiotique':
        return antibiotique;
      case 'antalgique':
        return antalgique;
      case 'anti-inflammatoire':
        return antiInflammatoire;
      case 'cardiovasculaire':
        return cardiovasculaire;
      case 'digestif':
        return digestif;
      case 'respiratoire':
        return respiratoire;
      case 'neurologique':
        return neurologique;
      case 'endocrinien':
        return endocrinien;
      case 'dermatologique':
        return dermatologique;
      case 'ophtalmologique':
        return ophtalmologique;
      case 'gynécologie':
        return gynecologie;
      default:
        return grey500;
    }
  }

  /// Utility methods for color manipulation
  static Color lighten(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslLight =
        hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }

  static Color darken(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }
}

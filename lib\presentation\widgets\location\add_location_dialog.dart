import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/location.dart';
import '../../bloc/location/location_bloc.dart';
import '../../bloc/location/location_event.dart';
import '../../bloc/location/location_state.dart';

class AddLocationDialog extends StatefulWidget {
  final String householdId;
  final Location? location; // For editing

  const AddLocationDialog({
    super.key,
    required this.householdId,
    this.location,
  });

  @override
  State<AddLocationDialog> createState() => _AddLocationDialogState();
}

class _AddLocationDialogState extends State<AddLocationDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  String _selectedIcon = 'location_on';

  bool get _isEditing => widget.location != null;

  // Predefined icons for locations
  static const List<Map<String, dynamic>> _availableIcons = [
    {'icon': 'medical_services', 'name': 'Pharmacie'},
    {'icon': 'kitchen', 'name': 'Cuisine'},
    {'icon': 'bed', 'name': 'Chambre'},
    {'icon': 'bathtub', 'name': 'Salle de bain'},
    {'icon': 'restaurant', 'name': 'Salle à manger'},
    {'icon': 'home', 'name': 'Maison'},
    {'icon': 'work', 'name': 'Bureau'},
    {'icon': 'local_pharmacy', 'name': 'Pharmacie locale'},
    {'icon': 'storage', 'name': 'Stockage'},
    {'icon': 'inventory', 'name': 'Inventaire'},
    {'icon': 'folder', 'name': 'Dossier'},
    {'icon': 'archive', 'name': 'Archive'},
  ];

  @override
  void initState() {
    super.initState();

    if (_isEditing) {
      _nameController.text = widget.location!.name;
      _descriptionController.text = widget.location!.description ?? '';
      _selectedIcon = widget.location!.icon;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<LocationBloc, LocationState>(
      listener: (context, state) {
        if (state is LocationOperationSuccess) {
          Navigator.of(context).pop();
        } else if (state is LocationError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppColors.error,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
      child: AlertDialog(
        title:
            Text(_isEditing ? 'Modifier l\'emplacement' : 'Nouvel emplacement'),
        content: SizedBox(
          width: MediaQuery.of(context).size.width * 0.9,
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name field
                  TextFormField(
                    controller: _nameController,
                    decoration: const InputDecoration(
                      labelText: 'Nom de l\'emplacement *',
                      hintText: 'Ex: Armoire à pharmacie',
                      prefixIcon: Icon(Icons.label),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Le nom est requis';
                      }
                      if (value.trim().length < 2) {
                        return 'Le nom doit contenir au moins 2 caractères';
                      }
                      if (value.trim().length > 50) {
                        return 'Le nom ne peut pas dépasser 50 caractères';
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 16),

                  // Description field
                  TextFormField(
                    controller: _descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description (optionnel)',
                      hintText: 'Description de l\'emplacement',
                      prefixIcon: Icon(Icons.description),
                    ),
                    maxLines: 2,
                    validator: (value) {
                      if (value != null && value.length > 200) {
                        return 'La description ne peut pas dépasser 200 caractères';
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 24),

                  // Icon selection
                  Text(
                    'Icône',
                    style: AppTextStyles.titleSmall.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    height: 120,
                    child: GridView.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 4,
                        childAspectRatio: 1,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                      ),
                      itemCount: _availableIcons.length,
                      itemBuilder: (context, index) {
                        final iconData = _availableIcons[index];
                        final isSelected = _selectedIcon == iconData['icon'];

                        return InkWell(
                          onTap: () {
                            setState(() {
                              _selectedIcon = iconData['icon'];
                            });
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? AppColors.teal.withValues(alpha: 0.1)
                                  : Colors.transparent,
                              border: Border.all(
                                color: isSelected
                                    ? AppColors.teal
                                    : AppColors.grey300,
                                width: isSelected ? 2 : 1,
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  _getIconData(iconData['icon']),
                                  color: isSelected
                                      ? AppColors.teal
                                      : AppColors.grey600,
                                  size: 24,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  iconData['name'],
                                  style: AppTextStyles.labelSmall.copyWith(
                                    color: isSelected
                                        ? AppColors.teal
                                        : AppColors.grey600,
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: _saveLocation,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.teal,
              foregroundColor: Colors.white,
            ),
            child: Text(_isEditing ? 'Modifier' : 'Créer'),
          ),
        ],
      ),
    );
  }

  void _saveLocation() {
    if (_formKey.currentState!.validate()) {
      final location = Location(
        id: _isEditing ? widget.location!.id : '',
        householdId: widget.householdId,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        icon: _selectedIcon,
        createdAt: _isEditing ? widget.location!.createdAt : DateTime.now(),
        updatedAt: _isEditing ? DateTime.now() : null,
      );

      if (_isEditing) {
        context.read<LocationBloc>().add(LocationUpdated(location: location));
      } else {
        context.read<LocationBloc>().add(LocationCreated(location: location));
      }
    }
  }

  IconData _getIconData(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'medical_services':
        return Icons.medical_services;
      case 'kitchen':
        return Icons.kitchen;
      case 'bed':
        return Icons.bed;
      case 'restaurant':
        return Icons.restaurant;
      case 'bathtub':
        return Icons.bathtub;
      case 'home':
        return Icons.home;
      case 'work':
        return Icons.work;
      case 'local_pharmacy':
        return Icons.local_pharmacy;
      case 'storage':
        return Icons.storage;
      case 'inventory':
        return Icons.inventory;
      case 'folder':
        return Icons.folder;
      case 'archive':
        return Icons.archive;
      default:
        return Icons.location_on;
    }
  }
}

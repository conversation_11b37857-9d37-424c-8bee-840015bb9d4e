import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';

/// Base list card component that provides consistent styling for all list items
/// Features:
/// - Fixed height: 72dp
/// - Border radius: 16dp
/// - Padding: 16dp horizontal, 12dp vertical
/// - Material 3 design principles
/// - Consistent elevation and border styling
class BaseListCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool isSelected;
  final bool showBorder;
  final Color? backgroundColor;
  final Color? borderColor; // Status-based border color
  final double? elevation;
  final EdgeInsetsGeometry? padding;
  final double? height;

  const BaseListCard({
    super.key,
    required this.child,
    this.onTap,
    this.onLongPress,
    this.isSelected = false,
    this.showBorder = false,
    this.backgroundColor,
    this.borderColor,
    this.elevation,
    this.padding,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveHeight = height ??
        104.0; // 104dp height to prevent overflow (increased from 96dp)
    final effectivePadding = padding ??
        const EdgeInsets.symmetric(
          horizontal: 16.0, // 16dp horizontal padding
          vertical: 12.0, // 12dp vertical padding (reduced for content fit)
        );
    final effectiveBackgroundColor = backgroundColor ?? AppColors.white;

    return Container(
      height: effectiveHeight,
      margin: const EdgeInsets.only(bottom: 12.0), // 12dp vertical margin
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(16.0), // 16dp corner radius
        border: Border.all(
          color: showBorder
              ? (isSelected ? AppColors.teal : AppColors.border)
              : (borderColor ?? AppColors.border.withValues(alpha: 0.3)),
          width: showBorder && isSelected ? 2.0 : 1.0,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          borderRadius: BorderRadius.circular(16.0),
          child: Container(
            padding: effectivePadding,
            child: child,
          ),
        ),
      ),
    );
  }
}

/// Enhanced base list card with additional features for complex layouts
class EnhancedBaseListCard extends StatelessWidget {
  final Widget leading;
  final Widget title;
  final Widget? subtitle;
  final Widget? trailing;
  final List<Widget>? actions;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool isSelected;
  final bool showBorder;
  final Color? backgroundColor;
  final Color? borderColor; // Status-based border color
  final double? elevation;

  const EnhancedBaseListCard({
    super.key,
    required this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    this.actions,
    this.onTap,
    this.onLongPress,
    this.isSelected = false,
    this.showBorder = false,
    this.backgroundColor,
    this.borderColor,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    return BaseListCard(
      onTap: onTap,
      onLongPress: onLongPress,
      isSelected: isSelected,
      showBorder: showBorder,
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      elevation: elevation,
      child: Row(
        children: [
          // Leading widget (icon, avatar, etc.)
          leading,

          const SizedBox(width: 12.0), // 12dp spacing between icon and text

          // Main content area
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min, // Minimize column size
              children: [
                Flexible(
                  child: title,
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 2.0), // Reduced spacing to fit content
                  Flexible(
                    child: subtitle!,
                  ),
                ],
              ],
            ),
          ),

          // Trailing content (status badges, actions, etc.)
          if (trailing != null) ...[
            const SizedBox(width: 8.0), // 8dp spacing before trailing
            trailing!,
          ],

          // Action buttons
          if (actions != null && actions!.isNotEmpty) ...[
            const SizedBox(width: 8.0), // 8dp spacing before actions
            Row(
              mainAxisSize: MainAxisSize.min,
              children: actions!,
            ),
          ],
        ],
      ),
    );
  }
}

/// Utility class for creating common leading widgets
class ListCardLeading {
  /// Create a circular icon container
  static Widget icon({
    required IconData icon,
    required Color color,
    double size = 48.0,
    double iconSize = 24.0,
    double borderRadius = 8.0,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Icon(
        icon,
        color: color,
        size: iconSize,
      ),
    );
  }

  /// Create a circular avatar container
  static Widget avatar({
    required IconData icon,
    required Color color,
    double size = 56.0,
    double iconSize = 28.0,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(size / 2),
      ),
      child: Icon(
        icon,
        color: color,
        size: iconSize,
      ),
    );
  }

  /// Create an image avatar (for future use)
  static Widget imageAvatar({
    required String imageUrl,
    double size = 56.0,
    IconData fallbackIcon = Icons.person,
    Color fallbackColor = AppColors.teal,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: fallbackColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(size / 2),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(size / 2),
        child: Image.network(
          imageUrl,
          width: size,
          height: size,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Icon(
              fallbackIcon,
              color: fallbackColor,
              size: size * 0.5,
            );
          },
        ),
      ),
    );
  }
}

/// Utility class for creating common trailing widgets
class ListCardTrailing {
  /// Create a simple arrow icon
  static Widget arrow({
    Color color = AppColors.grey500,
    double size = 16.0,
  }) {
    return Icon(
      Icons.chevron_right,
      color: color,
      size: size,
    );
  }

  /// Create a pill-shaped more options menu button
  static Widget moreOptions({
    required List<PopupMenuEntry<String>> items,
    required Function(String) onSelected,
    Color color = AppColors.grey600,
    double size = 20.0,
  }) {
    return PopupMenuButton<String>(
      onSelected: onSelected,
      itemBuilder: (context) => items,
      offset: const Offset(0, 40), // Position menu below the button
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20), // Pill shape
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.more_horiz,
              color: color,
              size: size,
            ),
          ],
        ),
      ),
    );
  }
}

/// Utility widget for creating properly spaced lists with enhanced cards
class EnhancedListView extends StatelessWidget {
  final List<Widget> children;
  final EdgeInsetsGeometry? padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;

  const EnhancedListView({
    super.key,
    required this.children,
    this.padding,
    this.physics,
    this.shrinkWrap = false,
  });

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: padding ?? const EdgeInsets.all(16.0),
      physics: physics,
      shrinkWrap: shrinkWrap,
      children: children,
    );
  }

  /// Create a list view with proper spacing for enhanced cards
  static Widget builder({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    EdgeInsetsGeometry? padding,
    ScrollPhysics? physics,
    bool shrinkWrap = false,
  }) {
    return ListView.builder(
      padding: padding ?? const EdgeInsets.all(16.0),
      physics: physics,
      shrinkWrap: shrinkWrap,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
    );
  }

  /// Create a separated list view with custom separators
  static Widget separated({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    Widget Function(BuildContext, int)? separatorBuilder,
    EdgeInsetsGeometry? padding,
    ScrollPhysics? physics,
    bool shrinkWrap = false,
  }) {
    return ListView.separated(
      padding: padding ?? const EdgeInsets.all(16.0),
      physics: physics,
      shrinkWrap: shrinkWrap,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      separatorBuilder:
          separatorBuilder ?? (context, index) => const SizedBox.shrink(),
    );
  }
}

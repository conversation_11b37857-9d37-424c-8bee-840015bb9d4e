# Architecture Documentation

MedyTrack Mobile follows Clean Architecture principles with the BLoC pattern for state management, ensuring maintainable, testable, and scalable code.

## 🏗️ Architecture Overview

### Clean Architecture Layers

```
┌─────────────────────────────────────────────────────────┐
│                    Presentation Layer                    │
│  ┌─────────────────┐  ┌─────────────────┐              │
│  │     Pages       │  │     Widgets     │              │
│  │   (Screens)     │  │  (Components)   │              │
│  └─────────────────┘  └─────────────────┘              │
│  ┌─────────────────────────────────────────────────────┐ │
│  │                BLoC (State Management)              │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────┐
│                    Domain Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐              │
│  │    Entities     │  │   Use Cases     │              │
│  │  (Models)       │  │ (Business Logic)│              │
│  └─────────────────┘  └─────────────────┘              │
│  ┌─────────────────────────────────────────────────────┐ │
│  │            Repository Interfaces                    │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────┐
│                     Data Layer                          │
│  ┌─────────────────┐  ┌─────────────────┐              │
│  │  Data Sources   │  │     Models      │              │
│  │ (API, Local DB) │  │     (DTOs)      │              │
│  └─────────────────┘  └─────────────────┘              │
│  ┌─────────────────────────────────────────────────────┐ │
│  │           Repository Implementations                │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 📁 Project Structure

```
lib/
├── core/                           # Core utilities and configurations
│   ├── config/                     # App configuration
│   │   ├── app_config.dart         # Environment variables
│   │   └── constants.dart          # App constants
│   ├── di/                         # Dependency injection
│   │   ├── injection.dart          # GetIt setup
│   │   └── injection.config.dart   # Generated DI config
│   ├── error/                      # Error handling
│   │   ├── exceptions.dart         # Custom exceptions
│   │   └── failures.dart           # Failure classes
│   ├── router/                     # Navigation
│   │   └── app_router.dart         # GoRouter configuration
│   ├── theme/                      # UI theming
│   │   ├── app_colors.dart         # Color palette
│   │   ├── app_text_styles.dart    # Typography
│   │   └── app_theme.dart          # Theme configuration
│   └── utils/                      # Utility functions
│       ├── date_utils.dart         # Date formatting
│       └── validators.dart         # Input validation
├── data/                           # Data layer
│   ├── datasources/                # Data sources
│   │   ├── local/                  # Local storage
│   │   │   └── shared_prefs_service.dart
│   │   └── remote/                 # API services
│   │       ├── auth_service.dart   # Authentication API
│   │       ├── medicine_service.dart # Medicine API
│   │       └── supabase_client.dart # Supabase client
│   ├── models/                     # Data models (DTOs)
│   │   ├── medicine_model.dart     # Medicine data model
│   │   ├── user_model.dart         # User data model
│   │   └── household_model.dart    # Household data model
│   └── repositories/               # Repository implementations
│       ├── auth_repository_impl.dart
│       ├── medicine_repository_impl.dart
│       └── dashboard_repository_impl.dart
├── domain/                         # Business logic layer
│   ├── entities/                   # Domain entities
│   │   ├── medicine.dart           # Medicine entity
│   │   ├── user.dart               # User entity
│   │   └── household.dart          # Household entity
│   ├── repositories/               # Repository interfaces
│   │   ├── auth_repository.dart    # Auth contract
│   │   ├── medicine_repository.dart # Medicine contract
│   │   └── dashboard_repository.dart # Dashboard contract
│   └── usecases/                   # Business use cases
│       ├── auth/                   # Authentication use cases
│       ├── medicine/               # Medicine use cases
│       └── dashboard/              # Dashboard use cases
└── presentation/                   # UI layer
    ├── bloc/                       # State management
    │   ├── auth/                   # Authentication BLoC
    │   ├── medicine/               # Medicine BLoC
    │   └── dashboard/              # Dashboard BLoC
    ├── pages/                      # Screen widgets
    │   ├── auth/                   # Authentication screens
    │   ├── dashboard/              # Dashboard screen
    │   ├── medicine/               # Medicine screens
    │   ├── profile/                # Profile screen
    │   └── settings/               # Settings screen
    └── widgets/                    # Reusable UI components
        ├── common/                 # Common widgets
        ├── dashboard/              # Dashboard-specific widgets
        └── medicine/               # Medicine-specific widgets
```

## 🔄 State Management (BLoC Pattern)

### BLoC Architecture

```
┌─────────────────┐    Events    ┌─────────────────┐
│       UI        │ ──────────► │      BLoC       │
│   (Widgets)     │              │  (Business      │
│                 │ ◄────────── │   Logic)        │
└─────────────────┘    States    └─────────────────┘
                                          │
                                          ▼
                                 ┌─────────────────┐
                                 │   Repository    │
                                 │  (Data Access)  │
                                 └─────────────────┘
```

### Example BLoC Implementation

```dart
// Events
abstract class MedicineEvent extends Equatable {
  const MedicineEvent();
}

class LoadMedicines extends MedicineEvent {
  @override
  List<Object> get props => [];
}

class AddMedicine extends MedicineEvent {
  final Medicine medicine;
  const AddMedicine(this.medicine);
  
  @override
  List<Object> get props => [medicine];
}

// States
abstract class MedicineState extends Equatable {
  const MedicineState();
}

class MedicineInitial extends MedicineState {
  @override
  List<Object> get props => [];
}

class MedicineLoading extends MedicineState {
  @override
  List<Object> get props => [];
}

class MedicineLoaded extends MedicineState {
  final List<Medicine> medicines;
  const MedicineLoaded({required this.medicines});
  
  @override
  List<Object> get props => [medicines];
}

class MedicineError extends MedicineState {
  final String message;
  const MedicineError({required this.message});
  
  @override
  List<Object> get props => [message];
}

// BLoC
class MedicineBloc extends Bloc<MedicineEvent, MedicineState> {
  final MedicineRepository _repository;
  
  MedicineBloc({required MedicineRepository repository})
      : _repository = repository,
        super(MedicineInitial()) {
    on<LoadMedicines>(_onLoadMedicines);
    on<AddMedicine>(_onAddMedicine);
  }
  
  Future<void> _onLoadMedicines(
    LoadMedicines event,
    Emitter<MedicineState> emit,
  ) async {
    emit(MedicineLoading());
    try {
      final medicines = await _repository.getMedicines();
      emit(MedicineLoaded(medicines: medicines));
    } catch (e) {
      emit(MedicineError(message: e.toString()));
    }
  }
  
  Future<void> _onAddMedicine(
    AddMedicine event,
    Emitter<MedicineState> emit,
  ) async {
    try {
      await _repository.addMedicine(event.medicine);
      add(LoadMedicines()); // Reload medicines
    } catch (e) {
      emit(MedicineError(message: e.toString()));
    }
  }
}
```

## 🔌 Dependency Injection

### GetIt Configuration

```dart
@InjectableInit()
void configureDependencies() => getIt.init();

final getIt = GetIt.instance;

// Registration
@module
abstract class RegisterModule {
  @singleton
  SupabaseClient get supabaseClient => Supabase.instance.client;
  
  @singleton
  SharedPreferences get sharedPreferences => 
      SharedPreferences.getInstance();
}

// Service Registration
@Injectable()
class MedicineService {
  final SupabaseClient _client;
  MedicineService(this._client);
}

@Injectable(as: MedicineRepository)
class MedicineRepositoryImpl implements MedicineRepository {
  final MedicineService _service;
  MedicineRepositoryImpl(this._service);
}

@Injectable()
class MedicineBloc {
  final MedicineRepository _repository;
  MedicineBloc(this._repository);
}
```

## 🌐 Data Flow

### Complete Data Flow Example

```
1. User Action (UI)
   │
   ▼
2. Event Dispatched (BLoC)
   │
   ▼
3. Use Case Execution (Domain)
   │
   ▼
4. Repository Call (Domain Interface)
   │
   ▼
5. Data Source Access (Data Implementation)
   │
   ▼
6. API/Database Call (External)
   │
   ▼
7. Data Transformation (Models → Entities)
   │
   ▼
8. State Emission (BLoC)
   │
   ▼
9. UI Update (Widget Rebuild)
```

## 🔐 Error Handling

### Exception Hierarchy

```dart
abstract class AppException implements Exception {
  final String message;
  const AppException(this.message);
}

class NetworkException extends AppException {
  const NetworkException(String message) : super(message);
}

class AuthException extends AppException {
  const AuthException(String message) : super(message);
}

class ValidationException extends AppException {
  const ValidationException(String message) : super(message);
}

// Failure classes for use cases
abstract class Failure extends Equatable {
  final String message;
  const Failure(this.message);
}

class NetworkFailure extends Failure {
  const NetworkFailure(String message) : super(message);
  
  @override
  List<Object> get props => [message];
}
```

## 🧪 Testing Strategy

### Test Pyramid

```
┌─────────────────────────────────────┐
│           E2E Tests                 │  ← Few, Slow, High Confidence
├─────────────────────────────────────┤
│         Integration Tests           │  ← Some, Medium Speed
├─────────────────────────────────────┤
│           Widget Tests              │  ← More, Faster
├─────────────────────────────────────┤
│            Unit Tests               │  ← Many, Fast, Low Level
└─────────────────────────────────────┘
```

### Test Structure

```
test/
├── unit/                           # Unit tests
│   ├── domain/                     # Domain layer tests
│   │   ├── entities/               # Entity tests
│   │   └── usecases/               # Use case tests
│   ├── data/                       # Data layer tests
│   │   ├── models/                 # Model tests
│   │   ├── repositories/           # Repository tests
│   │   └── datasources/            # Data source tests
│   └── presentation/               # Presentation tests
│       └── bloc/                   # BLoC tests
├── widget/                         # Widget tests
│   ├── pages/                      # Page widget tests
│   └── widgets/                    # Component tests
├── integration/                    # Integration tests
│   ├── auth_flow_test.dart         # Authentication flow
│   └── medicine_crud_test.dart     # Medicine CRUD operations
└── helpers/                        # Test utilities
    ├── test_data.dart              # Mock data
    └── mock_dependencies.dart      # Mock services
```

## 🚀 Performance Considerations

### Optimization Strategies

1. **Lazy Loading**: Load data only when needed
2. **Caching**: Cache frequently accessed data
3. **Pagination**: Load large datasets in chunks
4. **Image Optimization**: Compress and cache images
5. **State Management**: Minimize unnecessary rebuilds
6. **Database Indexing**: Optimize query performance

### Memory Management

```dart
// Proper disposal in BLoCs
@override
Future<void> close() {
  _subscription?.cancel();
  return super.close();
}

// Efficient list rendering
ListView.builder(
  itemCount: medicines.length,
  itemBuilder: (context, index) {
    return MedicineCard(medicine: medicines[index]);
  },
)
```

## 🔗 External Integrations

### Supabase Integration

- **Authentication**: User management and sessions
- **Database**: Real-time PostgreSQL database
- **Storage**: File and image storage
- **Real-time**: Live data synchronization

### Third-party Services

- **Analytics**: Firebase Analytics (planned)
- **Crash Reporting**: Firebase Crashlytics (planned)
- **Push Notifications**: Firebase Messaging (planned)
- **Barcode Scanning**: ML Kit (planned)

---

This architecture ensures:
- **Separation of Concerns**: Each layer has a specific responsibility
- **Testability**: Easy to unit test business logic
- **Maintainability**: Changes in one layer don't affect others
- **Scalability**: Easy to add new features and modify existing ones
- **Code Reusability**: Shared components and utilities

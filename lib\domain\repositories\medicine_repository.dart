import 'package:dartz/dartz.dart';
import '../entities/medicine.dart';
import '../../core/error/failures.dart';

abstract class MedicineRepository {
  Future<Either<Failure, List<Medicine>>> getMedicines(String householdId);
  Future<Either<Failure, Medicine>> getMedicineById(String medicineId);
  Future<Either<Failure, Medicine>> addMedicine(Medicine medicine);
  Future<Either<Failure, void>> deleteMedicine(String medicineId);
  Future<Either<Failure, Medicine>> updateMedicine(Medicine medicine);
  Future<Either<Failure, List<Medicine>>> searchMedicines(
      String householdId, String query);
  Future<Either<Failure, List<Medicine>>> getRecentMedicines(String householdId,
      {int limit = 5});
}

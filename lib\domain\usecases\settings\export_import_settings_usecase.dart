import 'package:dartz/dartz.dart';
import '../../entities/settings.dart';
import '../../repositories/settings_repository.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';

/// Use case for exporting user settings
class ExportSettingsUseCase implements UseCase<String, String> {
  final SettingsRepository repository;

  ExportSettingsUseCase(this.repository);

  @override
  Future<Either<Failure, String>> call(String userId) async {
    return await repository.exportSettings(userId);
  }
}

/// Use case for importing user settings
class ImportSettingsUseCase implements UseCase<Settings, ImportSettingsParams> {
  final SettingsRepository repository;

  ImportSettingsUseCase(this.repository);

  @override
  Future<Either<Failure, Settings>> call(ImportSettingsParams params) async {
    return await repository.importSettings(params.userId, params.jsonData);
  }
}

/// Parameters for importing settings
class ImportSettingsParams {
  final String userId;
  final String jsonData;

  ImportSettingsParams({
    required this.userId,
    required this.jsonData,
  });
}

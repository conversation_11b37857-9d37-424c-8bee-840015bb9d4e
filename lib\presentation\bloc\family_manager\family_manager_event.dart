import 'package:equatable/equatable.dart';

import '../../../domain/entities/family_member.dart';

/// Base class for family manager events
abstract class FamilyManagerEvent extends Equatable {
  const FamilyManagerEvent();

  @override
  List<Object?> get props => [];
}

/// Initialize family members for a household
class FamilyManagerInitialized extends FamilyManagerEvent {
  final String householdId;

  const FamilyManagerInitialized({required this.householdId});

  @override
  List<Object?> get props => [householdId];
}

/// Refresh family members
class FamilyMembersRefreshed extends FamilyManagerEvent {
  const FamilyMembersRefreshed();
}

/// Create a new family member
class FamilyMemberCreated extends FamilyManagerEvent {
  final FamilyMember member;

  const FamilyMemberCreated({required this.member});

  @override
  List<Object?> get props => [member];
}

/// Update an existing family member
class FamilyMemberUpdated extends FamilyManagerEvent {
  final FamilyMember member;

  const FamilyMemberUpdated({required this.member});

  @override
  List<Object?> get props => [member];
}

/// Delete a family member
class FamilyMemberDeleted extends FamilyManagerEvent {
  final String memberId;

  const FamilyMemberDeleted({required this.memberId});

  @override
  List<Object?> get props => [memberId];
}

/// Search family members
class FamilyMembersSearched extends FamilyManagerEvent {
  final String query;

  const FamilyMembersSearched({required this.query});

  @override
  List<Object?> get props => [query];
}

/// Clear search
class FamilyMemberSearchCleared extends FamilyManagerEvent {
  const FamilyMemberSearchCleared();
}

/// Filter family members by relationship
class FamilyMembersFiltered extends FamilyManagerEvent {
  final String? relationship;

  const FamilyMembersFiltered({this.relationship});

  @override
  List<Object?> get props => [relationship];
}

/// Sort family members
class FamilyMembersSorted extends FamilyManagerEvent {
  final FamilyMemberSortOption sortOption;

  const FamilyMembersSorted({required this.sortOption});

  @override
  List<Object?> get props => [sortOption];
}

/// Select a family member for editing
class FamilyMemberSelected extends FamilyManagerEvent {
  final FamilyMember? member;

  const FamilyMemberSelected({this.member});

  @override
  List<Object?> get props => [member];
}

/// Clear selected family member
class FamilyMemberSelectionCleared extends FamilyManagerEvent {
  const FamilyMemberSelectionCleared();
}

/// Validate family member form
class FamilyMemberFormValidated extends FamilyManagerEvent {
  final String name;
  final String? relationship;
  final DateTime? dateOfBirth;
  final String? gender;
  final String? notes;

  const FamilyMemberFormValidated({
    required this.name,
    this.relationship,
    this.dateOfBirth,
    this.gender,
    this.notes,
  });

  @override
  List<Object?> get props => [name, relationship, dateOfBirth, gender, notes];
}

/// Reset family member form
class FamilyMemberFormReset extends FamilyManagerEvent {
  const FamilyMemberFormReset();
}

/// Sort options for family members
enum FamilyMemberSortOption {
  nameAsc,
  nameDesc,
  ageAsc,
  ageDesc,
  relationshipAsc,
  relationshipDesc,
  createdAtAsc,
  createdAtDesc,
}

/// Extension for sort option display names
extension FamilyMemberSortOptionExtension on FamilyMemberSortOption {
  String get displayName {
    switch (this) {
      case FamilyMemberSortOption.nameAsc:
        return 'Nom (A-Z)';
      case FamilyMemberSortOption.nameDesc:
        return 'Nom (Z-A)';
      case FamilyMemberSortOption.ageAsc:
        return 'Âge (croissant)';
      case FamilyMemberSortOption.ageDesc:
        return 'Âge (décroissant)';
      case FamilyMemberSortOption.relationshipAsc:
        return 'Relation (A-Z)';
      case FamilyMemberSortOption.relationshipDesc:
        return 'Relation (Z-A)';
      case FamilyMemberSortOption.createdAtAsc:
        return 'Ajouté (plus ancien)';
      case FamilyMemberSortOption.createdAtDesc:
        return 'Ajouté (plus récent)';
    }
  }

  String get icon {
    switch (this) {
      case FamilyMemberSortOption.nameAsc:
      case FamilyMemberSortOption.nameDesc:
        return 'sort_by_alpha';
      case FamilyMemberSortOption.ageAsc:
      case FamilyMemberSortOption.ageDesc:
        return 'cake';
      case FamilyMemberSortOption.relationshipAsc:
      case FamilyMemberSortOption.relationshipDesc:
        return 'family_restroom';
      case FamilyMemberSortOption.createdAtAsc:
      case FamilyMemberSortOption.createdAtDesc:
        return 'access_time';
    }
  }

  bool get isAscending {
    switch (this) {
      case FamilyMemberSortOption.nameAsc:
      case FamilyMemberSortOption.ageAsc:
      case FamilyMemberSortOption.relationshipAsc:
      case FamilyMemberSortOption.createdAtAsc:
        return true;
      case FamilyMemberSortOption.nameDesc:
      case FamilyMemberSortOption.ageDesc:
      case FamilyMemberSortOption.relationshipDesc:
      case FamilyMemberSortOption.createdAtDesc:
        return false;
    }
  }
}

import '../entities/tag.dart';

/// Repository interface for tag management
abstract class TagRepository {
  /// Get all tags for a household
  /// 
  /// [householdId] - Household ID
  /// 
  /// Returns a Stream of tag lists that updates in real-time
  Stream<List<Tag>> getHouseholdTags(String householdId);
  
  /// Get tags by category
  /// 
  /// [householdId] - Household ID
  /// [category] - Tag category ('therapeutic' or 'usage')
  /// 
  /// Returns a Future with a list of tags in the specified category
  Future<List<Tag>> getTagsByCategory(String householdId, String category);
  
  /// Create a new tag
  /// 
  /// [tag] - Tag entity to create
  /// 
  /// Returns a Future with the created tag
  Future<Tag> createTag(Tag tag);
  
  /// Update an existing tag
  /// 
  /// [tag] - Tag entity with updated information
  /// 
  /// Returns a Future with the updated tag
  Future<Tag> updateTag(Tag tag);
  
  /// Delete a tag
  /// 
  /// [tagId] - Tag ID to delete
  /// 
  /// Returns a Future that completes when the tag is deleted
  Future<void> deleteTag(String tagId);
  
  /// Add tag to medicine (creates medicine_tags relationship)
  /// 
  /// [medicineId] - Medicine ID
  /// [tagId] - Tag ID
  /// 
  /// Returns a Future that completes when the relationship is created
  Future<void> addTagToMedicine(String medicineId, String tagId);
  
  /// Remove tag from medicine
  /// 
  /// [medicineId] - Medicine ID
  /// [tagId] - Tag ID
  /// 
  /// Returns a Future that completes when the relationship is removed
  Future<void> removeTagFromMedicine(String medicineId, String tagId);
  
  /// Get tags for a specific medicine
  /// 
  /// [medicineId] - Medicine ID
  /// 
  /// Returns a Future with a list of tags associated with the medicine
  Future<List<Tag>> getMedicineTags(String medicineId);
}

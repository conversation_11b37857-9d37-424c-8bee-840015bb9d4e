import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../bloc/my_medicines/my_medicines_event.dart';

class MedicineFilterBar extends StatelessWidget {
  final MedicineFilter currentFilter;
  final Function(MedicineFilter) onFilterChanged;

  const MedicineFilterBar({
    super.key,
    required this.currentFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        children: [
          _buildFilterChip(
            filter: MedicineFilter.all,
            label: 'Tous',
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            filter: MedicineFilter.expired,
            label: 'Expirés',
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            filter: MedicineFilter.expiringSoon,
            label: 'Bientôt expirés',
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            filter: MedicineFilter.lowStock,
            label: 'Stock faible',
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            filter: MedicineFilter.byLocation,
            label: 'Par lieu',
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            filter: MedicineFilter.byFamilyMember,
            label: 'Par membre',
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required MedicineFilter filter,
    required String label,
  }) {
    final isSelected = currentFilter == filter;

    Color backgroundColor;
    Color textColor;
    Color borderColor;

    if (isSelected) {
      backgroundColor = _getFilterColor(filter);
      textColor = Colors.white;
      borderColor = _getFilterColor(filter);
    } else {
      backgroundColor = _getFilterColor(filter).withValues(alpha: 0.1);
      textColor = _getFilterColor(filter);
      borderColor = _getFilterColor(filter).withValues(alpha: 0.3);
    }

    return FilterChip(
      selected: isSelected,
      onSelected: (selected) => onFilterChanged(filter),
      label: Text(
        label,
        style: AppTextStyles.labelMedium.copyWith(
          color: textColor,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
        ),
      ),
      backgroundColor: backgroundColor,
      selectedColor: backgroundColor,
      side: BorderSide(color: borderColor, width: 1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      visualDensity: VisualDensity.compact,
    );
  }

  Color _getFilterColor(MedicineFilter filter) {
    switch (filter) {
      case MedicineFilter.all:
        return AppColors.teal;
      case MedicineFilter.expired:
        return AppColors.error;
      case MedicineFilter.expiringSoon:
        return AppColors.warning;
      case MedicineFilter.lowStock:
        return AppColors.warning;
      case MedicineFilter.outOfStock:
        return AppColors.error;
      case MedicineFilter.custom:
        return AppColors.blue;
      case MedicineFilter.prescription:
        return AppColors.purple;
      case MedicineFilter.byLocation:
        return AppColors.navy;
      case MedicineFilter.byFamilyMember:
        return AppColors.teal;
    }
  }
}

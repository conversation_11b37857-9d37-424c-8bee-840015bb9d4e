import 'package:dartz/dartz.dart';
import 'package:medytrack_mobile_v2/core/error/failures.dart';
import 'package:medytrack_mobile_v2/domain/entities/reminder.dart';

abstract class ReminderRepository {
  Future<Either<Failure, List<Reminder>>> getRemindersForUserMedicine(
      String userMedicineId);
  Future<Either<Failure, List<Reminder>>> getRemindersForMultipleMedicines(
      List<String> userMedicineIds);
  Future<Either<Failure, Reminder>> addReminder(Reminder reminder);
  Future<Either<Failure, Reminder>> updateReminder(Reminder reminder);
  Future<Either<Failure, Unit>> deleteReminder(String reminderId);

  Future<Either<Failure, List<DoseHistory>>> getDoseHistoryForUserMedicine(
      String userMedicineId);
  Future<Either<Failure, Unit>> addDoseHistory(DoseHistory doseHistory);
}

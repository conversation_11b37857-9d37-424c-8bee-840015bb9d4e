import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:async';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_state.dart' as auth_state;
import '../../bloc/medicine/medicine_bloc.dart';
import '../../bloc/dashboard/dashboard_bloc.dart';
import '../../../core/utils/supabase_utils.dart';
import '../../../core/services/supabase_service.dart';
import '../../../core/di/injection_container.dart';

import '../../../data/repositories/location_repository_impl.dart';
import '../../../data/repositories/family_member_repository_impl.dart';
import '../../../data/models/medicine_model.dart';

/// Debug/Test page for manual testing of core functionality
class DebugTestPage extends StatefulWidget {
  const DebugTestPage({super.key});

  @override
  State<DebugTestPage> createState() => _DebugTestPageState();
}

class _DebugTestPageState extends State<DebugTestPage> {
  final List<String> _consoleOutput = [];
  final ScrollController _scrollController = ScrollController();

  // Test status tracking
  Map<String, TestStatus> testResults = {
    'supabase_connection': TestStatus.pending,
    'auth_state': TestStatus.pending,
    'household_id': TestStatus.pending,
    'medicine_crud': TestStatus.pending,
    'dashboard_data': TestStatus.pending,
    'location_management': TestStatus.pending,
    'family_management': TestStatus.pending,
  };

  @override
  void initState() {
    super.initState();
    _addToConsole('Debug Test Page initialized');
    _addToConsole('Ready for testing...');
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _addToConsole(String message) {
    setState(() {
      _consoleOutput
          .add('${DateTime.now().toString().substring(11, 19)}: $message');
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _updateTestStatus(String testKey, TestStatus status, [String? message]) {
    setState(() {
      testResults[testKey] = status;
    });
    if (message != null) {
      _addToConsole(message);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug & Test Page'),
        backgroundColor: Colors.orange,
        actions: [
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: () {
              setState(() {
                _consoleOutput.clear();
                testResults.updateAll((key, value) => TestStatus.pending);
              });
              _addToConsole('Console cleared, tests reset');
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Test Status Overview
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Test Status Overview',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: testResults.entries.map((entry) {
                    return _buildStatusChip(entry.key, entry.value);
                  }).toList(),
                ),
              ],
            ),
          ),

          // Test Buttons
          Expanded(
            flex: 2,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildTestButton(
                    'Test Supabase Connection',
                    Icons.cloud_done,
                    Colors.blue,
                    _testSupabaseConnection,
                  ),
                  const SizedBox(height: 8),
                  _buildTestButton(
                    'Test Authentication State',
                    Icons.person,
                    Colors.green,
                    _testAuthenticationState,
                  ),
                  const SizedBox(height: 8),
                  _buildTestButton(
                    'Test Household ID Validation',
                    Icons.home,
                    Colors.purple,
                    _testHouseholdIdValidation,
                  ),
                  const SizedBox(height: 8),
                  _buildTestButton(
                    'Test Medicine CRUD Operations',
                    Icons.medication,
                    Colors.red,
                    _testMedicineCrud,
                  ),
                  const SizedBox(height: 8),
                  _buildTestButton(
                    'Test Dashboard Data Loading',
                    Icons.dashboard,
                    Colors.orange,
                    _testDashboardData,
                  ),
                  const SizedBox(height: 8),
                  _buildTestButton(
                    'Test Location Management',
                    Icons.location_on,
                    Colors.teal,
                    _testLocationManagement,
                  ),
                  const SizedBox(height: 8),
                  _buildTestButton(
                    'Test Family Management',
                    Icons.family_restroom,
                    Colors.indigo,
                    _testFamilyManagement,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _runAllTests,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.deepPurple,
                      padding: const EdgeInsets.all(16),
                    ),
                    child: const Text(
                      'RUN ALL TESTS',
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Console Output
          Container(
            height: 200,
            decoration: BoxDecoration(
              color: Colors.black87,
              border: Border.all(color: Colors.grey),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  color: Colors.grey[800],
                  child: const Row(
                    children: [
                      Icon(Icons.terminal, color: Colors.white, size: 16),
                      SizedBox(width: 8),
                      Text(
                        'Console Output',
                        style: TextStyle(
                            color: Colors.white, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(8),
                    itemCount: _consoleOutput.length,
                    itemBuilder: (context, index) {
                      return Text(
                        _consoleOutput[index],
                        style: const TextStyle(
                          color: Colors.greenAccent,
                          fontFamily: 'monospace',
                          fontSize: 12,
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String testKey, TestStatus status) {
    Color color;
    IconData icon;

    switch (status) {
      case TestStatus.pending:
        color = Colors.grey;
        icon = Icons.pending;
        break;
      case TestStatus.running:
        color = Colors.orange;
        icon = Icons.refresh;
        break;
      case TestStatus.success:
        color = Colors.green;
        icon = Icons.check_circle;
        break;
      case TestStatus.failure:
        color = Colors.red;
        icon = Icons.error;
        break;
    }

    return Chip(
      avatar: Icon(icon, size: 16, color: Colors.white),
      label: Text(
        testKey.replaceAll('_', ' ').toUpperCase(),
        style: const TextStyle(color: Colors.white, fontSize: 10),
      ),
      backgroundColor: color,
    );
  }

  Widget _buildTestButton(
      String title, IconData icon, Color color, VoidCallback onPressed) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon),
      label: Text(title),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        padding: const EdgeInsets.all(12),
        alignment: Alignment.centerLeft,
      ),
    );
  }

  // Test Methods
  Future<void> _testSupabaseConnection() async {
    _updateTestStatus('supabase_connection', TestStatus.running,
        'Testing Supabase connection...');

    try {
      final client = Supabase.instance.client;
      _addToConsole('Supabase client initialized');

      // Test basic connection
      final response = await client.from('profiles').select('count').limit(1);
      _addToConsole('Connection test response: $response');

      _updateTestStatus('supabase_connection', TestStatus.success,
          'Supabase connection successful');
    } catch (e) {
      _updateTestStatus('supabase_connection', TestStatus.failure,
          'Supabase connection failed: $e');
    }
  }

  Future<void> _testAuthenticationState() async {
    _updateTestStatus(
        'auth_state', TestStatus.running, 'Testing authentication state...');

    try {
      final authState = context.read<AuthBloc>().state;
      _addToConsole('Auth state type: ${authState.runtimeType}');

      if (authState is auth_state.AuthAuthenticated) {
        _addToConsole('User ID: ${authState.user.id}');
        _addToConsole('User email: ${authState.user.email}');
        _addToConsole('User name: ${authState.user.name}');
        _addToConsole('Household ID: ${authState.householdId}');
        _addToConsole('Household name: ${authState.user.householdName}');

        _updateTestStatus(
            'auth_state', TestStatus.success, 'Authentication state valid');
      } else if (authState is auth_state.AuthUnauthenticated) {
        _updateTestStatus(
            'auth_state', TestStatus.failure, 'User not authenticated');
      } else {
        _updateTestStatus('auth_state', TestStatus.failure,
            'Unknown auth state: ${authState.runtimeType}');
      }
    } catch (e) {
      _updateTestStatus(
          'auth_state', TestStatus.failure, 'Auth state test failed: $e');
    }
  }

  Future<void> _testHouseholdIdValidation() async {
    _updateTestStatus('household_id', TestStatus.running,
        'Testing household ID validation...');

    try {
      final householdId = SupabaseUtils.getHouseholdId(context);
      _addToConsole('Retrieved household ID: $householdId');

      if (householdId == null) {
        _updateTestStatus(
            'household_id', TestStatus.failure, 'Household ID is null');
        return;
      }

      // Test UUID validation
      final isValidUUID = SupabaseUtils.isValidUUID(householdId);
      _addToConsole('Is valid UUID: $isValidUUID');

      // Test against known bad values
      final badValues = ['default-household', '', 'invalid-uuid', '123'];
      for (final badValue in badValues) {
        final isValid = SupabaseUtils.isValidUUID(badValue);
        _addToConsole('$badValue is valid: $isValid (should be false)');
        if (isValid) {
          _updateTestStatus('household_id', TestStatus.failure,
              'UUID validation failed for: $badValue');
          return;
        }
      }

      if (isValidUUID && householdId != 'default-household') {
        _updateTestStatus('household_id', TestStatus.success,
            'Household ID validation passed');
      } else {
        _updateTestStatus('household_id', TestStatus.failure,
            'Invalid household ID: $householdId');
      }
    } catch (e) {
      _updateTestStatus(
          'household_id', TestStatus.failure, 'Household ID test failed: $e');
    }
  }

  Future<void> _testMedicineCrud() async {
    _updateTestStatus('medicine_crud', TestStatus.running,
        'Testing medicine CRUD operations...');

    try {
      final householdId = SupabaseUtils.getHouseholdId(context);
      if (householdId == null) {
        _updateTestStatus('medicine_crud', TestStatus.failure,
            'No household ID for medicine test');
        return;
      }

      _addToConsole('Testing direct database query first...');

      // Test direct database access to understand the issue
      final client = Supabase.instance.client;

      // Test 1: Check if dashboard_medicine_alerts_view exists and has data
      try {
        _addToConsole('Testing dashboard_medicine_alerts_view...');
        final viewResponse = await client
            .from('dashboard_medicine_alerts_view')
            .select('*')
            .eq('household_id', householdId)
            .limit(5);
        _addToConsole('View query result: ${viewResponse.length} records');
        if (viewResponse.isNotEmpty) {
          _addToConsole(
              'Sample record keys: ${viewResponse.first.keys.toList()}');
          // Show a sample record to understand the data structure
          final sample = viewResponse.first;
          _addToConsole(
              'Sample medicine: ${sample['custom_name'] ?? sample['medicine_name'] ?? 'Unknown'}');
          _addToConsole('Sample household_id: ${sample['household_id']}');

          // Test MedicineModel.fromJson with sample data
          try {
            _addToConsole('Testing MedicineModel.fromJson with sample data...');
            final testMedicine = MedicineModel.fromJson(sample);
            _addToConsole(
                'MedicineModel creation successful: ${testMedicine.customName ?? testMedicine.medicineName}');
          } catch (e) {
            _addToConsole('MedicineModel creation failed: $e');
            // Show problematic fields
            _addToConsole('Checking for null fields in sample:');
            sample.forEach((key, value) {
              if (value == null) {
                _addToConsole('  - $key: null');
              }
            });
          }
        }
      } catch (e) {
        _addToConsole('View query failed: $e');
      }

      // Test 2: Check user_medicines table directly
      try {
        _addToConsole('Testing user_medicines table...');
        final tableResponse = await client
            .from('user_medicines')
            .select('*')
            .eq('household_id', householdId)
            .limit(5);
        _addToConsole('Table query result: ${tableResponse.length} records');
        if (tableResponse.isNotEmpty) {
          _addToConsole(
              'Sample record keys: ${tableResponse.first.keys.toList()}');
        }
      } catch (e) {
        _addToConsole('Table query failed: $e');
      }

      // Test loading medicines via BLoC
      _addToConsole('Testing medicine BLoC...');
      if (mounted) {
        context
            .read<MedicineBloc>()
            .add(MedicineLoadRequested(householdId: householdId));
        _addToConsole('Medicine load requested for household: $householdId');
      }

      // Wait a bit for the response
      await Future.delayed(const Duration(seconds: 3));

      if (mounted) {
        final medicineState = context.read<MedicineBloc>().state;
        _addToConsole('Medicine state: ${medicineState.runtimeType}');

        if (medicineState is MedicineLoaded) {
          _addToConsole('Found ${medicineState.medicines.length} medicines');
          if (medicineState.medicines.isNotEmpty) {
            final firstMedicine = medicineState.medicines.first;
            _addToConsole(
                'First medicine: ${firstMedicine.customName ?? firstMedicine.medicineName}');
            _addToConsole(
                'Medicine household ID: ${firstMedicine.householdId}');
          } else {
            _addToConsole(
                'No medicines found (empty list but state is loaded)');
          }
          _updateTestStatus('medicine_crud', TestStatus.success,
              'Medicine CRUD test completed - ${medicineState.medicines.length} medicines loaded');
        } else if (medicineState is MedicineError) {
          _addToConsole('Medicine loading failed: ${medicineState.message}');
          _updateTestStatus('medicine_crud', TestStatus.failure,
              'Medicine CRUD test failed: ${medicineState.message}');
        } else if (medicineState is MedicineLoading) {
          _addToConsole('Medicine state still loading after timeout');
          _updateTestStatus('medicine_crud', TestStatus.failure,
              'Medicine CRUD test failed: Loading timeout');
        } else {
          _addToConsole(
              'Unexpected medicine state: ${medicineState.runtimeType}');
          _updateTestStatus('medicine_crud', TestStatus.failure,
              'Medicine CRUD test failed: Unexpected state ${medicineState.runtimeType}');
        }
      }
    } catch (e) {
      _updateTestStatus(
          'medicine_crud', TestStatus.failure, 'Medicine CRUD test failed: $e');
    }
  }

  Future<void> _testDashboardData() async {
    _updateTestStatus('dashboard_data', TestStatus.running,
        'Testing dashboard data loading...');

    try {
      final householdId = SupabaseUtils.getHouseholdId(context);
      if (householdId == null) {
        _updateTestStatus('dashboard_data', TestStatus.failure,
            'No household ID for dashboard test');
        return;
      }

      // Test loading dashboard data
      context
          .read<DashboardBloc>()
          .add(DashboardLoadRequested(householdId: householdId));
      _addToConsole('Dashboard data requested for household: $householdId');

      // Wait for response
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        final dashboardState = context.read<DashboardBloc>().state;
        _addToConsole('Dashboard state: ${dashboardState.runtimeType}');
      }

      _updateTestStatus('dashboard_data', TestStatus.success,
          'Dashboard data test completed');
    } catch (e) {
      _updateTestStatus('dashboard_data', TestStatus.failure,
          'Dashboard data test failed: $e');
    }
  }

  Future<void> _testLocationManagement() async {
    _updateTestStatus('location_management', TestStatus.running,
        'Testing location management...');

    try {
      final householdId = SupabaseUtils.getHouseholdId(context);
      if (householdId == null) {
        _updateTestStatus('location_management', TestStatus.failure,
            'No household ID for location test');
        return;
      }

      final client = Supabase.instance.client;
      final supabaseService = getIt<SupabaseService>();
      final locationRepo = LocationRepositoryImpl(client, supabaseService);

      // Test loading locations
      final locationsStream = locationRepo.getHouseholdLocations(householdId);
      _addToConsole('Location stream created for household: $householdId');

      final locations =
          await locationsStream.first.timeout(const Duration(seconds: 5));
      _addToConsole('Found ${locations.length} locations');

      for (final location in locations.take(3)) {
        _addToConsole('Location: ${location.name} (${location.id})');
        _addToConsole('  - Icon: ${location.icon}');
        _addToConsole('  - Household: ${location.householdId}');
      }

      _updateTestStatus('location_management', TestStatus.success,
          'Location management test completed - ${locations.length} locations loaded');
    } catch (e) {
      _updateTestStatus('location_management', TestStatus.failure,
          'Location management test failed: $e');
    }
  }

  Future<void> _testFamilyManagement() async {
    _updateTestStatus('family_management', TestStatus.running,
        'Testing family management...');

    try {
      final householdId = SupabaseUtils.getHouseholdId(context);
      if (householdId == null) {
        _updateTestStatus('family_management', TestStatus.failure,
            'No household ID for family test');
        return;
      }

      final client = Supabase.instance.client;
      final supabaseService = getIt<SupabaseService>();
      final familyRepo = FamilyMemberRepositoryImpl(client, supabaseService);

      // Test loading family members
      final membersStream = familyRepo.getHouseholdMembers(householdId);
      _addToConsole(
          'Family members stream created for household: $householdId');

      final members =
          await membersStream.first.timeout(const Duration(seconds: 5));
      _addToConsole('Found ${members.length} family members');

      for (final member in members.take(3)) {
        _addToConsole('Member: ${member.name} (${member.relationship})');
      }

      _updateTestStatus('family_management', TestStatus.success,
          'Family management test completed');
    } catch (e) {
      _updateTestStatus('family_management', TestStatus.failure,
          'Family management test failed: $e');
    }
  }

  Future<void> _runAllTests() async {
    _addToConsole('=== RUNNING ALL TESTS ===');

    await _testSupabaseConnection();
    await Future.delayed(const Duration(milliseconds: 500));

    await _testAuthenticationState();
    await Future.delayed(const Duration(milliseconds: 500));

    await _testHouseholdIdValidation();
    await Future.delayed(const Duration(milliseconds: 500));

    await _testMedicineCrud();
    await Future.delayed(const Duration(milliseconds: 500));

    await _testDashboardData();
    await Future.delayed(const Duration(milliseconds: 500));

    await _testLocationManagement();
    await Future.delayed(const Duration(milliseconds: 500));

    await _testFamilyManagement();

    _addToConsole('=== ALL TESTS COMPLETED ===');

    // Summary
    final successCount = testResults.values
        .where((status) => status == TestStatus.success)
        .length;
    final totalCount = testResults.length;
    _addToConsole('SUMMARY: $successCount/$totalCount tests passed');
  }
}

enum TestStatus { pending, running, success, failure }

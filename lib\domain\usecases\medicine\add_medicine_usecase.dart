import 'package:dartz/dartz.dart';
import '../../entities/medicine.dart';
import '../../repositories/medicine_repository.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';

class AddMedicineUseCase implements UseCase<Medicine, Medicine> {
  final MedicineRepository repository;

  AddMedicineUseCase(this.repository);

  @override
  Future<Either<Failure, Medicine>> call(Medicine params) async {
    return await repository.addMedicine(params);
  }
}

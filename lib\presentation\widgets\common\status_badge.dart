import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/medicine.dart';

/// Pill-shaped status badge component for displaying status information
/// Features:
/// - 8dp vertical padding, 12dp horizontal padding
/// - 12dp border radius (pill-shaped)
/// - Proper color integration from AppColors
/// - Typography consistency with app theme
class StatusBadge extends StatelessWidget {
  final String text;
  final Color backgroundColor;
  final Color textColor;
  final IconData? icon;
  final double? iconSize;
  final EdgeInsetsGeometry? padding;
  final double? borderRadius;
  final TextStyle? textStyle;

  const StatusBadge({
    super.key,
    required this.text,
    required this.backgroundColor,
    required this.textColor,
    this.icon,
    this.iconSize,
    this.padding,
    this.borderRadius,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    final effectivePadding = padding ?? const EdgeInsets.symmetric(
      vertical: 8.0,   // 8dp vertical padding
      horizontal: 12.0, // 12dp horizontal padding
    );
    final effectiveBorderRadius = borderRadius ?? 12.0; // 12dp border radius
    final effectiveIconSize = iconSize ?? 14.0;
    final effectiveTextStyle = textStyle ?? AppTextStyles.labelSmall.copyWith(
      color: textColor,
      fontWeight: FontWeight.w600,
    );

    return Container(
      padding: effectivePadding,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(effectiveBorderRadius),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(
              icon!,
              size: effectiveIconSize,
              color: textColor,
            ),
            const SizedBox(width: 4.0),
          ],
          Text(
            text,
            style: effectiveTextStyle,
          ),
        ],
      ),
    );
  }
}

/// Factory class for creating medicine status badges
class MedicineStatusBadge {
  /// Create a status badge for medicine status
  static StatusBadge fromStatus(MedicineStatus status) {
    switch (status) {
      case MedicineStatus.expired:
        return StatusBadge(
          text: 'Expiré',
          backgroundColor: AppColors.expired.withValues(alpha: 0.1),
          textColor: AppColors.expired,
          icon: Icons.error,
        );
      case MedicineStatus.expiringSoon:
        return StatusBadge(
          text: 'Expire bientôt',
          backgroundColor: AppColors.expiringSoon.withValues(alpha: 0.1),
          textColor: AppColors.expiringSoon,
          icon: Icons.warning,
        );
      case MedicineStatus.lowStock:
        return StatusBadge(
          text: 'Stock faible',
          backgroundColor: AppColors.lowStock.withValues(alpha: 0.1),
          textColor: AppColors.lowStock,
          icon: Icons.inventory_2,
        );
      case MedicineStatus.outOfStock:
        return StatusBadge(
          text: 'Rupture',
          backgroundColor: AppColors.error.withValues(alpha: 0.1),
          textColor: AppColors.error,
          icon: Icons.remove_circle,
        );
      case MedicineStatus.normal:
        return StatusBadge(
          text: 'Valide',
          backgroundColor: AppColors.adequate.withValues(alpha: 0.1),
          textColor: AppColors.adequate,
          icon: Icons.check_circle,
        );
    }
  }

  /// Create a custom status badge with predefined colors
  static StatusBadge custom({
    required String text,
    required String type,
    IconData? icon,
  }) {
    Color backgroundColor;
    Color textColor;
    IconData? effectiveIcon = icon;

    switch (type.toLowerCase()) {
      case 'success':
      case 'valid':
      case 'adequate':
        backgroundColor = AppColors.adequate.withValues(alpha: 0.1);
        textColor = AppColors.adequate;
        effectiveIcon ??= Icons.check_circle;
        break;
      case 'warning':
      case 'expiring':
        backgroundColor = AppColors.expiringSoon.withValues(alpha: 0.1);
        textColor = AppColors.expiringSoon;
        effectiveIcon ??= Icons.warning;
        break;
      case 'error':
      case 'expired':
        backgroundColor = AppColors.expired.withValues(alpha: 0.1);
        textColor = AppColors.expired;
        effectiveIcon ??= Icons.error;
        break;
      case 'info':
      case 'neutral':
        backgroundColor = AppColors.info.withValues(alpha: 0.1);
        textColor = AppColors.info;
        effectiveIcon ??= Icons.info;
        break;
      case 'teal':
      case 'primary':
        backgroundColor = AppColors.teal.withValues(alpha: 0.1);
        textColor = AppColors.teal;
        effectiveIcon ??= Icons.circle;
        break;
      default:
        backgroundColor = AppColors.grey200;
        textColor = AppColors.grey700;
        effectiveIcon ??= Icons.circle;
    }

    return StatusBadge(
      text: text,
      backgroundColor: backgroundColor,
      textColor: textColor,
      icon: effectiveIcon,
    );
  }
}

/// Factory class for creating general purpose badges
class GeneralBadge {
  /// Create a quantity badge
  static StatusBadge quantity(int quantity) {
    final isLow = quantity <= 5;
    return StatusBadge(
      text: '$quantity',
      backgroundColor: isLow 
          ? AppColors.lowStock.withValues(alpha: 0.1)
          : AppColors.grey200,
      textColor: isLow 
          ? AppColors.lowStock
          : AppColors.grey700,
      icon: Icons.inventory_2,
    );
  }

  /// Create an age badge
  static StatusBadge age(int age) {
    Color backgroundColor;
    Color textColor;
    
    if (age < 2) {
      backgroundColor = Colors.pink.withValues(alpha: 0.1);
      textColor = Colors.pink;
    } else if (age < 13) {
      backgroundColor = Colors.orange.withValues(alpha: 0.1);
      textColor = Colors.orange;
    } else if (age < 18) {
      backgroundColor = AppColors.purple.withValues(alpha: 0.1);
      textColor = AppColors.purple;
    } else if (age < 65) {
      backgroundColor = AppColors.teal.withValues(alpha: 0.1);
      textColor = AppColors.teal;
    } else {
      backgroundColor = Colors.brown.withValues(alpha: 0.1);
      textColor = Colors.brown;
    }

    return StatusBadge(
      text: '$age ans',
      backgroundColor: backgroundColor,
      textColor: textColor,
    );
  }

  /// Create a date badge (for creation dates, etc.)
  static StatusBadge date(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    String text;
    if (difference.inDays == 0) {
      text = 'Aujourd\'hui';
    } else if (difference.inDays == 1) {
      text = 'Hier';
    } else if (difference.inDays < 7) {
      text = '${difference.inDays}j';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      text = '${weeks}sem';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      text = '${months}mois';
    } else {
      final years = (difference.inDays / 365).floor();
      text = '${years}an${years > 1 ? 's' : ''}';
    }

    return StatusBadge(
      text: text,
      backgroundColor: AppColors.grey200,
      textColor: AppColors.grey600,
      icon: Icons.access_time,
      iconSize: 12.0,
    );
  }

  /// Create a tag badge
  static StatusBadge tag(String tagName) {
    return StatusBadge(
      text: tagName,
      backgroundColor: AppColors.teal.withValues(alpha: 0.1),
      textColor: AppColors.teal,
      padding: const EdgeInsets.symmetric(
        vertical: 6.0,
        horizontal: 10.0,
      ),
      borderRadius: 10.0,
    );
  }

  /// Create a count badge
  static StatusBadge count(int count, String label) {
    return StatusBadge(
      text: '$count $label',
      backgroundColor: AppColors.grey200,
      textColor: AppColors.grey700,
    );
  }
}

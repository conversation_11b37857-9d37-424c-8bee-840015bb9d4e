import '../../domain/entities/user.dart';

class UserModel extends User {
  const UserModel({
    required super.id,
    required super.email,
    super.name,
    super.householdId,
    super.householdName,
    super.isOnboardingCompleted,
    super.expiryWarningDays,
    super.createdAt,
    super.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['name'] as String?,
      householdId: json['household_id'] as String?,
      householdName: json['household_name'] as String?,
      isOnboardingCompleted: json['onboarding_completed'] as bool? ?? false,
      expiryWarningDays: json['expiry_warning_days'] as int? ?? 1,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'household_id': householdId,
      'household_name': householdName,
      'onboarding_completed': isOnboardingCompleted,
      'expiry_warning_days': expiryWarningDays,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  factory UserModel.fromEntity(User user) {
    return UserModel(
      id: user.id,
      email: user.email,
      name: user.name,
      householdId: user.householdId,
      householdName: user.householdName,
      isOnboardingCompleted: user.isOnboardingCompleted,
      expiryWarningDays: user.expiryWarningDays,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    );
  }
}

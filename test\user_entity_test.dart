import 'package:flutter_test/flutter_test.dart';
import 'package:medytrack_mobile_v2/domain/entities/user.dart';

void main() {
  group('User Entity Tests', () {
    test('should create user with correct properties', () {
      // Arrange
      const user = User(
        id: 'test-id',
        email: '<EMAIL>',
        name: '<PERSON>',
        householdId: 'household-123',
        householdName: 'Test Household',
      );

      // Assert
      expect(user.id, 'test-id');
      expect(user.email, '<EMAIL>');
      expect(user.name, '<PERSON>');
      expect(user.displayName, '<PERSON>');
      expect(user.initials, 'JD');
    });

    test('should generate correct initials', () {
      // Test with full name
      const user1 = User(
        id: 'test-id',
        email: '<EMAIL>',
        name: '<PERSON>',
      );
      expect(user1.initials, 'JD');

      // Test with single name
      const user2 = User(
        id: 'test-id',
        email: '<EMAIL>',
        name: '<PERSON>',
      );
      expect(user2.initials, 'J');

      // Test with no name (should use email)
      const user3 = User(
        id: 'test-id',
        email: '<EMAIL>',
      );
      expect(user3.initials, 'T');
    });

    test('should generate correct display name', () {
      // Test with name
      const user1 = User(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'John Doe',
      );
      expect(user1.displayName, 'John Doe');

      // Test without name (should use email prefix)
      const user2 = User(
        id: 'test-id',
        email: '<EMAIL>',
      );
      expect(user2.displayName, 'test');
    });

    test('should verify properties that do NOT exist', () {
      const user = User(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'John Doe',
      );

      // These properties should NOT exist and would cause NoSuchMethodError
      // if accessed in the settings page
      expect(() => (user as dynamic).profilePicture, throwsNoSuchMethodError);
      expect(() => (user as dynamic).firstName, throwsNoSuchMethodError);
    });
  });
}

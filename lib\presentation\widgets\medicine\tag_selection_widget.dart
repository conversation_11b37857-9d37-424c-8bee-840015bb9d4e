import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/tag.dart';
import '../../bloc/add_medicine/add_medicine_bloc.dart';
import '../../bloc/add_medicine/add_medicine_event.dart';
import '../../bloc/add_medicine/add_medicine_state.dart';

/// Widget for selecting and managing medicine tags
class TagSelectionWidget extends StatelessWidget {
  const TagSelectionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AddMedicineBloc, AddMedicineState>(
      builder: (context, state) {
        if (state is! AddMedicineFormState) {
          return const SizedBox.shrink();
        }

        final therapeuticTags = state.availableTags
            .where((tag) => tag.category == 'therapeutic')
            .toList();
        final usageTags = state.availableTags
            .where((tag) => tag.category == 'usage')
            .toList();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Catégories et étiquettes',
              style: AppTextStyles.titleMedium,
            ),
            const SizedBox(height: 16),

            // Therapeutic tags dropdown
            if (therapeuticTags.isNotEmpty) ...[
              _buildTagDropdown(
                context,
                'Catégories thérapeutiques',
                therapeuticTags,
                state.selectedTags,
                AppColors.info,
              ),
              const SizedBox(height: 16),
            ],

            // Usage tags dropdown
            if (usageTags.isNotEmpty) ...[
              _buildTagDropdown(
                context,
                'Usage et posologie',
                usageTags,
                state.selectedTags,
                AppColors.success,
              ),
              const SizedBox(height: 16),
            ],

            // Add new tag button
            OutlinedButton.icon(
              onPressed: () => _showCreateTagDialog(context),
              icon: const Icon(Icons.add),
              label: const Text('Créer une nouvelle étiquette'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.teal,
                side: BorderSide(color: AppColors.teal),
              ),
            ),

            // Selected tags summary
            if (state.selectedTags.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildSelectedTagsSummary(context, state.selectedTags),
            ],
          ],
        );
      },
    );
  }

  Widget _buildTagDropdown(
    BuildContext context,
    String title,
    List<Tag> tags,
    List<Tag> selectedTags,
    Color accentColor,
  ) {
    final categorySelectedTags = selectedTags
        .where((selected) => tags.any((tag) => tag.id == selected.id))
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTextStyles.titleSmall.copyWith(
            color: accentColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<Tag>(
          decoration: InputDecoration(
            labelText: 'Sélectionner $title',
            prefixIcon: Icon(Icons.local_offer, color: accentColor),
            border: const OutlineInputBorder(),
          ),
          items: tags.map((tag) {
            return DropdownMenuItem<Tag>(
              value: tag,
              child: Text(tag.displayName),
            );
          }).toList(),
          onChanged: (Tag? selectedTag) {
            if (selectedTag != null) {
              context.read<AddMedicineBloc>().add(TagToggled(tag: selectedTag));
            }
          },
        ),
        if (categorySelectedTags.isNotEmpty) ...[
          const SizedBox(height: 8),
          Wrap(
            spacing: 6,
            runSpacing: 6,
            children: categorySelectedTags.map((tag) {
              final color = _parseColor(tag.color);
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      tag.displayName,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    GestureDetector(
                      onTap: () {
                        context
                            .read<AddMedicineBloc>()
                            .add(TagToggled(tag: tag));
                      },
                      child: const Icon(
                        Icons.close,
                        size: 14,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildSelectedTagsSummary(
      BuildContext context, List<Tag> selectedTags) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.grey50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.grey200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Étiquettes sélectionnées (${selectedTags.length})',
            style: AppTextStyles.titleSmall,
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 6,
            runSpacing: 6,
            children: selectedTags.map((tag) {
              final color = _parseColor(tag.color);
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      tag.displayName,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    GestureDetector(
                      onTap: () {
                        context
                            .read<AddMedicineBloc>()
                            .add(TagToggled(tag: tag));
                      },
                      child: const Icon(
                        Icons.close,
                        size: 14,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  void _showCreateTagDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) => _CreateTagDialog(),
    );
  }

  Color _parseColor(String colorString) {
    try {
      String hex = colorString.replaceAll('#', '');
      if (hex.length == 6) {
        hex = 'FF$hex'; // Add alpha if not present
      }
      return Color(int.parse(hex, radix: 16));
    } catch (e) {
      return AppColors.grey500; // Fallback color
    }
  }
}

class _CreateTagDialog extends StatefulWidget {
  @override
  State<_CreateTagDialog> createState() => _CreateTagDialogState();
}

class _CreateTagDialogState extends State<_CreateTagDialog> {
  final _nameController = TextEditingController();
  String _selectedCategory = 'therapeutic';
  String _selectedColor = '#14B8A6';

  final List<String> _predefinedColors = [
    '#14B8A6',
    '#3B82F6',
    '#8B5CF6',
    '#EF4444',
    '#F59E0B',
    '#10B981',
    '#6366F1',
    '#EC4899',
    '#F97316',
    '#84CC16',
  ];

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Créer une nouvelle étiquette'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Name field
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Nom de l\'étiquette',
              hintText: 'Ex: Antibiotique',
            ),
            autofocus: true,
          ),
          const SizedBox(height: 16),

          // Category selection
          Text('Catégorie', style: AppTextStyles.titleSmall),
          const SizedBox(height: 8),
          DropdownButtonFormField<String>(
            value: _selectedCategory,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(
                value: 'therapeutic',
                child: Text('Thérapeutique'),
              ),
              DropdownMenuItem(
                value: 'usage',
                child: Text('Usage'),
              ),
            ],
            onChanged: (value) {
              setState(() {
                _selectedCategory = value!;
              });
            },
          ),
          const SizedBox(height: 16),

          // Color selection
          Text('Couleur', style: AppTextStyles.titleSmall),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _predefinedColors.map((color) {
              final isSelected = color == _selectedColor;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedColor = color;
                  });
                },
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: Color(
                        int.parse(color.replaceAll('#', 'FF'), radix: 16)),
                    shape: BoxShape.circle,
                    border: isSelected
                        ? Border.all(color: AppColors.grey800, width: 3)
                        : Border.all(color: AppColors.grey300, width: 1),
                  ),
                  child: isSelected
                      ? const Icon(Icons.check, color: Colors.white, size: 16)
                      : null,
                ),
              );
            }).toList(),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_nameController.text.trim().isNotEmpty) {
              context.read<AddMedicineBloc>().add(
                    TagCreated(
                      name: _nameController.text.trim(),
                      color: _selectedColor,
                      category: _selectedCategory,
                    ),
                  );
              Navigator.of(context).pop();
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.teal,
            foregroundColor: Colors.white,
          ),
          child: const Text('Créer'),
        ),
      ],
    );
  }
}

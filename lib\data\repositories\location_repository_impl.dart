import 'package:injectable/injectable.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../core/utils/supabase_utils.dart';
import '../../core/utils/debug_logger.dart';
import '../../core/services/supabase_service.dart';
import '../../domain/entities/location.dart';
import '../../domain/repositories/location_repository.dart';
import '../models/location_model.dart';

@Injectable(as: LocationRepository)
class LocationRepositoryImpl implements LocationRepository {
  final SupabaseClient _supabaseClient;
  final SupabaseService _supabaseService;

  LocationRepositoryImpl(this._supabaseClient, this._supabaseService);

  @override
  Stream<List<Location>> getHouseholdLocations(String householdId) {
    DebugLogger.logRepository('getHouseholdLocations called', data: {
      'householdId': householdId,
      'householdIdLength': householdId.length,
      'isValidUUID': SupabaseUtils.isValidUUID(householdId),
    });

    // Validate household ID before creating stream
    if (!SupabaseUtils.isValidUUID(householdId)) {
      DebugLogger.logError('LOCATION_REPO', 'Invalid household ID format',
          data: {
            'householdId': householdId,
            'length': householdId.length,
          });
      return Stream.error(
          Exception('Invalid household ID format: $householdId'));
    }

    DebugLogger.logDatabase('Querying locations table', data: {
      'table': 'locations',
      'filter': 'household_id = $householdId',
      'orderBy': 'name',
    });

    return _supabaseService.executeStreamWithRetry(
      () => _supabaseClient
          .from('locations')
          .stream(primaryKey: ['id'])
          .eq('household_id', householdId)
          .order('name')
          .map((data) {
            DebugLogger.logDatabase('Locations query result', data: {
              'rawDataCount': data.length,
              'rawData': data,
            });
            return data.map((json) {
              try {
                final model = LocationModel.fromJson(json);
                DebugLogger.logTransform('JSON to LocationModel', 'Success',
                    data: {
                      'id': model.id,
                      'name': model.name,
                    });
                return model.toEntity();
              } catch (e, stackTrace) {
                DebugLogger.logError(
                    'LOCATION_TRANSFORM', 'Failed to parse location',
                    data: {'json': json, 'error': e}, stackTrace: stackTrace);
                rethrow;
              }
            }).toList();
          }),
      operationName: 'getHouseholdLocations',
    );
  }

  @override
  Future<Location?> getLocationById(String locationId) async {
    try {
      // Validate location ID
      if (!SupabaseUtils.isValidUUID(locationId)) {
        throw Exception('Invalid location ID format: $locationId');
      }

      final response = await _supabaseClient
          .from('locations')
          .select(
              'id, user_id, household_id, name, description, created_at, family_id') // Select actual database fields
          .eq('id', locationId)
          .maybeSingle();

      if (response == null) {
        return null;
      }

      final model = LocationModel.fromJson(response);
      return model.toEntity();
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }

  @override
  Future<Location> createLocation(Location location) async {
    return await _supabaseService.executeWithRetry(
      () async {
        // Validate required parameters
        final requiredParams = {
          'household_id': location.householdId,
          'name': location.name,
        };

        if (!SupabaseUtils.validateRequiredParams(requiredParams)) {
          throw Exception('Missing required parameters for location creation');
        }

        final model = LocationModel.fromEntity(location);
        final response = await _supabaseClient
            .from('locations')
            .insert(model.toJson())
            .select(
                'id, user_id, household_id, name, description, created_at, family_id') // Select actual database fields
            .single();

        final createdModel = LocationModel.fromJson(response);
        return createdModel.toEntity();
      },
      operationName: 'createLocation',
    );
  }

  @override
  Future<Location> updateLocation(Location location) async {
    try {
      // Validate required parameters
      final requiredParams = {
        'id': location.id,
        'household_id': location.householdId,
        'name': location.name,
      };

      if (!SupabaseUtils.validateRequiredParams(requiredParams)) {
        throw Exception('Missing required parameters for location update');
      }

      final model = LocationModel.fromEntity(location);
      final response = await _supabaseClient
          .from('locations')
          .update(model.toJson())
          .eq('id', location.id)
          .select()
          .single();

      final updatedModel = LocationModel.fromJson(response);
      return updatedModel.toEntity();
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }

  @override
  Future<void> deleteLocation(String locationId) async {
    try {
      // Validate location ID
      if (!SupabaseUtils.isValidUUID(locationId)) {
        throw Exception('Invalid location ID format: $locationId');
      }

      await _supabaseClient.from('locations').delete().eq('id', locationId);
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }

  @override
  Future<void> initializeDefaultLocations(String householdId) async {
    try {
      // Validate household ID
      if (!SupabaseUtils.isValidUUID(householdId)) {
        throw Exception('Invalid household ID format: $householdId');
      }

      final defaultLocations = DefaultLocations.values.map((locationData) {
        return {
          'household_id': householdId,
          'name': locationData['name']!,
          'description': locationData['description']!,
          'icon': locationData['icon']!,
          // 'color': locationData['color']!, // Removed - column doesn't exist in database schema
          'created_at': DateTime.now().toIso8601String(),
        };
      }).toList();

      await _supabaseClient.from('locations').insert(defaultLocations);
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }
}

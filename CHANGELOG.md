# Changelog

All notable changes to MedyTrack Mobile will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [0.5.0] - 2025-08-20

### Added
- **Comprehensive Error Tracking System**: Complete error monitoring and reporting infrastructure
  - Centralized `AppLogger` utility with silent release builds and debug-only logging
  - `ErrorReporter` class with Supabase integration for automatic error reporting to `app_errors` table
  - Device information collection including platform, browser/OS details, and user context
  - Global error handling with `runZonedGuarded`, `FlutterError.onError`, and `PlatformDispatcher.instance.onError`
- **Debug Tools Page**: Professional debugging interface accessible only in debug builds
  - Test crash functionality to verify error tracking system
  - Async error testing to validate `runZonedGuarded` implementation
  - System information display with device details and platform detection
  - Error log viewer with refresh and clear functionality
  - Material Design 3 theming with teal header pattern following MedyTrack design guidelines
- **Enhanced Logging Infrastructure**: Replaced all `print()` and `debugPrint()` calls with centralized logging
  - Consistent logging format: `[timestamp] [MedyTrack:tag] message`
  - Categorized logging methods: auth, bloc, repository, database, UI, network, error, warning
  - Extension methods for easy logging in any class
  - Complete silence in release builds for production security

### Changed
- **Settings Page Enhancement**: Added conditional debug navigation visible only in debug builds (`kDebugMode`)
- **Main Application Bootstrap**: Enhanced with comprehensive error handling without affecting existing initialization flow
- **Router Configuration**: Updated debug routes to use new `DebugPage` with proper navigation structure

### Technical
- **Database Schema**: Utilizes existing `app_errors` table with proper RLS policies for secure error reporting
- **Device Integration**: Added `device_info_plus` package for comprehensive device context collection
- **Error Boundaries**: Multi-layer error catching for Flutter framework, platform, and async zone errors
- **Network Resilience**: Error reporting handles network failures gracefully without crashing the app
- **Production Security**: Debug tools completely hidden in release builds, error reporting optimized for production use

### Testing
- **Chrome Web Verification**: Successfully tested error tracking functionality on Chrome web platform
- **Supabase Integration**: Confirmed error records properly stored with complete context including user ID, device info, platform, and error source
- **End-to-End Validation**: Verified async error capture, device information collection, and database persistence

### Developer Experience
- **AI-Assisted Development**: This release was accelerated using AI assistance for implementation, with all code reviewed and finalized by human maintainers to ensure quality and security standards.

## [0.3.1] - 2025-01-26

### Added
- **Enhanced Internationalization (i18n)**: Complete language support for French, English, and Arabic with RTL support
- **Real-time Language Switching**: Interface updates immediately without app restart
- **Dynamic Dark Mode**: Toggle works instantly and persists across app restarts
- **Date Format Selection**: Multiple formats (DD/MM/YYYY, MM/DD/YYYY, YYYY-MM-DD) with interactive dialog
- **Expanded Localization**: 70+ localized strings covering all major UI elements

### Changed
- **Settings Architecture Reorganization**: Eliminated redundancy and improved logical grouping
- **Profile Page Integration**: Security features moved from separate page, removed duplicate preferences
- **Personalization Page Enhancement**: Added dark mode toggle, date format selection, and comprehensive CRUD operations
- **Language Settings Focus**: Now exclusively handles language selection with clean interface

### Fixed
- **PersonalizationBloc Loading**: No more "Erreur de chargement" errors with proper userId/householdId handling
- **Language Switching**: Comprehensive UI text updates when changing languages
- **Dark Mode Integration**: Theme changes properly affect MaterialApp theme system
- **Navigation Cleanup**: Removed profile-security route and duplicate settings options

### Removed
- **Profile-Security Page**: Functionality integrated into main Profile page
- **Duplicate Settings**: Eliminated redundant preferences across multiple pages
- **Dead Code**: Removed unused methods, imports, and duplicate functionality

### Technical
- **Enhanced BLoC Architecture**: Proper initialization and error handling across all settings pages
- **Multi-BLoC Integration**: Seamless coordination between LanguageBloc and SettingsBloc
- **Code Quality**: Fixed type annotations, null safety warnings, and improved separation of concerns
- **Localization Infrastructure**: Comprehensive ARB files and proper AppLocalizations integration

## [0.2.0] - 2025-01-16

### Added
- Enhanced Location entity with proper database schema alignment
- Improved error handling and validation throughout the application
- Better code documentation and inline comments
- Optimized dependency management for better performance

### Changed
- **BREAKING**: Removed `color` field from Location entity (now uses default teal color)
- Updated Location model to use `icon_name` field for database compatibility
- Added `user_id` field to Location model as required by database schema
- Modernized deprecated Flutter APIs (`withOpacity` → `withValues`)
- Improved BuildContext usage with proper async gap handling
- Enhanced test method names for better Flutter compatibility

### Removed
- **18 redundant documentation files** from root directory for cleaner workspace
- **Unused dependencies**: `hive`, `hive_flutter`, `lucide_icons` (3 packages removed)
- **Redundant test files**: `settings_page_fix_verification.dart`
- **Unused code files**: `lib/main_simple.dart`
- **Unused asset references** from pubspec.yaml
- Unnecessary color validation logic from LocationBloc
- Dead code and unused methods throughout the codebase

### Fixed
- Location entity constructor to match database schema requirements
- Test files to work with updated Location model structure
- Deprecated API usage for better Flutter compatibility
- Missing `@override` annotations in model classes
- BuildContext async gap issues in splash page
- Test method naming for current Flutter test framework

### Performance
- **Reduced bundle size** by removing 3 unused dependencies
- **Faster builds** with optimized dependency tree (22 → 19 production packages)
- **Cleaner workspace** with 21 fewer files for better IDE performance
- **Improved maintainability** with consistent code patterns

### Technical Debt
- Resolved all diagnostic issues and compilation errors
- Fixed deprecated Flutter API usage
- Improved code consistency across all BLoC implementations
- Enhanced error handling patterns throughout the application

## [0.1.1] - 2025-01-15

### Added
- Initial project setup with Flutter 3.24.0
- Clean architecture implementation with BLoC pattern
- Supabase backend integration
- User authentication and household management
- Modern dashboard with layered UI design
- Real-time medicine search functionality
- Statistics cards with color-coded alerts
- Medicine CRUD operations
- Location and tag management
- Custom bottom navigation with floating action button
- Responsive design for multiple screen sizes

### Changed
- Updated app description to focus on household medicine management
- Redesigned dashboard to match web app Home page layout
- Improved navigation structure with custom components

### Technical
- Implemented dependency injection with GetIt
- Set up GoRouter for declarative navigation
- Created reusable UI components with Material Design 3
- Established comprehensive testing structure
- Added code documentation and inline comments

## [1.0.0] - 2024-12-XX (Planned)

### Added
- Complete medicine management system
- Expiry date tracking and notifications
- Multi-user household support
- Cross-platform compatibility (iOS/Android)
- Offline mode capabilities
- Data export/import functionality

### Features
- **Dashboard**: Real-time overview with statistics
- **Medicine Management**: Add, edit, view, delete medicines
- **Search & Filter**: Intelligent search with real-time results
- **Expiry Tracking**: Automated monitoring and alerts
- **Location Management**: Organize by storage locations
- **User Management**: Household-based sharing
- **Modern UI**: Layered design with smooth animations

### Technical Achievements
- Clean Architecture with separation of concerns
- BLoC pattern for predictable state management
- Supabase integration for real-time data
- Comprehensive test coverage
- Performance optimizations
- Accessibility compliance

## Development Milestones

### Phase 1: Foundation ✅
- [x] Project setup and architecture
- [x] Authentication system
- [x] Basic UI components
- [x] Database schema design

### Phase 2: Core Features ✅
- [x] Dashboard implementation
- [x] Medicine CRUD operations
- [x] Search functionality
- [x] Statistics and analytics

### Phase 3: Enhanced UX 🚧
- [x] Modern layered UI design
- [x] Custom navigation components
- [x] Responsive layouts
- [ ] Animations and transitions

### Phase 4: Advanced Features 📋
- [ ] Push notifications
- [ ] Barcode scanning
- [ ] Medicine interaction warnings
- [ ] Pharmacy API integration

### Phase 5: Polish & Release 📋
- [ ] Performance optimizations
- [ ] Comprehensive testing
- [ ] Documentation completion
- [ ] App store preparation

## Version History

### v0.3.0 - Dashboard Redesign (Current)
- Implemented layered dashboard design
- Added custom bottom navigation with FAB
- Enhanced statistics cards with proper color coding
- Improved search functionality with real-time filtering
- Fixed navigation conflicts and layout issues

### v0.2.0 - Core Functionality
- Added medicine management features
- Implemented BLoC state management
- Created reusable UI components
- Set up Supabase backend integration

### v0.1.0 - Initial Setup
- Project initialization with Flutter 3.24.0
- Clean architecture implementation
- Basic authentication flow
- Initial UI framework

## Breaking Changes

### v0.3.0
- Modified navigation structure (removed MainLayout for dashboard)
- Updated ModernHeader component API (removed `greeting` parameter)
- Changed statistics card data structure to match web app

### v0.2.0
- Restructured project architecture
- Changed data models for Supabase integration
- Updated dependency injection setup

## Migration Guide

### Upgrading to v0.3.0
1. Update navigation routes if using custom implementations
2. Replace `greeting` parameter with `title` and `showGreeting` in ModernHeader
3. Update statistics data handling to use new card structure

### Upgrading to v0.2.0
1. Run `flutter pub get` to update dependencies
2. Update Supabase configuration with new schema
3. Migrate local data if upgrading from v0.1.0

## Known Issues

### Current
- Minor type annotation warnings in profile page
- Deprecated `withOpacity` usage (non-breaking)

### Resolved
- ✅ Duplicate navigation bars (v0.3.0)
- ✅ FAB positioning and clipping (v0.3.0)
- ✅ Layout structure integration (v0.3.0)

## Upcoming Features

### v1.1.0 (Planned)
- Push notification system
- Barcode scanning for medicine entry
- Dark mode support
- Enhanced accessibility features

### v1.2.0 (Planned)
- Medicine interaction warnings
- Pharmacy API integration
- Advanced analytics and insights
- Export/import functionality

### v2.0.0 (Future)
- Multi-language support
- Wearable device integration
- AI-powered medicine recommendations
- Telemedicine integration

---

For more details about specific changes, see the [commit history](https://github.com/BeeGaat/MedyTrack-Mobile/commits/main) or [release notes](https://github.com/BeeGaat/MedyTrack-Mobile/releases).

import 'package:equatable/equatable.dart';

enum FrequencyType { daily, weekly, hourlyInterval }

enum ReminderStatus { active, paused, archived }

class <PERSON>minder extends Equatable {
  final String? id;
  final String userMedicineId;
  final String? name;
  final double? dosageAmount;
  final String? dosageUnit;
  final List<String> times; // "HH:mm"
  final String
      frequencyType; // 'DAILY', 'WEEKLY', 'HOURLY_INTERVAL', 'SPECIFIC_DATES'
  final int? frequencyValue;
  final List<int> frequencyDays; // Days of week for weekly, or interval days
  final List<DateTime> specificDates; // For specific date reminders
  final DateTime startDate;
  final DateTime? endDate;
  final String? notes;
  final bool isActive;
  final ReminderStatus status;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Reminder({
    this.id,
    required this.userMedicineId,
    this.name,
    this.dosageAmount,
    this.dosageUnit,
    required this.times,
    required this.frequencyType,
    this.frequencyValue,
    this.frequencyDays = const [],
    this.specificDates = const [],
    required this.startDate,
    this.endDate,
    this.notes,
    this.isActive = true,
    this.status = ReminderStatus.active,
    this.createdAt,
    this.updatedAt,
  });

  /// Create a copy of this reminder with updated fields
  Reminder copyWith({
    String? id,
    String? userMedicineId,
    String? name,
    double? dosageAmount,
    String? dosageUnit,
    List<String>? times,
    String? frequencyType,
    int? frequencyValue,
    List<int>? frequencyDays,
    List<DateTime>? specificDates,
    DateTime? startDate,
    DateTime? endDate,
    String? notes,
    bool? isActive,
    ReminderStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Reminder(
      id: id ?? this.id,
      userMedicineId: userMedicineId ?? this.userMedicineId,
      name: name ?? this.name,
      dosageAmount: dosageAmount ?? this.dosageAmount,
      dosageUnit: dosageUnit ?? this.dosageUnit,
      times: times ?? this.times,
      frequencyType: frequencyType ?? this.frequencyType,
      frequencyValue: frequencyValue ?? this.frequencyValue,
      frequencyDays: frequencyDays ?? this.frequencyDays,
      specificDates: specificDates ?? this.specificDates,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      notes: notes ?? this.notes,
      isActive: isActive ?? this.isActive,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Check if reminder is currently active (not paused or archived)
  bool get isCurrentlyActive => status == ReminderStatus.active && isActive;

  /// Check if reminder is paused
  bool get isPaused => status == ReminderStatus.paused;

  /// Check if reminder is archived
  bool get isArchived => status == ReminderStatus.archived;

  @override
  List<Object?> get props => [
        id,
        userMedicineId,
        name,
        dosageAmount,
        dosageUnit,
        times,
        frequencyType,
        frequencyValue,
        frequencyDays,
        specificDates,
        startDate,
        endDate,
        notes,
        isActive,
        status,
        createdAt,
        updatedAt,
      ];
}

class DoseHistory extends Equatable {
  final int? id;
  final String? userId; // Add user_id field for RLS policies
  final String userMedicineId;
  final String? reminderId;
  final DateTime scheduledAt;
  final DateTime? actionAt;
  final String status; // 'TAKEN', 'SKIPPED', 'SNOOZED'
  final DateTime? snoozeAt; // NEW: persisted snooze time to align UI/notification/DB

  const DoseHistory({
    this.id,
    this.userId,
    required this.userMedicineId,
    this.reminderId,
    required this.scheduledAt,
    this.actionAt,
    required this.status,
    this.snoozeAt,
  });

  factory DoseHistory.fromJson(Map<String, dynamic> json) {
    return DoseHistory(
      id: json['id'],
      userId: json['user_id'],
      userMedicineId: json['user_medicine_id'],
      reminderId: json['reminder_id'],
      scheduledAt: DateTime.parse(json['scheduled_time']),
      actionAt: json['action_time'] != null
          ? DateTime.parse(json['action_time'])
          : null,
      status: json['status'],
      snoozeAt: json['snooze_at'] != null
          ? DateTime.parse(json['snooze_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'user_id': userId,
      'user_medicine_id': userMedicineId,
      'reminder_id': reminderId,
      'scheduled_time': scheduledAt.toIso8601String(),
      'action_time': actionAt?.toIso8601String(),
      'status': status,
      'snooze_at': snoozeAt?.toIso8601String(),
    };

    // Only include id if it's not null (for updates, not inserts)
    if (id != null) {
      json['id'] = id;
    }

    return json;
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        userMedicineId,
        reminderId,
        scheduledAt,
        actionAt,
        status,
        snoozeAt,
      ];
}

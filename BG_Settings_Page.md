# 🛠️ MedyTrack – Settings Page Specification

Central hub for configuring user preferences, permissions, and app-wide behavior. Organized by role: General Users, Super Users, and Super Admins.

---

## 👤 General User Settings

### 🔐 Profile
- **Edit Profile**
  - Update name, avatar, and contact email/phone

### 🔒 Security Options
- **Change Password**
  - Update login credentials
- **Authentication Mode**
  - Choose between PIN or biometric login

### 🎨 Personalization
- **Manage Family Members**
  - Add, edit, or remove family profiles
  - Assign medicines per member
- **Storage Locations**
  - Define physical locations (e.g. fridge, cabinet)
  - Edit or assign storage per medicine
- **Tags Management**
  - Create, edit, or remove tags
  - Assign multiple tags to medicines (e.g. painkiller, child-safe)
- **Minimum Expiry Threshold**
  - Customize threshold (in days) to trigger expiry alerts

### ⚙️ App Settings
- **Notifications**
  - Toggle alerts for:
    - Expiring medicines
    - Low stock
    - Prescription renewals
- **Language & Region**
  - Select UI language
  - Set country for localized medicine database
- **Offline Mode**
  - Enable offline access
  - Manage cached data for local medicine search

### 👥 Invitations
- **Invite Family Members**
  - Send invites via email or phone number
  - Manage and revoke pending invitations

### 🛟 Support
- **Terms of Service**
- **Privacy Policy**

> ℹ️ App version info displayed at the bottom of the page.

---

## 🧑‍💻 Super User Features

- **Backup & Sync**
  - Manually sync with Supabase
  - Export to or import from local/cloud backup
- **Data Import/Export**
  - Export full dataset (CSV, JSON)
  - Import backups with validation
- **Diagnostics**
  - View logs of sync history
  - Monitor storage/cache usage

---

## 👨‍⚖️ Super Admin Tools

- **User & Role Management**
  - Assign or modify roles (User, Super User, Admin)
- **System Defaults**
  - Define default tags, expiry rules, storage locations
- **Audit Logs**
  - View detailed action logs (edits, deletions, logins)
- **Database Maintenance**
  - Run cleanup routines (e.g., remove unused tags)
  - Trigger manual syncs or migrations
- **Admin Page Controls**
  - Enable/disable:
    - App-wide statistics
    - New entries and user edits tracking dashboards

---

> ✅ All changes are autosaved and synced automatically when the app is online.
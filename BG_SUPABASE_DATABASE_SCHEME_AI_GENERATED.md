Database Documentation for Medication Management System
1. Database Architecture Overview
The database is structured using Supabase with PostgreSQL, organized into two primary schemas:

auth schema: Manages authentication and user-related system tables
public schema: Contains application-specific tables for medication and household management
2. Core Authentication Schema (auth)
2.1 Users Table (auth.users)
Primary authentication table
Columns include:
id: Unique user identifier (UUID)
email: User's email address
encrypted_password: Hashed user password
created_at, updated_at: Timestamp tracking
raw_user_meta_data, raw_app_meta_data: JSON fields for additional user information
Authentication-related fields: email_confirmed_at, last_sign_in_at
2.2 Other Authentication Tables
sessions: User login sessions
identities: External authentication provider connections
mfa_factors: Multi-factor authentication management
refresh_tokens: Token management for authentication
3. Public Schema Tables
3.1 Core Entities
3.1.1 Households (households)
Central organizational unit for users
Columns:
id: Unique household identifier
name: Household name
created_by: User who created the household
created_at: Creation timestamp
3.1.2 Household Members (household_members)
Manages user membership in households
Columns:
household_id: Reference to household
user_id: Reference to user
role: User's role (owner, admin, member)
permissions: JSON defining user capabilities
3.1.3 Profiles (profiles)
Extended user profile information
Linked to auth.users via id
Columns:
name
email
avatar_url
onboarding_completed
3.2 Medication Management
3.2.1 User Medicines (user_medicines)
Personal medication tracking
Columns:
medicine_id: Reference to medicine catalog
dosage
quantity
expiration_date
location
family_member_id: Optional association with family member
3.2.2 Tunisia Medicines Catalog (tunisia_medicines)
Comprehensive medicine database
Columns:
nom: Medicine name
dosage
forme: Medicine form
classe: Therapeutic class
laboratoire: Manufacturer
3.2.3 Presentations (presentations)
Medicine presentation details
Columns:
cip13: Unique identifier
label
price
reimbursement_rate
3.3 Family and Social Structure
3.3.1 Family Members (family_members)
Tracks individual family members
Columns:
name
role
birth_date
household_id
3.3.2 Household Invitations (household_invitations)
Manages invitations to households
Columns:
household_id
invited_by
email
status
permissions
3.4 Supplementary Tables
3.4.1 Tags (tags)
Categorization for medicines
Columns:
name
color
category (therapeutic/usage)
3.4.2 Locations (locations)
Tracks storage locations for medicines
Columns:
name
description
household_id
4. Security and Access Control
4.1 Row Level Security (RLS)
Enabled on most tables
Ensures users can only access data within their household/context
4.2 Authentication Strategies
Email/Password
Multi-factor authentication support
External identity provider integration
5. Database Extensions
Notable extensions:

uuid-ossp: UUID generation
pgcrypto: Cryptographic functions
vector: Vector data type support
pg_graphql: GraphQL API integration
6. Data Relationships
Households are central, connecting users, medicines, and family members
Strict referential integrity between tables
Flexible permission model through JSON-defined roles
7. Performance Considerations
UUID primary keys
Indexed foreign key relationships
RLS policies optimized for quick access
8. Scalability Features
Supports multi-household, multi-user scenarios
Flexible medicine tracking across family members
Comprehensive medicine catalog integration

Comprehensive Data Relationships Diagram
Relationship Mapping Legend
🔑 Primary Key 🔗 Foreign Key Relationship 📦 Entity/Table 🔒 Row Level Security Enabled

Core Relationship Diagram
SQL Query



📦 auth.users 🔑
│
├─🔗 public.profiles
│  └─ Extended user information
│
├─🔗 public.household_members
│  └─ Links users to households
│
└─🔗 public.users
   └─ Additional user-specific settings

Detailed Relationship Breakdown
1. User-Household Ecosystem
SQL Query



📦 auth.users 🔑
│
├─🔗 public.households 
│   ├─ created_by (household creator)
│   └─ Tracks household ownership
│
└─🔗 public.household_members 🔒
    ├─ user_id (membership link)
    ├─ role (owner/admin/member)
    └─ permissions (granular access control)

2. Medication Management Relationships
SQL Query



📦 public.user_medicines 🔒
│
├─🔗 public.tunisia_medicines
│   └─ Catalog reference for medicines
│
├─🔗 public.family_members
│   └─ Optional medicine assignment
│
├─🔗 public.households
│   └─ Household-level medicine tracking
│
└─🔗 public.medicine_tags
    └─ Tagging and categorization

3. Family and Social Structure
SQL Query



📦 public.households 🔑
│
├─🔗 public.family_members
│   ├─ household_id
│   └─ Tracks family members within households
│
├─🔗 public.locations
│   └─ Location tracking per household
│
└─🔗 public.household_invitations
    ├─ invited_by (user who sent invite)
    └─ Manages household membership invitations

4. Medicine Catalog Relationships
SQL Query



📦 public.tunisia_medicines 🔑
│
├─🔗 public.presentations
│   └─ Detailed medicine presentation info
│
├─🔗 public.specialties
│   └─ Comprehensive medicine details
│
└─🔗 public.user_medicines
    └─ Personal medicine tracking

Relationship Characteristics
1. Hierarchical Structure
🏠 Households are the central organizing principle
👥 Users can belong to multiple households
💊 Medicines can be tracked at household and individual levels
2. Flexible Permissions
🔒 Row Level Security ensures data isolation
📋 JSON-based permissions in household_members
🔑 Granular access control at multiple levels
3. Referential Integrity
All relationships use UUID foreign keys
Cascading updates and deletes managed through database constraints
No orphaned records possible due to strict referential checks
Key Relationship Patterns
One-to-Many Relationships

One Household → Many Household Members
One Household → Many Medicines
One Family Member → Many Medicines
Many-to-Many Relationships

Medicines ↔ Tags (through medicine_tags)
Users ↔ Households (through household_members)
Polymorphic Relationships

Medicines can be linked to:
Households
Family Members
Individual Users
Performance Considerations
Foreign key columns are indexed
UUIDs used consistently for flexible joins
JSON columns for extensible metadata
Potential Query Patterns
Find all medicines in a household
List family members with their medicines
Track medicine usage across households
Generate user-specific medicine reports
Scalability Design
Horizontal scaling support
Flexible entity relationships
Minimal data duplication
Efficient querying through thoughtful indexing

Recommended Indexes
SQL Query

-- Household Member Lookups
CREATE INDEX idx_household_members_user_id ON household_members(user_id);
CREATE INDEX idx_household_members_household_id ON household_members(household_id);

-- Medicine Tracking
CREATE INDEX idx_user_medicines_household_id ON user_medicines(household_id);
CREATE INDEX idx_user_medicines_family_member_id ON user_medicines(family_member_id);

-- Invitation Tracking
CREATE INDEX idx_household_invitations_household_id ON household_invitations(household_id);
CREATE INDEX idx_household_invitations_invited_by ON household_invitations(invited_by);

Potential Future Expansions
Geolocation support for medicine storage
Advanced tagging and categorization
Machine learning-based medicine recommendations
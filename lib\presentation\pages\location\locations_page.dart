import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/supabase_utils.dart';
import '../../../domain/entities/location.dart';
import '../../bloc/location/location_bloc.dart';
import '../../bloc/location/location_event.dart';
import '../../bloc/location/location_state.dart';
import '../../widgets/location/enhanced_location_list_card.dart';
import '../../widgets/location/add_location_dialog.dart';

class LocationsPage extends StatefulWidget {
  final String householdId;

  const LocationsPage({
    super.key,
    required this.householdId,
  });

  @override
  State<LocationsPage> createState() => _LocationsPageState();
}

class _LocationsPageState extends State<LocationsPage> {
  @override
  void initState() {
    super.initState();
    // Initialize locations with validation
    _initializeLocations();
  }

  void _initializeLocations() {
    if (widget.householdId.isNotEmpty &&
        widget.householdId != 'default-household') {
      context.read<LocationBloc>().add(
            LocationsInitialized(householdId: widget.householdId),
          );
    } else {
      // Retry after a short delay if household ID is not ready
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          final householdId = SupabaseUtils.getHouseholdId(context);
          if (householdId != null && householdId.isNotEmpty) {
            context.read<LocationBloc>().add(
                  LocationsInitialized(householdId: householdId),
                );
          }
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      body: BlocListener<LocationBloc, LocationState>(
        listener: (context, state) {
          if (state is LocationOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.success,
                behavior: SnackBarBehavior.floating,
              ),
            );
          } else if (state is LocationError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        },
        child: Column(
          children: [
            // Header with teal background
            Container(
              padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () => context.pop(),
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.arrow_back,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      'Emplacements',
                      style: AppTextStyles.headlineMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Content container with white background
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, -4),
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.only(top: 20),
                  child: BlocBuilder<LocationBloc, LocationState>(
                    builder: (context, state) {
                      if (state is LocationLoading) {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      }

                      if (state is LocationError) {
                        return Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.error_outline,
                                size: 64,
                                color: AppColors.error,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'Erreur de chargement',
                                style: AppTextStyles.titleMedium,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                state.message,
                                style: AppTextStyles.bodyMedium.copyWith(
                                  color: AppColors.grey600,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: () {
                                  context.read<LocationBloc>().add(
                                        LocationsInitialized(
                                          householdId: widget.householdId,
                                        ),
                                      );
                                },
                                child: const Text('Réessayer'),
                              ),
                            ],
                          ),
                        );
                      }

                      if (state is LocationLoaded) {
                        if (state.isEmpty) {
                          return _buildEmptyState(context);
                        }

                        final locations = state.locations;

                        return Column(
                          children: [
                            Expanded(
                              child: RefreshIndicator(
                                onRefresh: () async {
                                  context.read<LocationBloc>().add(
                                        const LocationsRefreshed(),
                                      );
                                },
                                child: ListView.builder(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20),
                                  itemCount: locations.length,
                                  itemBuilder: (context, index) {
                                    final location = locations[index];
                                    return EnhancedLocationListCard(
                                      location: location,
                                      onTap: () => _showLocationDetails(
                                          context, location),
                                      onEdit: () => _showEditLocationDialog(
                                          context, location),
                                      onDelete: () => _showDeleteConfirmation(
                                          context, location),
                                    );
                                  },
                                ),
                              ),
                            ),
                            // Centered Add Location Button
                            Padding(
                              padding:
                                  const EdgeInsets.only(top: 24, bottom: 20),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  ElevatedButton.icon(
                                    onPressed: () =>
                                        _showAddLocationDialog(context),
                                    icon: const Icon(Icons.add, size: 20),
                                    label: Text(
                                      'Ajouter un emplacement',
                                      style: AppTextStyles.labelLarge,
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.teal,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 24,
                                        vertical: 12,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      elevation: 2,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        );
                      }

                      return const SizedBox.shrink();
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off,
            size: 64,
            color: AppColors.grey400,
          ),
          const SizedBox(height: 16),
          Text(
            'Aucun emplacement',
            style: AppTextStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Commencez par ajouter vos premiers emplacements de stockage',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.grey600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          // Responsive button layout to prevent overflow
          LayoutBuilder(
            builder: (context, constraints) {
              // If screen is too narrow, stack buttons vertically
              if (constraints.maxWidth < 400) {
                return Column(
                  children: [
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () => _showAddLocationDialog(context),
                        icon: const Icon(Icons.add),
                        label: const Text('Ajouter un emplacement'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.teal,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: () {
                          context.read<LocationBloc>().add(
                                DefaultLocationsInitialized(
                                  householdId: widget.householdId,
                                ),
                              );
                        },
                        icon: const Icon(Icons.auto_awesome),
                        label: const Text('Emplacements par défaut'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.teal,
                          side: BorderSide(color: AppColors.teal),
                        ),
                      ),
                    ),
                  ],
                );
              } else {
                // Wide screen: keep buttons side by side
                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Flexible(
                      child: ElevatedButton.icon(
                        onPressed: () => _showAddLocationDialog(context),
                        icon: const Icon(Icons.add),
                        label: const Text('Ajouter un emplacement'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.teal,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Flexible(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          context.read<LocationBloc>().add(
                                DefaultLocationsInitialized(
                                  householdId: widget.householdId,
                                ),
                              );
                        },
                        icon: const Icon(Icons.auto_awesome),
                        label: const Text('Emplacements par défaut'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.teal,
                          side: BorderSide(color: AppColors.teal),
                        ),
                      ),
                    ),
                  ],
                );
              }
            },
          ),
        ],
      ),
    );
  }

  void _showAddLocationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AddLocationDialog(
        householdId: widget.householdId,
      ),
    );
  }

  void _showEditLocationDialog(BuildContext context, Location location) {
    showDialog(
      context: context,
      builder: (context) => AddLocationDialog(
        householdId: widget.householdId,
        location: location,
      ),
    );
  }

  void _showLocationDetails(BuildContext context, Location location) {
    // TODO: Implement location details page
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Détails de ${location.displayName}'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, Location location) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer l\'emplacement'),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer "${location.displayName}" ?\n\n'
          'Cette action est irréversible et tous les médicaments associés '
          'perdront leur emplacement.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<LocationBloc>().add(
                    LocationDeleted(locationId: location.id),
                  );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }
}

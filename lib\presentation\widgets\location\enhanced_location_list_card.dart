import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/location.dart';
import '../common/base_list_card.dart';
import '../common/status_badge.dart';

/// Enhanced location list card following the new design specifications
/// Features:
/// - 72dp fixed height
/// - 16dp border radius
/// - Consistent typography and icon sizing
/// - Material 3 design principles
class EnhancedLocationListCard extends StatelessWidget {
  final Location location;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool isSelected;
  final bool isSelectionMode;
  final int? medicineCount; // Optional medicine count for this location

  const EnhancedLocationListCard({
    super.key,
    required this.location,
    this.onTap,
    this.onLongPress,
    this.onEdit,
    this.onDelete,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.medicineCount,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedBaseListCard(
      leading: _buildLeading(),
      title: _buildTitle(),
      subtitle: _buildSubtitle(),
      trailing: _buildTrailing(),
      actions: _buildActions(),
      onTap: onTap,
      onLongPress: onLongPress,
      isSelected: isSelected,
      showBorder: isSelectionMode,
      backgroundColor: _getLocationColor().withValues(alpha: 0.05),
      borderColor: _getLocationColor(),
    );
  }

  Widget _buildLeading() {
    return ListCardLeading.icon(
      icon: _getLocationIcon(),
      color: _getLocationColor(),
      size: 48.0,
      iconSize: 20.0, // 20dp for primary icons
    );
  }

  Widget _buildTitle() {
    return Text(
      location.displayName,
      style: AppTextStyles.titleMedium.copyWith(
        fontWeight: FontWeight.w600,
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildSubtitle() {
    final List<String> subtitleParts = [];

    // Add description if available
    if (location.description != null && location.description!.isNotEmpty) {
      subtitleParts.add(location.description!);
    }

    // Add creation date
    subtitleParts.add(_formatDate(location.createdAt));

    // Add medicine count if available
    if (medicineCount != null) {
      subtitleParts
          .add('$medicineCount médicament${medicineCount! > 1 ? 's' : ''}');
    }

    return Text(
      subtitleParts.join(' • '),
      style: AppTextStyles.bodySmall.copyWith(
        color: AppColors.grey600,
      ),
      maxLines: 2, // Allow 2 lines for better content display
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildTrailing() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Medicine count badge only - simplified to prevent overflow
        if (medicineCount != null) GeneralBadge.count(medicineCount!, 'méd.'),
      ],
    );
  }

  List<Widget>? _buildActions() {
    if (onEdit == null && onDelete == null) return null;

    return [
      ListCardTrailing.moreOptions(
        items: [
          if (onEdit != null)
            PopupMenuItem<String>(
              value: 'edit',
              child: Row(
                children: [
                  Icon(
                    Icons.edit_outlined,
                    size: 18,
                    color: AppColors.grey600,
                  ),
                  const SizedBox(width: 8),
                  const Text('Modifier'),
                ],
              ),
            ),
          if (onDelete != null)
            PopupMenuItem<String>(
              value: 'delete',
              child: Row(
                children: [
                  Icon(
                    Icons.delete_outline,
                    size: 18,
                    color: AppColors.error,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Supprimer',
                    style: TextStyle(color: AppColors.error),
                  ),
                ],
              ),
            ),
        ],
        onSelected: (value) {
          switch (value) {
            case 'edit':
              onEdit?.call();
              break;
            case 'delete':
              onDelete?.call();
              break;
          }
        },
      ),
    ];
  }

  /// Get location icon based on icon name
  IconData _getLocationIcon() {
    switch (location.icon.toLowerCase()) {
      case 'medical_services':
        return Icons.medical_services;
      case 'kitchen':
        return Icons.kitchen;
      case 'bed':
        return Icons.bed;
      case 'restaurant':
        return Icons.restaurant;
      case 'bathtub':
        return Icons.bathtub;
      case 'home':
        return Icons.home;
      case 'work':
        return Icons.work;
      case 'local_pharmacy':
        return Icons.local_pharmacy;
      case 'location_on':
        return Icons.location_on;
      case 'room':
        return Icons.room;
      case 'storage':
        return Icons.storage;
      case 'inventory':
        return Icons.inventory;
      case 'folder':
        return Icons.folder;
      case 'archive':
        return Icons.archive;
      default:
        return Icons.location_on;
    }
  }

  /// Get location color
  Color _getLocationColor() {
    try {
      final colorString = location.hexColor;
      return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return AppColors.teal; // Default color
    }
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Aujourd\'hui';
    } else if (difference.inDays == 1) {
      return 'Hier';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}j';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '${weeks}sem';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '${months}mois';
    } else {
      final years = (difference.inDays / 365).floor();
      return '${years}an${years > 1 ? 's' : ''}';
    }
  }
}

/// Compact version of the enhanced location card
class CompactLocationListCard extends StatelessWidget {
  final Location location;
  final VoidCallback? onTap;
  final bool isSelected;
  final int? medicineCount;

  const CompactLocationListCard({
    super.key,
    required this.location,
    this.onTap,
    this.isSelected = false,
    this.medicineCount,
  });

  @override
  Widget build(BuildContext context) {
    return BaseListCard(
      height: 56.0, // Smaller height for compact version
      onTap: onTap,
      isSelected: isSelected,
      backgroundColor: _getLocationColor().withValues(alpha: 0.05),
      borderColor: _getLocationColor(),
      child: Row(
        children: [
          // Compact leading icon
          ListCardLeading.icon(
            icon: _getLocationIcon(),
            color: _getLocationColor(),
            size: 40.0,
            iconSize: 18.0,
            borderRadius: 6.0,
          ),

          const SizedBox(width: 12.0),

          // Main content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  location.displayName,
                  style: AppTextStyles.titleSmall.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (location.description != null) ...[
                  const SizedBox(height: 2.0),
                  Text(
                    location.description!,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.grey600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),

          // Medicine count badge if available
          if (medicineCount != null)
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8.0,
                vertical: 4.0,
              ),
              decoration: BoxDecoration(
                color: _getLocationColor().withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Text(
                '$medicineCount',
                style: AppTextStyles.labelSmall.copyWith(
                  color: _getLocationColor(),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  IconData _getLocationIcon() {
    switch (location.icon.toLowerCase()) {
      case 'medical_services':
        return Icons.medical_services;
      case 'kitchen':
        return Icons.kitchen;
      case 'bed':
        return Icons.bed;
      case 'restaurant':
        return Icons.restaurant;
      case 'bathtub':
        return Icons.bathtub;
      case 'home':
        return Icons.home;
      case 'work':
        return Icons.work;
      case 'local_pharmacy':
        return Icons.local_pharmacy;
      case 'location_on':
        return Icons.location_on;
      case 'room':
        return Icons.room;
      case 'storage':
        return Icons.storage;
      case 'inventory':
        return Icons.inventory;
      case 'folder':
        return Icons.folder;
      case 'archive':
        return Icons.archive;
      default:
        return Icons.location_on;
    }
  }

  Color _getLocationColor() {
    try {
      final colorString = location.hexColor;
      return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return AppColors.teal; // Default color
    }
  }
}

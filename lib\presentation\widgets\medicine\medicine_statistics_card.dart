import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../bloc/my_medicines/my_medicines_event.dart';
import '../../bloc/my_medicines/my_medicines_state.dart';

class MedicineStatisticsCard extends StatelessWidget {
  final MedicineStatistics statistics;
  final Function(MedicineFilter)? onFilterTap;

  const MedicineStatisticsCard({
    super.key,
    required this.statistics,
    this.onFilterTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Aperçu de l\'inventaire',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            // Statistics grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              childAspectRatio: 2.5,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              children: [
                _buildStatisticItem(
                  title: 'Total',
                  value: statistics.totalMedicines,
                  icon: Icons.medication,
                  color: AppColors.teal,
                  filter: MedicineFilter.all,
                ),
                _buildStatisticItem(
                  title: 'Expirés',
                  value: statistics.expiredMedicines,
                  icon: Icons.error,
                  color: AppColors.error,
                  filter: MedicineFilter.expired,
                ),
                _buildStatisticItem(
                  title: 'Expire bientôt',
                  value: statistics.expiringSoonMedicines,
                  icon: Icons.warning,
                  color: AppColors.warning,
                  filter: MedicineFilter.expiringSoon,
                ),
                _buildStatisticItem(
                  title: 'Stock faible',
                  value: statistics.lowStockMedicines,
                  icon: Icons.inventory_2,
                  color: AppColors.warning,
                  filter: MedicineFilter.lowStock,
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Additional statistics row
            Row(
              children: [
                Expanded(
                  child: _buildSmallStatisticItem(
                    title: 'Rupture',
                    value: statistics.outOfStockMedicines,
                    color: AppColors.error,
                    filter: MedicineFilter.outOfStock,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSmallStatisticItem(
                    title: 'Personnalisés',
                    value: statistics.customMedicines,
                    color: AppColors.blue,
                    filter: MedicineFilter.custom,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSmallStatisticItem(
                    title: 'Prescription',
                    value: statistics.prescriptionMedicines,
                    color: AppColors.purple,
                    filter: MedicineFilter.prescription,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticItem({
    required String title,
    required int value,
    required IconData icon,
    required Color color,
    required MedicineFilter filter,
  }) {
    return InkWell(
      onTap: value > 0 ? () => onFilterTap?.call(filter) : null,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                icon,
                size: 20,
                color: color,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    value.toString(),
                    style: AppTextStyles.titleLarge.copyWith(
                      fontWeight: FontWeight.w700,
                      color: color,
                    ),
                  ),
                  Text(
                    title,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.grey600,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSmallStatisticItem({
    required String title,
    required int value,
    required Color color,
    required MedicineFilter filter,
  }) {
    return InkWell(
      onTap: value > 0 ? () => onFilterTap?.call(filter) : null,
      borderRadius: BorderRadius.circular(6),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Text(
              value.toString(),
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w700,
                color: color,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              title,
              style: AppTextStyles.labelSmall.copyWith(
                color: AppColors.grey600,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

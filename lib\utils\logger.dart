import 'package:flutter/foundation.dart';

/// Centralized logging utility for MedyTrack Mobile
/// 
/// Provides consistent logging behavior across the application:
/// - Prints logs in debug builds only
/// - Completely silent in release builds
/// - Supports optional tags, errors, and stack traces
class AppLogger {
  static const String _appTag = 'MedyTrack';

  /// Log a message with optional tag, error, and stack trace
  /// 
  /// [message] - The log message to display
  /// [tag] - Optional tag to categorize the log (defaults to 'INFO')
  /// [error] - Optional error object to include
  /// [stackTrace] - Optional stack trace to include
  static void log(
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
  }) {
    // Only log in debug builds - completely silent in release
    if (kDebugMode) {
      final timestamp = DateTime.now().toIso8601String().substring(11, 23);
      final logTag = tag ?? 'INFO';
      final logMessage = '[$timestamp] [$_appTag:$logTag] $message';
      
      print(logMessage);
      
      if (error != null) {
        print('  Error: $error');
      }
      
      if (stackTrace != null) {
        print('  Stack: ${stackTrace.toString().split('\n').take(5).join('\n')}');
      }
    }
  }

  /// Log an authentication-related message
  static void auth(String message, {Object? error, StackTrace? stackTrace}) {
    log(message, tag: 'AUTH', error: error, stackTrace: stackTrace);
  }

  /// Log a BLoC-related message
  static void bloc(String message, {Object? error, StackTrace? stackTrace}) {
    log(message, tag: 'BLOC', error: error, stackTrace: stackTrace);
  }

  /// Log a repository operation
  static void repository(String message, {Object? error, StackTrace? stackTrace}) {
    log(message, tag: 'REPO', error: error, stackTrace: stackTrace);
  }

  /// Log a database operation
  static void database(String message, {Object? error, StackTrace? stackTrace}) {
    log(message, tag: 'DB', error: error, stackTrace: stackTrace);
  }

  /// Log a UI-related message
  static void ui(String message, {Object? error, StackTrace? stackTrace}) {
    log(message, tag: 'UI', error: error, stackTrace: stackTrace);
  }

  /// Log an error with full context
  static void error(String message, {Object? error, StackTrace? stackTrace}) {
    log(message, tag: 'ERROR', error: error, stackTrace: stackTrace);
  }

  /// Log a warning message
  static void warning(String message, {Object? error, StackTrace? stackTrace}) {
    log(message, tag: 'WARNING', error: error, stackTrace: stackTrace);
  }

  /// Log a network-related message
  static void network(String message, {Object? error, StackTrace? stackTrace}) {
    log(message, tag: 'NETWORK', error: error, stackTrace: stackTrace);
  }
}

/// Extension for easy logging in any class
extension AppLogging on Object {
  void logInfo(String message, {Object? error, StackTrace? stackTrace}) {
    AppLogger.log('${runtimeType}: $message', error: error, stackTrace: stackTrace);
  }
  
  void logError(String message, {Object? error, StackTrace? stackTrace}) {
    AppLogger.error('${runtimeType}: $message', error: error, stackTrace: stackTrace);
  }
  
  void logWarning(String message, {Object? error, StackTrace? stackTrace}) {
    AppLogger.warning('${runtimeType}: $message', error: error, stackTrace: stackTrace);
  }
}

import 'package:equatable/equatable.dart';
import '../../../domain/entities/user.dart';
import '../../../domain/entities/settings.dart';

/// Base class for profile security states
abstract class ProfileSecurityState extends Equatable {
  const ProfileSecurityState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class ProfileSecurityInitial extends ProfileSecurityState {
  const ProfileSecurityInitial();
}

/// Loading state
class ProfileSecurityLoading extends ProfileSecurityState {
  const ProfileSecurityLoading();
}

/// Loaded state with profile and security data
class ProfileSecurityLoaded extends ProfileSecurityState {
  final User user;
  final String? avatarUrl;
  final SecuritySettings securitySettings;
  final bool isBiometricAvailable;

  const ProfileSecurityLoaded({
    required this.user,
    this.avatarUrl,
    required this.securitySettings,
    this.isBiometricAvailable = false,
  });

  @override
  List<Object?> get props => [user, avatarUrl, securitySettings, isBiometricAvailable];

  /// Copy with method for state updates
  ProfileSecurityLoaded copyWith({
    User? user,
    String? avatarUrl,
    SecuritySettings? securitySettings,
    bool? isBiometricAvailable,
  }) {
    return ProfileSecurityLoaded(
      user: user ?? this.user,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      securitySettings: securitySettings ?? this.securitySettings,
      isBiometricAvailable: isBiometricAvailable ?? this.isBiometricAvailable,
    );
  }
}

/// Error state
class ProfileSecurityError extends ProfileSecurityState {
  final String message;
  final User? user;
  final SecuritySettings? securitySettings;

  const ProfileSecurityError({
    required this.message,
    this.user,
    this.securitySettings,
  });

  @override
  List<Object?> get props => [message, user, securitySettings];
}

/// Success state for operations
class ProfileSecurityOperationSuccess extends ProfileSecurityState {
  final String message;
  final User user;
  final String? avatarUrl;
  final SecuritySettings securitySettings;

  const ProfileSecurityOperationSuccess({
    required this.message,
    required this.user,
    this.avatarUrl,
    required this.securitySettings,
  });

  @override
  List<Object?> get props => [message, user, avatarUrl, securitySettings];
}

/// State for updating profile
class ProfileUpdating extends ProfileSecurityState {
  final User user;
  final String section; // 'name', 'email', 'avatar', 'password'

  const ProfileUpdating({
    required this.user,
    required this.section,
  });

  @override
  List<Object?> get props => [user, section];
}

/// State for updating security settings
class SecuritySettingsUpdating extends ProfileSecurityState {
  final User user;
  final SecuritySettings securitySettings;
  final String section; // 'biometric', 'pin', 'autolock', 'session'

  const SecuritySettingsUpdating({
    required this.user,
    required this.securitySettings,
    required this.section,
  });

  @override
  List<Object?> get props => [user, securitySettings, section];
}

/// State for password validation
class PasswordValidationResult extends ProfileSecurityState {
  final bool isValid;
  final String? message;
  final User user;

  const PasswordValidationResult({
    required this.isValid,
    this.message,
    required this.user,
  });

  @override
  List<Object?> get props => [isValid, message, user];
}

/// State for password strength check
class PasswordStrengthResult extends ProfileSecurityState {
  final int strength; // 0-4 (weak to strong)
  final String message;
  final List<String> suggestions;
  final User user;

  const PasswordStrengthResult({
    required this.strength,
    required this.message,
    required this.suggestions,
    required this.user,
  });

  @override
  List<Object?> get props => [strength, message, suggestions, user];
}

/// State for avatar upload progress
class AvatarUploading extends ProfileSecurityState {
  final User user;
  final double progress; // 0.0 to 1.0

  const AvatarUploading({
    required this.user,
    required this.progress,
  });

  @override
  List<Object?> get props => [user, progress];
}

/// State for biometric setup
class BiometricSetupResult extends ProfileSecurityState {
  final bool isEnabled;
  final bool isAvailable;
  final String? message;
  final User user;
  final SecuritySettings securitySettings;

  const BiometricSetupResult({
    required this.isEnabled,
    required this.isAvailable,
    this.message,
    required this.user,
    required this.securitySettings,
  });

  @override
  List<Object?> get props => [isEnabled, isAvailable, message, user, securitySettings];
}

/// State for PIN setup
class PinSetupResult extends ProfileSecurityState {
  final bool isEnabled;
  final bool isValid;
  final String? message;
  final User user;
  final SecuritySettings securitySettings;

  const PinSetupResult({
    required this.isEnabled,
    required this.isValid,
    this.message,
    required this.user,
    required this.securitySettings,
  });

  @override
  List<Object?> get props => [isEnabled, isValid, message, user, securitySettings];
}

/// State for account deletion confirmation
class AccountDeletionConfirmation extends ProfileSecurityState {
  final User user;
  final bool isPasswordValid;

  const AccountDeletionConfirmation({
    required this.user,
    required this.isPasswordValid,
  });

  @override
  List<Object?> get props => [user, isPasswordValid];
}

/// State for account deletion success
class AccountDeletionSuccess extends ProfileSecurityState {
  const AccountDeletionSuccess();
}

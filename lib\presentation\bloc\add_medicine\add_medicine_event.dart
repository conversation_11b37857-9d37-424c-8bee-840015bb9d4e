import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import '../../../domain/entities/tunisia_medicine.dart';
import '../../../domain/entities/tag.dart';
import '../../../domain/entities/location.dart';
import '../../../domain/entities/family_member.dart';

/// Events for AddMedicineBloc
abstract class AddMedicineEvent extends Equatable {
  const AddMedicineEvent();

  @override
  List<Object?> get props => [];
}

/// Initialize the add medicine flow
class AddMedicineInitialized extends AddMedicineEvent {
  final String householdId;

  const AddMedicineInitialized({required this.householdId});

  @override
  List<Object?> get props => [householdId];
}

/// Search for medicines in tunisia_medicines table
class MedicinesSearched extends AddMedicineEvent {
  final String query;

  const MedicinesSearched({required this.query});

  @override
  List<Object?> get props => [query];
}

/// Clear search results
class SearchCleared extends AddMedicineEvent {
  const SearchCleared();
}

/// Select a medicine from search results
class MedicineSelected extends AddMedicineEvent {
  final TunisiaMedicine medicine;

  const MedicineSelected({required this.medicine});

  @override
  List<Object?> get props => [medicine];
}

class MedicineNameSelected extends AddMedicineEvent {
  final String medicineName;

  const MedicineNameSelected({required this.medicineName});

  @override
  List<Object> get props => [medicineName];
}

class DosageFormSelected extends AddMedicineEvent {
  final TunisiaMedicine medicine;

  const DosageFormSelected({required this.medicine});

  @override
  List<Object> get props => [medicine];
}

/// Toggle between database search and custom entry modes
class ModeToggled extends AddMedicineEvent {
  final bool isCustomMode;
  final String?
      customName; // Optional custom name to preserve when switching to custom mode

  const ModeToggled({required this.isCustomMode, this.customName});

  @override
  List<Object?> get props => [isCustomMode, customName];
}

/// Update form field values
class FormFieldUpdated extends AddMedicineEvent {
  final String field;
  final dynamic value;

  const FormFieldUpdated({required this.field, required this.value});

  @override
  List<Object?> get props => [field, value];
}

/// Load available tags for the household
class TagsLoaded extends AddMedicineEvent {
  const TagsLoaded();
}

/// Select/deselect a tag
class TagToggled extends AddMedicineEvent {
  final Tag tag;

  const TagToggled({required this.tag});

  @override
  List<Object?> get props => [tag];
}

/// Create a new tag
class TagCreated extends AddMedicineEvent {
  final String name;
  final String color;
  final String category;

  const TagCreated({
    required this.name,
    required this.color,
    required this.category,
  });

  @override
  List<Object?> get props => [name, color, category];
}

/// Load available locations for the household
class LocationsLoaded extends AddMedicineEvent {
  const LocationsLoaded();
}

/// Select a location
class LocationSelected extends AddMedicineEvent {
  final Location? location;

  const LocationSelected({this.location});

  @override
  List<Object?> get props => [location];
}

/// Load available family members for the household
class FamilyMembersLoaded extends AddMedicineEvent {
  const FamilyMembersLoaded();
}

/// Load both locations and family members together (batched)
class LocationsAndFamilyMembersLoaded extends AddMedicineEvent {
  const LocationsAndFamilyMembersLoaded();
}

/// Select a family member
class FamilyMemberSelected extends AddMedicineEvent {
  final FamilyMember? member;

  const FamilyMemberSelected({this.member});

  @override
  List<Object?> get props => [member];
}

/// Validate the current form
class FormValidated extends AddMedicineEvent {
  const FormValidated();
}

/// Submit the medicine form
class MedicineSubmitted extends AddMedicineEvent {
  final BuildContext context;

  const MedicineSubmitted({required this.context});

  @override
  List<Object?> get props => [context];
}

/// Reset the form to initial state
class FormReset extends AddMedicineEvent {
  const FormReset();
}

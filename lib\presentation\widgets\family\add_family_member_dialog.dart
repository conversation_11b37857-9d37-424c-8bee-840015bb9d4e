import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/entities/family_member.dart';
import '../../bloc/family_manager/family_manager_bloc.dart';
import '../../bloc/family_manager/family_manager_event.dart';
import '../../bloc/family_manager/family_manager_state.dart';

class AddFamilyMemberDialog extends StatefulWidget {
  final String householdId;
  final FamilyMember? member; // For editing

  const AddFamilyMemberDialog({
    super.key,
    required this.householdId,
    this.member,
  });

  @override
  State<AddFamilyMemberDialog> createState() => _AddFamilyMemberDialogState();
}

class _AddFamilyMemberDialogState extends State<AddFamilyMemberDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();

  String? _selectedRelationship;
  DateTime? _selectedDateOfBirth;

  bool get _isEditing => widget.member != null;

  // Predefined relationships
  static const List<String> _relationships = [
    'moi',
    'parent',
    'père',
    'mère',
    'enfant',
    'fils',
    'fille',
    'conjoint',
    'époux',
    'épouse',
    'frère',
    'sœur',
    'grand-parent',
    'grand-père',
    'grand-mère',
    'petit-enfant',
    'petit-fils',
    'petite-fille',
    'autre',
  ];

  @override
  void initState() {
    super.initState();

    if (_isEditing) {
      _nameController.text = widget.member!.name;
      // Validate that the relationship exists in our predefined list
      final memberRelationship = widget.member!.relationship?.toLowerCase();
      if (memberRelationship != null &&
          _relationships.contains(memberRelationship)) {
        _selectedRelationship = memberRelationship;
      } else {
        // If relationship doesn't exist in list, default to 'autre' (other)
        _selectedRelationship = 'autre';
      }
      _selectedDateOfBirth = widget.member!.dateOfBirth;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<FamilyManagerBloc, FamilyManagerState>(
      listener: (context, state) {
        if (state is FamilyManagerOperationSuccess) {
          Navigator.of(context).pop();
        } else if (state is FamilyManagerError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppColors.error,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
      child: AlertDialog(
        title: Text(_isEditing ? 'Modifier le membre' : 'Nouveau membre'),
        content: SizedBox(
          width: MediaQuery.of(context).size.width * 0.9,
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name field
                  TextFormField(
                    controller: _nameController,
                    decoration: const InputDecoration(
                      labelText: 'Nom complet *',
                      hintText: 'Ex: Jean Dupont',
                      prefixIcon: Icon(Icons.person),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Le nom est requis';
                      }
                      if (value.trim().length < 2) {
                        return 'Le nom doit contenir au moins 2 caractères';
                      }
                      if (value.trim().length > 50) {
                        return 'Le nom ne peut pas dépasser 50 caractères';
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 16),

                  // Relationship dropdown
                  DropdownButtonFormField<String>(
                    value: _selectedRelationship != null &&
                            _relationships
                                .contains(_selectedRelationship!.toLowerCase())
                        ? _selectedRelationship!.toLowerCase()
                        : null,
                    decoration: const InputDecoration(
                      labelText: 'Relation familiale',
                      prefixIcon: Icon(Icons.family_restroom),
                    ),
                    items: _relationships.toSet().map((relationship) {
                      return DropdownMenuItem(
                        value: relationship,
                        child: Text(_getRelationshipDisplayName(relationship)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedRelationship = value;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  // Date of birth field
                  InkWell(
                    onTap: () => _selectDateOfBirth(context),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'Date de naissance',
                        prefixIcon: Icon(Icons.cake),
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                      child: Text(
                        _selectedDateOfBirth != null
                            ? _formatDate(_selectedDateOfBirth!)
                            : 'Sélectionner une date',
                        style: _selectedDateOfBirth != null
                            ? null
                            : TextStyle(color: AppColors.grey500),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: _saveMember,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.teal,
              foregroundColor: Colors.white,
            ),
            child: Text(_isEditing ? 'Modifier' : 'Créer'),
          ),
        ],
      ),
    );
  }

  void _selectDateOfBirth(BuildContext context) async {
    final now = DateTime.now();
    final initialDate =
        _selectedDateOfBirth ?? now.subtract(const Duration(days: 365 * 30));

    final date = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(1900),
      lastDate: now,
      locale: const Locale('fr', 'FR'),
    );

    if (date != null) {
      setState(() {
        _selectedDateOfBirth = date;
      });
    }
  }

  void _saveMember() {
    if (_formKey.currentState!.validate()) {
      final member = FamilyMember(
        id: _isEditing ? widget.member!.id : '',
        householdId: widget.householdId,
        name: _nameController.text.trim(),
        relationship: _selectedRelationship,
        dateOfBirth: _selectedDateOfBirth,
        createdAt: _isEditing ? widget.member!.createdAt : DateTime.now(),
        updatedAt: _isEditing ? DateTime.now() : null,
      );

      if (_isEditing) {
        context
            .read<FamilyManagerBloc>()
            .add(FamilyMemberUpdated(member: member));
      } else {
        context
            .read<FamilyManagerBloc>()
            .add(FamilyMemberCreated(member: member));
      }
    }
  }

  String _getRelationshipDisplayName(String relationship) {
    switch (relationship.toLowerCase()) {
      case 'moi':
        return 'Moi';
      case 'parent':
        return 'Parent';
      case 'père':
        return 'Père';
      case 'mère':
        return 'Mère';
      case 'enfant':
        return 'Enfant';
      case 'fils':
        return 'Fils';
      case 'fille':
        return 'Fille';
      case 'conjoint':
        return 'Conjoint(e)';
      case 'époux':
        return 'Époux';
      case 'épouse':
        return 'Épouse';
      case 'frère':
        return 'Frère';
      case 'sœur':
        return 'Sœur';
      case 'grand-parent':
        return 'Grand-parent';
      case 'grand-père':
        return 'Grand-père';
      case 'grand-mère':
        return 'Grand-mère';
      case 'petit-enfant':
        return 'Petit-enfant';
      case 'petit-fils':
        return 'Petit-fils';
      case 'petite-fille':
        return 'Petite-fille';
      case 'autre':
        return 'Autre';
      default:
        return relationship.isNotEmpty
            ? relationship[0].toUpperCase() + relationship.substring(1)
            : 'Non spécifié';
    }
  }

  String _formatDate(DateTime date) {
    final months = [
      'janvier',
      'février',
      'mars',
      'avril',
      'mai',
      'juin',
      'juillet',
      'août',
      'septembre',
      'octobre',
      'novembre',
      'décembre'
    ];

    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }
}

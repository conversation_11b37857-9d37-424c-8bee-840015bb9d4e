import 'package:equatable/equatable.dart';

/// Settings entity representing user preferences and app configuration
class Settings extends Equatable {
  final String userId;
  final String householdId;
  final NotificationSettings notifications;
  final AppSettings app;
  final SecuritySettings security;
  final PersonalizationSettings personalization;
  final DateTime? updatedAt;

  const Settings({
    required this.userId,
    required this.householdId,
    required this.notifications,
    required this.app,
    required this.security,
    required this.personalization,
    this.updatedAt,
  });

  /// Create default settings for a new user
  factory Settings.defaultSettings({
    required String userId,
    required String householdId,
  }) {
    return Settings(
      userId: userId,
      householdId: householdId,
      notifications: const NotificationSettings.defaultSettings(),
      app: const AppSettings.defaultSettings(),
      security: const SecuritySettings.defaultSettings(),
      personalization: const PersonalizationSettings.defaultSettings(),
    );
  }

  /// Copy with method for immutable updates
  Settings copyWith({
    String? userId,
    String? householdId,
    NotificationSettings? notifications,
    AppSettings? app,
    SecuritySettings? security,
    PersonalizationSettings? personalization,
    DateTime? updatedAt,
  }) {
    return Settings(
      userId: userId ?? this.userId,
      householdId: householdId ?? this.householdId,
      notifications: notifications ?? this.notifications,
      app: app ?? this.app,
      security: security ?? this.security,
      personalization: personalization ?? this.personalization,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        userId,
        householdId,
        notifications,
        app,
        security,
        personalization,
        updatedAt,
      ];
}

/// Notification settings entity
class NotificationSettings extends Equatable {
  final bool expiryAlerts;
  final bool lowStockAlerts;
  final bool medicationReminders;
  final bool pushNotifications;
  final bool emailNotifications;
  final int expiryWarningDays;
  final int lowStockThreshold;

  const NotificationSettings({
    required this.expiryAlerts,
    required this.lowStockAlerts,
    required this.medicationReminders,
    required this.pushNotifications,
    required this.emailNotifications,
    required this.expiryWarningDays,
    required this.lowStockThreshold,
  });

  /// Default notification settings
  const NotificationSettings.defaultSettings()
      : expiryAlerts = true,
        lowStockAlerts = true,
        medicationReminders = false,
        pushNotifications = true,
        emailNotifications = false,
        expiryWarningDays = 30,
        lowStockThreshold = 5;

  NotificationSettings copyWith({
    bool? expiryAlerts,
    bool? lowStockAlerts,
    bool? medicationReminders,
    bool? pushNotifications,
    bool? emailNotifications,
    int? expiryWarningDays,
    int? lowStockThreshold,
  }) {
    return NotificationSettings(
      expiryAlerts: expiryAlerts ?? this.expiryAlerts,
      lowStockAlerts: lowStockAlerts ?? this.lowStockAlerts,
      medicationReminders: medicationReminders ?? this.medicationReminders,
      pushNotifications: pushNotifications ?? this.pushNotifications,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      expiryWarningDays: expiryWarningDays ?? this.expiryWarningDays,
      lowStockThreshold: lowStockThreshold ?? this.lowStockThreshold,
    );
  }

  @override
  List<Object?> get props => [
        expiryAlerts,
        lowStockAlerts,
        medicationReminders,
        pushNotifications,
        emailNotifications,
        expiryWarningDays,
        lowStockThreshold,
      ];
}

/// App settings entity
class AppSettings extends Equatable {
  final String language;
  final bool isDarkMode;
  final String dateFormat;
  final bool offlineMode;
  final bool autoSync;
  final bool analyticsEnabled;

  const AppSettings({
    required this.language,
    required this.isDarkMode,
    required this.dateFormat,
    required this.offlineMode,
    required this.autoSync,
    required this.analyticsEnabled,
  });

  /// Default app settings
  const AppSettings.defaultSettings()
      : language = 'fr',
        isDarkMode = false,
        dateFormat = 'DD/MM/YYYY',
        offlineMode = true,
        autoSync = true,
        analyticsEnabled = false;

  AppSettings copyWith({
    String? language,
    bool? isDarkMode,
    String? dateFormat,
    bool? offlineMode,
    bool? autoSync,
    bool? analyticsEnabled,
  }) {
    return AppSettings(
      language: language ?? this.language,
      isDarkMode: isDarkMode ?? this.isDarkMode,
      dateFormat: dateFormat ?? this.dateFormat,
      offlineMode: offlineMode ?? this.offlineMode,
      autoSync: autoSync ?? this.autoSync,
      analyticsEnabled: analyticsEnabled ?? this.analyticsEnabled,
    );
  }

  @override
  List<Object?> get props => [
        language,
        isDarkMode,
        dateFormat,
        offlineMode,
        autoSync,
        analyticsEnabled,
      ];
}

/// Security settings entity
class SecuritySettings extends Equatable {
  final bool biometricEnabled;
  final bool pinEnabled;
  final String? pinCode;
  final bool autoLockEnabled;
  final int autoLockMinutes;
  final bool sessionTimeout;
  final int sessionTimeoutMinutes;

  const SecuritySettings({
    required this.biometricEnabled,
    required this.pinEnabled,
    this.pinCode,
    required this.autoLockEnabled,
    required this.autoLockMinutes,
    required this.sessionTimeout,
    required this.sessionTimeoutMinutes,
  });

  /// Default security settings
  const SecuritySettings.defaultSettings()
      : biometricEnabled = false,
        pinEnabled = false,
        pinCode = null,
        autoLockEnabled = false,
        autoLockMinutes = 5,
        sessionTimeout = false,
        sessionTimeoutMinutes = 30;

  SecuritySettings copyWith({
    bool? biometricEnabled,
    bool? pinEnabled,
    String? pinCode,
    bool? autoLockEnabled,
    int? autoLockMinutes,
    bool? sessionTimeout,
    int? sessionTimeoutMinutes,
  }) {
    return SecuritySettings(
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      pinEnabled: pinEnabled ?? this.pinEnabled,
      pinCode: pinCode ?? this.pinCode,
      autoLockEnabled: autoLockEnabled ?? this.autoLockEnabled,
      autoLockMinutes: autoLockMinutes ?? this.autoLockMinutes,
      sessionTimeout: sessionTimeout ?? this.sessionTimeout,
      sessionTimeoutMinutes: sessionTimeoutMinutes ?? this.sessionTimeoutMinutes,
    );
  }

  @override
  List<Object?> get props => [
        biometricEnabled,
        pinEnabled,
        pinCode,
        autoLockEnabled,
        autoLockMinutes,
        sessionTimeout,
        sessionTimeoutMinutes,
      ];
}

/// Personalization settings entity
class PersonalizationSettings extends Equatable {
  final int minimumExpiryThreshold;
  final String defaultLocation;
  final String defaultFamilyMember;
  final List<String> favoriteCategories;
  final bool showExpiredMedicines;
  final bool groupByLocation;

  const PersonalizationSettings({
    required this.minimumExpiryThreshold,
    required this.defaultLocation,
    required this.defaultFamilyMember,
    required this.favoriteCategories,
    required this.showExpiredMedicines,
    required this.groupByLocation,
  });

  /// Default personalization settings
  const PersonalizationSettings.defaultSettings()
      : minimumExpiryThreshold = 30,
        defaultLocation = '',
        defaultFamilyMember = '',
        favoriteCategories = const [],
        showExpiredMedicines = true,
        groupByLocation = false;

  PersonalizationSettings copyWith({
    int? minimumExpiryThreshold,
    String? defaultLocation,
    String? defaultFamilyMember,
    List<String>? favoriteCategories,
    bool? showExpiredMedicines,
    bool? groupByLocation,
  }) {
    return PersonalizationSettings(
      minimumExpiryThreshold: minimumExpiryThreshold ?? this.minimumExpiryThreshold,
      defaultLocation: defaultLocation ?? this.defaultLocation,
      defaultFamilyMember: defaultFamilyMember ?? this.defaultFamilyMember,
      favoriteCategories: favoriteCategories ?? this.favoriteCategories,
      showExpiredMedicines: showExpiredMedicines ?? this.showExpiredMedicines,
      groupByLocation: groupByLocation ?? this.groupByLocation,
    );
  }

  @override
  List<Object?> get props => [
        minimumExpiryThreshold,
        defaultLocation,
        defaultFamilyMember,
        favoriteCategories,
        showExpiredMedicines,
        groupByLocation,
      ];
}

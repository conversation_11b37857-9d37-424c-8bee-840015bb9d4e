import 'package:dartz/dartz.dart';
import '../../entities/dashboard_stats.dart';
import '../../repositories/dashboard_repository.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';

class GetDashboardStatsUseCase implements UseCase<DashboardStats, HouseholdParams> {
  final DashboardRepository repository;

  GetDashboardStatsUseCase(this.repository);

  @override
  Future<Either<Failure, DashboardStats>> call(HouseholdParams params) async {
    return await repository.getDashboardStats(params.householdId);
  }
}

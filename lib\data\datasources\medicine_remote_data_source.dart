import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/medicine_model.dart';
import '../../core/utils/supabase_utils.dart';

abstract class MedicineRemoteDataSource {
  Future<List<MedicineModel>> getMedicines(String householdId);
  Future<MedicineModel> getMedicineById(String medicineId);
  Future<List<MedicineModel>> searchMedicines(String householdId, String query);
  Future<List<MedicineModel>> getRecentMedicines(String householdId,
      {int limit = 5});
  Future<MedicineModel> addMedicine(MedicineModel medicine);
  Future<void> deleteMedicine(String medicineId);
  Future<MedicineModel> updateMedicine(MedicineModel medicine);
}

class MedicineRemoteDataSourceImpl implements MedicineRemoteDataSource {
  final SupabaseClient supabaseClient;

  MedicineRemoteDataSourceImpl({required this.supabaseClient});

  @override
  Future<List<MedicineModel>> getMedicines(String householdId) async {
    try {
      // Validate household ID
      if (!SupabaseUtils.isValidUUID(householdId)) {
        throw Exception('Invalid household ID format: $householdId');
      }

      final response = await supabaseClient.from('user_medicines').select('''
            *,
            tunisia_medicines(nom, dci, dosage, forme, laboratoire, classe, sous_classe, amm),
            family_members(name)
          ''').eq('household_id', householdId);

      // Get locations separately
      final locationsResponse = await supabaseClient
          .from('locations')
          .select('id, name')
          .eq('household_id', householdId);

      // Create location lookup map
      final locationMap = <String, String>{};
      for (final location in locationsResponse) {
        locationMap[location['id'] as String] = location['name'] as String;
      }

      return response.map<MedicineModel>((json) {
        // Add location name if location exists
        if (json['location'] != null &&
            locationMap.containsKey(json['location'])) {
          json['location_name'] = locationMap[json['location']];
        }
        return MedicineModel.fromJson(json);
      }).toList();
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }

  @override
  Future<MedicineModel> getMedicineById(String medicineId) async {
    try {
      // Validate medicine ID
      if (!SupabaseUtils.isValidUUID(medicineId)) {
        throw Exception('Invalid medicine ID format: $medicineId');
      }

      debugPrint('Fetching medicine by ID: $medicineId');

      final response = await supabaseClient.from('user_medicines').select('''
            *,
            tunisia_medicines(nom, dci, dosage, forme, laboratoire, classe, sous_classe, amm),
            family_members(name)
          ''').eq('id', medicineId).single();

      // Get locations separately
      final locationsResponse = await supabaseClient
          .from('locations')
          .select('id, name')
          .eq('household_id', response['household_id']);

      final locationsMap = <String, String>{};
      for (final location in locationsResponse) {
        locationsMap[location['id']] = location['name'];
      }

      // Convert to MedicineModel
      final medicineData = Map<String, dynamic>.from(response);
      medicineData['location_name'] = medicineData['location_id'] != null
          ? locationsMap[medicineData['location_id']]
          : null;

      return MedicineModel.fromJson(medicineData);
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }

  @override
  Future<List<MedicineModel>> searchMedicines(
      String householdId, String query) async {
    try {
      // Validate household ID
      if (!SupabaseUtils.isValidUUID(householdId)) {
        throw Exception('Invalid household ID format: $householdId');
      }

      // Validate search query
      if (query.trim().isEmpty) {
        return getMedicines(householdId);
      }

      // Search in custom names first
      final customNameResponse = await supabaseClient
          .from('user_medicines')
          .select('''
            *,
            tunisia_medicines(nom, dci, dosage, forme, laboratoire, classe, sous_classe, amm),
            family_members(name)
          ''')
          .eq('household_id', householdId)
          .ilike('custom_name', '%$query%')
          .limit(10);

      // Search in tunisia medicines by joining
      final tunisiaMedicineResponse = await supabaseClient
          .from('user_medicines')
          .select('''
            *,
            tunisia_medicines!inner(nom, dci, dosage, forme, laboratoire, classe, sous_classe, amm),
            family_members(name)
          ''')
          .eq('household_id', householdId)
          .or('tunisia_medicines.nom.ilike.%$query%,tunisia_medicines.dci.ilike.%$query%,tunisia_medicines.laboratoire.ilike.%$query%')
          .limit(10);

      // Combine results and remove duplicates
      final Set<String> seenIds = {};
      final List<dynamic> combinedResults = [];

      for (final item in customNameResponse) {
        final id = item['id'] as String;
        if (!seenIds.contains(id)) {
          seenIds.add(id);
          combinedResults.add(item);
        }
      }

      for (final item in tunisiaMedicineResponse) {
        final id = item['id'] as String;
        if (!seenIds.contains(id)) {
          seenIds.add(id);
          combinedResults.add(item);
        }
      }

      final response = combinedResults;

      // Get locations separately
      final locationsResponse = await supabaseClient
          .from('locations')
          .select('id, name')
          .eq('household_id', householdId);

      // Create location lookup map
      final locationMap = <String, String>{};
      for (final location in locationsResponse) {
        locationMap[location['id'] as String] = location['name'] as String;
      }

      return response.map<MedicineModel>((json) {
        // Add location name if location exists
        if (json['location'] != null &&
            locationMap.containsKey(json['location'])) {
          json['location_name'] = locationMap[json['location']];
        }
        return MedicineModel.fromJson(json);
      }).toList();
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }

  @override
  Future<List<MedicineModel>> getRecentMedicines(String householdId,
      {int limit = 5}) async {
    try {
      // Validate household ID
      if (!SupabaseUtils.isValidUUID(householdId)) {
        throw Exception('Invalid household ID format: $householdId');
      }

      final response = await supabaseClient
          .from('user_medicines')
          .select('''
            *,
            tunisia_medicines(nom, dci, dosage, forme, laboratoire, classe, sous_classe, amm),
            family_members(name)
          ''')
          .eq('household_id', householdId)
          .order('created_at', ascending: false)
          .limit(limit);

      // Get locations separately
      final locationsResponse = await supabaseClient
          .from('locations')
          .select('id, name')
          .eq('household_id', householdId);

      // Create location lookup map
      final locationMap = <String, String>{};
      for (final location in locationsResponse) {
        locationMap[location['id'] as String] = location['name'] as String;
      }

      return response.map<MedicineModel>((json) {
        // Add location name if location exists
        if (json['location'] != null &&
            locationMap.containsKey(json['location'])) {
          json['location_name'] = locationMap[json['location']];
        }
        return MedicineModel.fromJson(json);
      }).toList();
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }

  @override
  Future<MedicineModel> addMedicine(MedicineModel medicine) async {
    try {
      // Prepare medicine data for insertion
      final medicineData = medicine.toJson();

      // Validate required parameters
      final requiredParams = {
        'household_id': medicineData['household_id'],
      };

      if (!SupabaseUtils.validateRequiredParams(requiredParams)) {
        throw Exception('Missing required parameters for medicine creation');
      }

      // Remove fields that shouldn't be inserted directly
      medicineData.remove('tags'); // Tags will be handled separately
      medicineData.remove('id'); // Let Supabase generate the ID

      // Insert medicine into user_medicines table
      final response = await supabaseClient
          .from('user_medicines')
          .insert(medicineData)
          .select()
          .single();

      final insertedMedicine = MedicineModel.fromJson(response);

      // Handle tag associations if tags are provided
      if (medicine.tags.isNotEmpty) {
        await _associateTags(
            insertedMedicine.id, medicine.tags, medicineData['household_id']);
      }

      return insertedMedicine;
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }

  /// Associate tags with a medicine
  Future<void> _associateTags(
      String medicineId, List<String> tagNames, String householdId) async {
    try {
      for (final tagName in tagNames) {
        // Find existing tag by name
        final tagResponse = await supabaseClient
            .from('tags')
            .select('id')
            .eq('name', tagName)
            .maybeSingle();

        String tagId;
        if (tagResponse != null) {
          tagId = tagResponse['id'] as String;
        } else {
          // Tag doesn't exist, create it with proper household ID
          final newTagResponse = await supabaseClient
              .from('tags')
              .insert({
                'name': tagName,
                'color': '#14B8A6', // Default teal color
                'category': 'therapeutic', // Default category
                'household_id': householdId, // Use provided household ID
              })
              .select('id')
              .single();
          tagId = newTagResponse['id'] as String;
        }

        // Create medicine-tag association
        await supabaseClient.from('medicine_tag_assignments').insert({
          'medicine_id': medicineId,
          'tag_id': tagId,
        });
      }
    } catch (e) {
      // Log error but don't fail the medicine creation
      // Using debugPrint instead of print for better logging
      debugPrint('Warning: Failed to associate tags: ${e.toString()}');
    }
  }

  @override
  Future<void> deleteMedicine(String medicineId) async {
    try {
      debugPrint('Starting deletion process for medicine: $medicineId');

      // Try to delete medicine-tag associations first (if table exists)
      try {
        await supabaseClient
            .from('medicine_tag_assignments')
            .delete()
            .eq('medicine_id', medicineId);
        debugPrint('Medicine-tag associations deleted successfully');
      } catch (tagError) {
        // Log the error but continue with medicine deletion
        debugPrint(
            'Warning: Could not delete medicine-tag associations: ${tagError.toString()}');
        // This might be expected if the junction table doesn't exist in the current schema
      }

      // Delete the medicine
      await supabaseClient.from('user_medicines').delete().eq('id', medicineId);

      debugPrint('Medicine deleted successfully: $medicineId');
    } on PostgrestException catch (e) {
      debugPrint('Supabase error during medicine deletion: ${e.message}');

      // Provide more specific error messages based on Supabase error codes
      if (e.code == 'PGRST116') {
        throw Exception(
            'Médicament introuvable. Il a peut-être déjà été supprimé.');
      } else if (e.code == '23503') {
        throw Exception(
            'Impossible de supprimer ce médicament car il est référencé par d\'autres données.');
      } else if (e.code == '42501') {
        throw Exception(
            'Permissions insuffisantes pour supprimer ce médicament.');
      } else {
        throw Exception('Erreur de base de données: ${e.message}');
      }
    } catch (e) {
      debugPrint('Unexpected error during medicine deletion: ${e.toString()}');

      if (e.toString().contains('network') ||
          e.toString().contains('connection')) {
        throw Exception(
            'Problème de connexion réseau. Vérifiez votre connexion internet.');
      } else if (e.toString().contains('timeout')) {
        throw Exception('Délai d\'attente dépassé. Veuillez réessayer.');
      } else {
        throw Exception(
            'Erreur inattendue lors de la suppression: ${e.toString()}');
      }
    }
  }

  @override
  Future<MedicineModel> updateMedicine(MedicineModel medicine) async {
    try {
      // Prepare medicine data for update
      final medicineData = medicine.toJson();
      medicineData.remove('tags'); // Tags will be handled separately
      medicineData.remove('created_at'); // Don't update creation time
      medicineData['updated_at'] = DateTime.now().toIso8601String();

      // Update medicine in user_medicines table
      final response = await supabaseClient
          .from('user_medicines')
          .update(medicineData)
          .eq('id', medicine.id)
          .select()
          .single();

      final updatedMedicine = MedicineModel.fromJson(response);

      // Update tag associations
      // First, remove existing associations
      await supabaseClient
          .from('medicine_tag_assignments')
          .delete()
          .eq('medicine_id', medicine.id);

      // Then add new associations
      if (medicine.tags.isNotEmpty) {
        await _associateTags(medicine.id, medicine.tags, medicine.householdId);
      }

      return updatedMedicine;
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }
}

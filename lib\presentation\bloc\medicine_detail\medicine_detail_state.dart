import 'package:equatable/equatable.dart';
import '../../../domain/entities/medicine.dart';

abstract class MedicineDetailState extends Equatable {
  const MedicineDetailState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class MedicineDetailInitial extends MedicineDetailState {
  const MedicineDetailInitial();
}

/// Loading state
class MedicineDetailLoading extends MedicineDetailState {
  const MedicineDetailLoading();
}

/// Loaded state with medicine details
class MedicineDetailLoaded extends MedicineDetailState {
  final Medicine medicine;
  final bool isUpdating;

  const MedicineDetailLoaded({
    required this.medicine,
    this.isUpdating = false,
  });

  MedicineDetailLoaded copyWith({
    Medicine? medicine,
    bool? isUpdating,
  }) {
    return MedicineDetailLoaded(
      medicine: medicine ?? this.medicine,
      isUpdating: isUpdating ?? this.isUpdating,
    );
  }

  @override
  List<Object?> get props => [medicine, isUpdating];
}

/// Error state
class MedicineDetailError extends MedicineDetailState {
  final String message;

  const MedicineDetailError({required this.message});

  @override
  List<Object?> get props => [message];
}

/// Success state for operations
class MedicineDetailOperationSuccess extends MedicineDetailState {
  final String message;
  final MedicineDetailOperationType operationType;

  const MedicineDetailOperationSuccess({
    required this.message,
    required this.operationType,
  });

  @override
  List<Object?> get props => [message, operationType];
}

/// Operation types for success feedback
enum MedicineDetailOperationType {
  quantityUpdated,
  medicineTaken,
  medicineDeleted,
}

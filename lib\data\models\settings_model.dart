import '../../domain/entities/settings.dart';

/// Settings model for data layer
class SettingsModel extends Settings {
  const SettingsModel({
    required super.userId,
    required super.householdId,
    required super.notifications,
    required super.app,
    required super.security,
    required super.personalization,
    super.updatedAt,
  });

  /// Create from entity
  factory SettingsModel.fromEntity(Settings settings) {
    return SettingsModel(
      userId: settings.userId,
      householdId: settings.householdId,
      notifications: NotificationSettingsModel.fromEntity(settings.notifications),
      app: AppSettingsModel.fromEntity(settings.app),
      security: SecuritySettingsModel.fromEntity(settings.security),
      personalization: PersonalizationSettingsModel.fromEntity(settings.personalization),
      updatedAt: settings.updatedAt,
    );
  }

  /// Create from JSON
  factory SettingsModel.fromJson(Map<String, dynamic> json) {
    return SettingsModel(
      userId: json['user_id'] ?? '',
      householdId: json['household_id'] ?? '',
      notifications: NotificationSettingsModel.fromJson(json['notifications'] ?? {}),
      app: AppSettingsModel.fromJson(json['app'] ?? {}),
      security: SecuritySettingsModel.fromJson(json['security'] ?? {}),
      personalization: PersonalizationSettingsModel.fromJson(json['personalization'] ?? {}),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'household_id': householdId,
      'notifications': (notifications as NotificationSettingsModel).toJson(),
      'app': (app as AppSettingsModel).toJson(),
      'security': (security as SecuritySettingsModel).toJson(),
      'personalization': (personalization as PersonalizationSettingsModel).toJson(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// Convert to entity
  Settings toEntity() {
    return Settings(
      userId: userId,
      householdId: householdId,
      notifications: notifications,
      app: app,
      security: security,
      personalization: personalization,
      updatedAt: updatedAt,
    );
  }
}

/// Notification settings model
class NotificationSettingsModel extends NotificationSettings {
  const NotificationSettingsModel({
    required super.expiryAlerts,
    required super.lowStockAlerts,
    required super.medicationReminders,
    required super.pushNotifications,
    required super.emailNotifications,
    required super.expiryWarningDays,
    required super.lowStockThreshold,
  });

  factory NotificationSettingsModel.fromEntity(NotificationSettings settings) {
    return NotificationSettingsModel(
      expiryAlerts: settings.expiryAlerts,
      lowStockAlerts: settings.lowStockAlerts,
      medicationReminders: settings.medicationReminders,
      pushNotifications: settings.pushNotifications,
      emailNotifications: settings.emailNotifications,
      expiryWarningDays: settings.expiryWarningDays,
      lowStockThreshold: settings.lowStockThreshold,
    );
  }

  factory NotificationSettingsModel.fromJson(Map<String, dynamic> json) {
    return NotificationSettingsModel(
      expiryAlerts: json['expiry_alerts'] ?? true,
      lowStockAlerts: json['low_stock_alerts'] ?? true,
      medicationReminders: json['medication_reminders'] ?? false,
      pushNotifications: json['push_notifications'] ?? true,
      emailNotifications: json['email_notifications'] ?? false,
      expiryWarningDays: json['expiry_warning_days'] ?? 30,
      lowStockThreshold: json['low_stock_threshold'] ?? 5,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'expiry_alerts': expiryAlerts,
      'low_stock_alerts': lowStockAlerts,
      'medication_reminders': medicationReminders,
      'push_notifications': pushNotifications,
      'email_notifications': emailNotifications,
      'expiry_warning_days': expiryWarningDays,
      'low_stock_threshold': lowStockThreshold,
    };
  }
}

/// App settings model
class AppSettingsModel extends AppSettings {
  const AppSettingsModel({
    required super.language,
    required super.isDarkMode,
    required super.dateFormat,
    required super.offlineMode,
    required super.autoSync,
    required super.analyticsEnabled,
  });

  factory AppSettingsModel.fromEntity(AppSettings settings) {
    return AppSettingsModel(
      language: settings.language,
      isDarkMode: settings.isDarkMode,
      dateFormat: settings.dateFormat,
      offlineMode: settings.offlineMode,
      autoSync: settings.autoSync,
      analyticsEnabled: settings.analyticsEnabled,
    );
  }

  factory AppSettingsModel.fromJson(Map<String, dynamic> json) {
    return AppSettingsModel(
      language: json['language'] ?? 'fr',
      isDarkMode: json['is_dark_mode'] ?? false,
      dateFormat: json['date_format'] ?? 'DD/MM/YYYY',
      offlineMode: json['offline_mode'] ?? true,
      autoSync: json['auto_sync'] ?? true,
      analyticsEnabled: json['analytics_enabled'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'language': language,
      'is_dark_mode': isDarkMode,
      'date_format': dateFormat,
      'offline_mode': offlineMode,
      'auto_sync': autoSync,
      'analytics_enabled': analyticsEnabled,
    };
  }
}

/// Security settings model
class SecuritySettingsModel extends SecuritySettings {
  const SecuritySettingsModel({
    required super.biometricEnabled,
    required super.pinEnabled,
    super.pinCode,
    required super.autoLockEnabled,
    required super.autoLockMinutes,
    required super.sessionTimeout,
    required super.sessionTimeoutMinutes,
  });

  factory SecuritySettingsModel.fromEntity(SecuritySettings settings) {
    return SecuritySettingsModel(
      biometricEnabled: settings.biometricEnabled,
      pinEnabled: settings.pinEnabled,
      pinCode: settings.pinCode,
      autoLockEnabled: settings.autoLockEnabled,
      autoLockMinutes: settings.autoLockMinutes,
      sessionTimeout: settings.sessionTimeout,
      sessionTimeoutMinutes: settings.sessionTimeoutMinutes,
    );
  }

  factory SecuritySettingsModel.fromJson(Map<String, dynamic> json) {
    return SecuritySettingsModel(
      biometricEnabled: json['biometric_enabled'] ?? false,
      pinEnabled: json['pin_enabled'] ?? false,
      pinCode: json['pin_code'],
      autoLockEnabled: json['auto_lock_enabled'] ?? false,
      autoLockMinutes: json['auto_lock_minutes'] ?? 5,
      sessionTimeout: json['session_timeout'] ?? false,
      sessionTimeoutMinutes: json['session_timeout_minutes'] ?? 30,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'biometric_enabled': biometricEnabled,
      'pin_enabled': pinEnabled,
      'pin_code': pinCode,
      'auto_lock_enabled': autoLockEnabled,
      'auto_lock_minutes': autoLockMinutes,
      'session_timeout': sessionTimeout,
      'session_timeout_minutes': sessionTimeoutMinutes,
    };
  }
}

/// Personalization settings model
class PersonalizationSettingsModel extends PersonalizationSettings {
  const PersonalizationSettingsModel({
    required super.minimumExpiryThreshold,
    required super.defaultLocation,
    required super.defaultFamilyMember,
    required super.favoriteCategories,
    required super.showExpiredMedicines,
    required super.groupByLocation,
  });

  factory PersonalizationSettingsModel.fromEntity(PersonalizationSettings settings) {
    return PersonalizationSettingsModel(
      minimumExpiryThreshold: settings.minimumExpiryThreshold,
      defaultLocation: settings.defaultLocation,
      defaultFamilyMember: settings.defaultFamilyMember,
      favoriteCategories: settings.favoriteCategories,
      showExpiredMedicines: settings.showExpiredMedicines,
      groupByLocation: settings.groupByLocation,
    );
  }

  factory PersonalizationSettingsModel.fromJson(Map<String, dynamic> json) {
    return PersonalizationSettingsModel(
      minimumExpiryThreshold: json['minimum_expiry_threshold'] ?? 30,
      defaultLocation: json['default_location'] ?? '',
      defaultFamilyMember: json['default_family_member'] ?? '',
      favoriteCategories: List<String>.from(json['favorite_categories'] ?? []),
      showExpiredMedicines: json['show_expired_medicines'] ?? true,
      groupByLocation: json['group_by_location'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'minimum_expiry_threshold': minimumExpiryThreshold,
      'default_location': defaultLocation,
      'default_family_member': defaultFamilyMember,
      'favorite_categories': favoriteCategories,
      'show_expired_medicines': showExpiredMedicines,
      'group_by_location': groupByLocation,
    };
  }
}

import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/reminder.dart';
import '../../repositories/reminder_repository.dart';

@injectable
class UpdateReminderUseCase implements UseCase<Reminder, UpdateReminderParams> {
  final ReminderRepository repository;

  UpdateReminderUseCase(this.repository);

  @override
  Future<Either<Failure, Reminder>> call(UpdateReminderParams params) async {
    return await repository.updateReminder(params.reminder);
  }
}

class UpdateReminderParams {
  final Reminder reminder;

  UpdateReminderParams({required this.reminder});
}

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/medicine.dart';

class MedicineGridItem extends StatelessWidget {
  final Medicine medicine;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const MedicineGridItem({
    super.key,
    required this.medicine,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isSelected ? 4 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected
            ? BorderSide(color: AppColors.teal, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with selection checkbox and status
              Row(
                children: [
                  if (isSelectionMode)
                    Checkbox(
                      value: isSelected,
                      onChanged: (value) => onTap?.call(),
                      activeColor: AppColors.teal,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                    ),
                  const Spacer(),
                  _buildStatusIndicator(),
                ],
              ),

              // Medicine name
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      medicine.displayName,
                      style: AppTextStyles.titleSmall.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (medicine.dosage != null || medicine.form != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        [medicine.dosage, medicine.form]
                            .where((e) => e != null && e.isNotEmpty)
                            .join(' • '),
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.grey600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),

              const SizedBox(height: 8),

              // Quantity
              Row(
                children: [
                  Icon(
                    Icons.inventory_2,
                    size: 14,
                    color: AppColors.grey500,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${medicine.quantity}',
                    style: AppTextStyles.bodySmall.copyWith(
                      fontWeight: FontWeight.w600,
                      color: medicine.quantity == 0 ? AppColors.error : null,
                    ),
                  ),
                  if (medicine.lowStockThreshold > 0 &&
                      medicine.quantity <= medicine.lowStockThreshold &&
                      medicine.quantity > 0) ...[
                    const SizedBox(width: 4),
                    Icon(
                      Icons.warning,
                      size: 12,
                      color: AppColors.warning,
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 4),

              // Expiration
              if (medicine.expiration != null)
                Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      size: 14,
                      color: AppColors.grey500,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        DateFormat('MMM yyyy').format(medicine.expiration!),
                        style: AppTextStyles.bodySmall.copyWith(
                          color: _getExpirationColor(),
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),

              const SizedBox(height: 4),

              // Location
              if (medicine.locationName != null)
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 14,
                      color: AppColors.grey500,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        medicine.locationName!,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.grey600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),

              // Tags (show only first tag if any)
              if (medicine.tags.isNotEmpty) ...[
                const SizedBox(height: 6),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.teal.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    medicine.tags.first,
                    style: AppTextStyles.labelSmall.copyWith(
                      color: AppColors.teal,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusIndicator() {
    final status = medicine.status;
    Color color;
    IconData icon;

    switch (status) {
      case MedicineStatus.expired:
        color = AppColors.error;
        icon = Icons.error;
        break;
      case MedicineStatus.expiringSoon:
        color = AppColors.warning;
        icon = Icons.warning;
        break;
      case MedicineStatus.lowStock:
        color = AppColors.warning;
        icon = Icons.inventory_2;
        break;
      case MedicineStatus.outOfStock:
        color = AppColors.error;
        icon = Icons.remove_circle;
        break;
      case MedicineStatus.normal:
        return const SizedBox(
            width: 20, height: 20); // Placeholder for alignment
    }

    return Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        icon,
        size: 12,
        color: color,
      ),
    );
  }

  Color _getExpirationColor() {
    if (medicine.expiration == null) return AppColors.grey600;

    final now = DateTime.now();
    final expiration = medicine.expiration!;
    final isExpired = expiration.isBefore(now);
    final daysUntilExpiration = expiration.difference(now).inDays;

    if (isExpired) {
      return AppColors.error;
    } else if (daysUntilExpiration <= 30) {
      return AppColors.warning;
    } else {
      return AppColors.grey600;
    }
  }
}

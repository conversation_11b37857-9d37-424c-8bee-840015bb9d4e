-- Add user_id column to dose_history table for RLS policy compliance
-- This migration fixes the HTTP 403 Forbidden errors by ensuring proper user context

-- Add user_id column to dose_history table
ALTER TABLE public.dose_history 
ADD COLUMN user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;

-- Update existing records to set user_id based on user_medicine relationship
UPDATE public.dose_history 
SET user_id = (
    SELECT um.user_id 
    FROM public.user_medicines um 
    WHERE um.id = dose_history.user_medicine_id
);

-- Make user_id NOT NULL after populating existing records
ALTER TABLE public.dose_history 
ALTER COLUMN user_id SET NOT NULL;

-- Drop existing RLS policies for dose_history
DROP POLICY IF EXISTS "Users can view their own dose history" ON public.dose_history;
DROP POLICY IF EXISTS "Users can insert their own dose history" ON public.dose_history;
DROP POLICY IF EXISTS "Users can update their own dose history" ON public.dose_history;
DROP POLICY IF EXISTS "Users can delete their own dose history" ON public.dose_history;

-- Create improved RLS policies that use both user_id and user_medicine relationship
CREATE POLICY "Users can view their own dose history" ON public.dose_history
    FOR SELECT USING (
        user_id = auth.uid() OR
        user_medicine_id IN (
            SELECT um.id FROM public.user_medicines um
            WHERE um.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own dose history" ON public.dose_history
    FOR INSERT WITH CHECK (
        user_id = auth.uid() AND
        user_medicine_id IN (
            SELECT um.id FROM public.user_medicines um
            WHERE um.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own dose history" ON public.dose_history
    FOR UPDATE USING (
        user_id = auth.uid() AND
        user_medicine_id IN (
            SELECT um.id FROM public.user_medicines um
            WHERE um.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own dose history" ON public.dose_history
    FOR DELETE USING (
        user_id = auth.uid() AND
        user_medicine_id IN (
            SELECT um.id FROM public.user_medicines um
            WHERE um.user_id = auth.uid()
        )
    );

-- Grant necessary permissions (if not already granted)
GRANT ALL ON public.dose_history TO authenticated;

-- Create index for better performance on user_id queries
CREATE INDEX IF NOT EXISTS idx_dose_history_user_id ON public.dose_history(user_id);
CREATE INDEX IF NOT EXISTS idx_dose_history_user_medicine_id ON public.dose_history(user_medicine_id);

-- Verify the migration
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'dose_history' 
    AND table_schema = 'public'
ORDER BY ordinal_position;

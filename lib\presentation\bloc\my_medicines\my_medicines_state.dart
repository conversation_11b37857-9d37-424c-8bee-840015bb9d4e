import 'package:equatable/equatable.dart';

import '../../../domain/entities/medicine.dart';
import 'my_medicines_event.dart';

/// Base class for MyMedicines states
abstract class MyMedicinesState extends Equatable {
  const MyMedicinesState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class MyMedicinesInitial extends MyMedicinesState {
  const MyMedicinesInitial();
}

/// Loading state
class MyMedicinesLoading extends MyMedicinesState {
  const MyMedicinesLoading();
}

/// Loaded state with medicines
class MyMedicinesLoaded extends MyMedicinesState {
  final String householdId;
  final List<Medicine> medicines;
  final List<Medicine> filteredMedicines;
  final String searchQuery;
  final MedicineFilter currentFilter;
  final MedicineSortOption currentSort;
  final MedicineViewMode viewMode;
  final Set<String> selectedMedicineIds;
  final bool isSearching;
  final bool isSelectionMode;

  const MyMedicinesLoaded({
    required this.householdId,
    required this.medicines,
    required this.filteredMedicines,
    this.searchQuery = '',
    this.currentFilter = MedicineFilter.all,
    this.currentSort = MedicineSortOption.nameAsc,
    this.viewMode = MedicineViewMode.list,
    this.selectedMedicineIds = const {},
    this.isSearching = false,
    this.isSelectionMode = false,
  });

  /// Get medicines count
  int get medicinesCount => medicines.length;

  /// Get filtered medicines count
  int get filteredMedicinesCount => filteredMedicines.length;

  /// Get selected medicines count
  int get selectedCount => selectedMedicineIds.length;

  /// Check if medicines are empty
  bool get isEmpty => medicines.isEmpty;

  /// Check if filtered medicines are empty
  bool get isFilteredEmpty => filteredMedicines.isEmpty;

  /// Check if search is active
  bool get hasSearchQuery => searchQuery.isNotEmpty;

  /// Check if filter is active
  bool get hasActiveFilter => currentFilter != MedicineFilter.all;

  /// Check if all filtered medicines are selected
  bool get areAllSelected =>
      filteredMedicines.isNotEmpty &&
      filteredMedicines
          .every((medicine) => selectedMedicineIds.contains(medicine.id));

  /// Get display medicines (filtered)
  List<Medicine> get displayMedicines => filteredMedicines;

  /// Get selected medicines
  List<Medicine> get selectedMedicines => medicines
      .where((medicine) => selectedMedicineIds.contains(medicine.id))
      .toList();

  /// Get medicine statistics
  MedicineStatistics get statistics =>
      MedicineStatistics.fromMedicines(medicines);

  /// Copy with method for immutable updates
  MyMedicinesLoaded copyWith({
    String? householdId,
    List<Medicine>? medicines,
    List<Medicine>? filteredMedicines,
    String? searchQuery,
    MedicineFilter? currentFilter,
    MedicineSortOption? currentSort,
    MedicineViewMode? viewMode,
    Set<String>? selectedMedicineIds,
    bool? isSearching,
    bool? isSelectionMode,
  }) {
    return MyMedicinesLoaded(
      householdId: householdId ?? this.householdId,
      medicines: medicines ?? this.medicines,
      filteredMedicines: filteredMedicines ?? this.filteredMedicines,
      searchQuery: searchQuery ?? this.searchQuery,
      currentFilter: currentFilter ?? this.currentFilter,
      currentSort: currentSort ?? this.currentSort,
      viewMode: viewMode ?? this.viewMode,
      selectedMedicineIds: selectedMedicineIds ?? this.selectedMedicineIds,
      isSearching: isSearching ?? this.isSearching,
      isSelectionMode: isSelectionMode ?? this.isSelectionMode,
    );
  }

  @override
  List<Object?> get props => [
        householdId,
        medicines,
        filteredMedicines,
        searchQuery,
        currentFilter,
        currentSort,
        viewMode,
        selectedMedicineIds,
        isSearching,
        isSelectionMode,
      ];
}

/// Error state
class MyMedicinesError extends MyMedicinesState {
  final String message;

  const MyMedicinesError({required this.message});

  @override
  List<Object?> get props => [message];
}

/// Deletion loading state
class MyMedicinesDeletionLoading extends MyMedicinesState {
  final String message;
  final int totalCount;
  final int currentIndex;

  const MyMedicinesDeletionLoading({
    required this.message,
    required this.totalCount,
    required this.currentIndex,
  });

  @override
  List<Object?> get props => [message, totalCount, currentIndex];
}

/// Success state for operations
class MyMedicinesOperationSuccess extends MyMedicinesState {
  final String message;
  final MyMedicinesOperationType operationType;

  const MyMedicinesOperationSuccess({
    required this.message,
    required this.operationType,
  });

  @override
  List<Object?> get props => [message, operationType];
}

/// Operation types for success messages
enum MyMedicinesOperationType {
  medicineDeleted,
  medicinesDeleted,
  quantityUpdated,
  medicineUpdated,
}

/// Extension for operation type messages
extension MyMedicinesOperationTypeExtension on MyMedicinesOperationType {
  String get message {
    switch (this) {
      case MyMedicinesOperationType.medicineDeleted:
        return 'Médicament supprimé avec succès';
      case MyMedicinesOperationType.medicinesDeleted:
        return 'Médicaments supprimés avec succès';
      case MyMedicinesOperationType.quantityUpdated:
        return 'Quantité mise à jour avec succès';
      case MyMedicinesOperationType.medicineUpdated:
        return 'Médicament mis à jour avec succès';
    }
  }
}

/// Medicine statistics class
class MedicineStatistics extends Equatable {
  final int totalMedicines;
  final int expiredMedicines;
  final int expiringSoonMedicines;
  final int lowStockMedicines;
  final int outOfStockMedicines;
  final int customMedicines;
  final int prescriptionMedicines;

  const MedicineStatistics({
    required this.totalMedicines,
    required this.expiredMedicines,
    required this.expiringSoonMedicines,
    required this.lowStockMedicines,
    required this.outOfStockMedicines,
    required this.customMedicines,
    required this.prescriptionMedicines,
  });

  /// Create statistics from list of medicines
  factory MedicineStatistics.fromMedicines(List<Medicine> medicines) {
    final now = DateTime.now();
    final expiringSoonThreshold = now.add(const Duration(days: 30));

    int expired = 0;
    int expiringSoon = 0;
    int lowStock = 0;
    int outOfStock = 0;
    int custom = 0;
    int prescription = 0;

    for (final medicine in medicines) {
      // Check expiration status
      if (medicine.expiration != null) {
        if (medicine.expiration!.isBefore(now)) {
          expired++;
        } else if (medicine.expiration!.isBefore(expiringSoonThreshold)) {
          expiringSoon++;
        }
      }

      // Check stock status
      if (medicine.quantity == 0) {
        outOfStock++;
      } else if (medicine.lowStockThreshold > 0 &&
          medicine.quantity <= medicine.lowStockThreshold) {
        lowStock++;
      }

      // Check medicine type
      if (medicine.isCustom) {
        custom++;
      } else {
        prescription++;
      }
    }

    return MedicineStatistics(
      totalMedicines: medicines.length,
      expiredMedicines: expired,
      expiringSoonMedicines: expiringSoon,
      lowStockMedicines: lowStock,
      outOfStockMedicines: outOfStock,
      customMedicines: custom,
      prescriptionMedicines: prescription,
    );
  }

  @override
  List<Object?> get props => [
        totalMedicines,
        expiredMedicines,
        expiringSoonMedicines,
        lowStockMedicines,
        outOfStockMedicines,
        customMedicines,
        prescriptionMedicines,
      ];
}

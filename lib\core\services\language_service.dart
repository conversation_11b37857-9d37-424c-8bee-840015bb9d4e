import 'dart:ui';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for managing app language settings
class LanguageService {
  static const String _languageKey = 'app_language';
  static const String _defaultLanguage = 'fr';

  static final LanguageService _instance = LanguageService._internal();
  factory LanguageService() => _instance;
  LanguageService._internal();

  /// Supported locales
  static const List<Locale> supportedLocales = [
    Locale('fr', 'FR'), // French
    Locale('en', 'US'), // English
    Locale('ar', 'TN'), // Arabic
  ];

  /// Get current language from storage
  Future<String> getCurrentLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_languageKey) ?? _defaultLanguage;
  }

  /// Set current language and save to storage
  Future<void> setLanguage(String languageCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languageKey, languageCode);
  }

  /// Get current locale
  Future<Locale> getCurrentLocale() async {
    final languageCode = await getCurrentLanguage();
    return _getLocaleFromLanguageCode(languageCode);
  }

  /// Set locale and save to storage
  Future<void> setLocale(Locale locale) async {
    await setLanguage(locale.languageCode);
  }

  /// Get locale from language code
  Locale _getLocaleFromLanguageCode(String languageCode) {
    switch (languageCode) {
      case 'fr':
        return const Locale('fr', 'FR');
      case 'en':
        return const Locale('en', 'US');
      case 'ar':
        return const Locale('ar', 'TN');
      default:
        return const Locale('fr', 'FR');
    }
  }

  /// Get language name for display
  String getLanguageName(String languageCode) {
    switch (languageCode) {
      case 'fr':
        return 'Français';
      case 'en':
        return 'English';
      case 'ar':
        return 'العربية';
      default:
        return 'Français';
    }
  }

  /// Get language flag emoji
  String getLanguageFlag(String languageCode) {
    switch (languageCode) {
      case 'fr':
        return '🇫🇷';
      case 'en':
        return '🇺🇸';
      case 'ar':
        return '🇹🇳';
      default:
        return '🇫🇷';
    }
  }

  /// Check if language is RTL
  bool isRTL(String languageCode) {
    return languageCode == 'ar';
  }

  /// Get system locale if supported, otherwise return default
  Locale getSystemLocaleOrDefault() {
    final systemLocale = PlatformDispatcher.instance.locale;
    
    // Check if system locale is supported
    for (final supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == systemLocale.languageCode) {
        return supportedLocale;
      }
    }
    
    // Return default if system locale is not supported
    return const Locale('fr', 'FR');
  }

  /// Initialize language service
  Future<void> initialize() async {
    // Ensure we have a language set
    final currentLanguage = await getCurrentLanguage();
    if (currentLanguage.isEmpty) {
      final systemLocale = getSystemLocaleOrDefault();
      await setLanguage(systemLocale.languageCode);
    }
  }
}

// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'MedyTrack';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get medicines => 'Medicines';

  @override
  String get myMedicines => 'My Medicines';

  @override
  String get alerts => 'Alerts';

  @override
  String get reminders => 'Reminders';

  @override
  String get settings => 'Settings';

  @override
  String get profile => 'Profile';

  @override
  String get locations => 'Locations';

  @override
  String get family => 'Family';

  @override
  String get notificationSettings => 'Notification Settings';

  @override
  String get languageSettings => 'Language Settings';

  @override
  String get profileSecurity => 'Profile & Security';

  @override
  String get personalization => 'Personalization';

  @override
  String get dataManagement => 'Data Management';

  @override
  String get expiryAlerts => 'Expiry Alerts';

  @override
  String get lowStockAlerts => 'Low Stock Alerts';

  @override
  String get medicationReminders => 'Medication Reminders';

  @override
  String get pushNotifications => 'Push Notifications';

  @override
  String get emailNotifications => 'Email Notifications';

  @override
  String get enabled => 'Enabled';

  @override
  String get disabled => 'Disabled';

  @override
  String get activated => 'Activated';

  @override
  String get deactivated => 'Deactivated';

  @override
  String get language => 'Language';

  @override
  String get french => 'French';

  @override
  String get english => 'English';

  @override
  String get arabic => 'Arabic';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get dateFormat => 'Date Format';

  @override
  String get autoSync => 'Auto Sync';

  @override
  String get testNotifications => 'Test Notifications';

  @override
  String get sendTestNotification => 'Send a test notification';

  @override
  String get test => 'Test';

  @override
  String get testNotificationSent => 'Test notification sent!';

  @override
  String get error => 'Error';

  @override
  String get expiryThreshold => 'Expiry Threshold';

  @override
  String get lowStockThreshold => 'Low Stock Threshold';

  @override
  String daysBeforeExpiry(int days) {
    return '$days days before expiry';
  }

  @override
  String unitsRemaining(int units) {
    return '$units units or less';
  }

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get ok => 'OK';

  @override
  String get addReminder => 'Add Reminder';

  @override
  String get editReminder => 'Edit Reminder';

  @override
  String get deleteReminder => 'Delete Reminder';

  @override
  String get reminderSettings => 'Reminder Settings';

  @override
  String get activeReminders => 'Active Reminders';

  @override
  String get medicinesWithoutReminders => 'Medicines without Reminders';

  @override
  String get noActiveReminders => 'No active reminders';

  @override
  String get noMedicinesWithoutReminders => 'All medicines have reminders';

  @override
  String get reminderTime => 'Reminder Time';

  @override
  String get reminderFrequency => 'Frequency';

  @override
  String get daily => 'Daily';

  @override
  String get weekly => 'Weekly';

  @override
  String get hourlyInterval => 'Hourly Interval';

  @override
  String get selectDays => 'Select Days';

  @override
  String get intervalHours => 'Interval (hours)';

  @override
  String get startDate => 'Start Date';

  @override
  String get endDate => 'End Date';

  @override
  String get isActive => 'Active';

  @override
  String get reminderAdded => 'Reminder added successfully';

  @override
  String get reminderUpdated => 'Reminder updated successfully';

  @override
  String get reminderDeleted => 'Reminder deleted successfully';

  @override
  String get medicineReminderTitle => '💊 Medicine Reminder';

  @override
  String timeToTakeMedicine(String medicineName) {
    return 'Time to take $medicineName';
  }

  @override
  String everyXHours(int hours) {
    return 'every $hours hours';
  }

  @override
  String get loadingError => 'Loading error';

  @override
  String get featureComingSoon =>
      'This feature will be available in a future version.';

  @override
  String get preferences => 'Preferences';

  @override
  String get familyMembers => 'Family Members';

  @override
  String get storageLocations => 'Storage Locations';

  @override
  String get tags => 'Tags';

  @override
  String get dangerZone => 'Danger Zone';

  @override
  String get security => 'Security';

  @override
  String get minimumExpiryThreshold => 'Minimum Expiry Threshold';

  @override
  String get defaultLocation => 'Default Location';

  @override
  String get defaultFamilyMember => 'Default Family Member';

  @override
  String get showExpiredMedicines => 'Show Expired Medicines';

  @override
  String get groupByLocation => 'Group by Location';

  @override
  String get addMember => 'Add Member';

  @override
  String get editMember => 'Edit Member';

  @override
  String get addLocation => 'Add Location';

  @override
  String get editLocation => 'Edit Location';

  @override
  String get addTag => 'Add Tag';

  @override
  String get editTag => 'Edit Tag';

  @override
  String get name => 'Name';

  @override
  String get description => 'Description';

  @override
  String get relationship => 'Relationship';

  @override
  String get category => 'Category';

  @override
  String get color => 'Color';

  @override
  String get none => 'None';

  @override
  String get days => 'days';

  @override
  String get units => 'units';

  @override
  String get changePassword => 'Change Password';

  @override
  String get currentPassword => 'Current Password';

  @override
  String get newPassword => 'New Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get updatePassword => 'Update Password';

  @override
  String get deleteAccount => 'Delete Account';

  @override
  String get accountDeletion => 'Account Deletion';

  @override
  String get personalInformation => 'Personal Information';

  @override
  String get email => 'Email';

  @override
  String get updateProfile => 'Update Profile';

  @override
  String get avatar => 'Avatar';

  @override
  String get uploadPhoto => 'Upload Photo';

  @override
  String get removePhoto => 'Remove Photo';
}

import '../../domain/entities/medicine.dart';

class MedicineModel extends Medicine {
  const MedicineModel({
    required super.id,
    required super.householdId,
    super.userId,
    super.medicineId,
    super.customName,
    super.isCustom,
    super.dosage,
    super.form,
    super.expiration,
    super.quantity,
    super.category,
    super.tags,
    super.location,
    super.locationName,
    super.familyMemberId,
    super.familyMemberName,
    super.notes,
    super.lowStockThreshold,
    super.barcode,
    required super.createdAt,
    super.updatedAt,
    super.medicineName,
    super.laboratoire,
    super.dci,
    super.classe,
    super.sousClasse,
    super.amm,
  });

  factory MedicineModel.fromJson(Map<String, dynamic> json) {
    // Validate required fields
    final id = json['id'] as String? ?? '';
    final householdId = json['household_id'] as String? ?? '';

    if (householdId.isEmpty) {
      throw Exception('Medicine household_id cannot be null or empty');
    }

    // Extract joined data
    final tunisiaMedicine = json['tunisia_medicines'] as Map<String, dynamic>?;
    final familyMember = json['family_members'] as Map<String, dynamic>?;

    return MedicineModel(
      id: id,
      householdId: householdId,
      userId: json['user_id'] as String?,
      medicineId: json['medicine_id'] as String?,
      customName: json['custom_name'] as String?,
      isCustom: json['is_custom'] as bool? ?? false,
      dosage:
          tunisiaMedicine?['dosage'] as String? ?? json['dosage'] as String?,
      form: tunisiaMedicine?['forme'] as String? ?? json['form'] as String?,
      expiration: json['expiration'] != null
          ? DateTime.parse(json['expiration'] as String)
          : null,
      quantity: json['quantity'] as int? ?? 1,
      category: json['category'] as String?,
      tags: json['tags'] != null ? List<String>.from(json['tags'] as List) : [],
      location: json['location'] as String?,
      locationName: json['location_name'] as String?,
      familyMemberId: json['family_member_id'] as String?,
      familyMemberName: familyMember?['name'] as String?,
      notes: json['notes'] as String?,
      lowStockThreshold: json['low_stock_threshold'] as int? ?? 0,
      barcode: json['barcode'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : DateTime.now(), // Keep current time as fallback for required field
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      medicineName: tunisiaMedicine?['nom'] as String?,
      laboratoire: tunisiaMedicine?['laboratoire'] as String?,
      dci: tunisiaMedicine?['dci'] as String?,
      classe: tunisiaMedicine?['classe'] as String?,
      sousClasse: tunisiaMedicine?['sous_classe'] as String?,
      amm: tunisiaMedicine?['amm'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id.isNotEmpty) 'id': id,
      'household_id': householdId,
      'user_id': userId,
      'medicine_id': medicineId,
      'custom_name': customName,
      'is_custom': isCustom,
      'dosage': dosage,
      // 'form': form, // Removed - column doesn't exist in database schema
      'expiration': expiration?.toIso8601String(),
      'quantity': quantity,
      'location': location, // Correct field name for database
      'family_member_id': familyMemberId,
      'notes': notes,
      'low_stock_threshold': lowStockThreshold,
      // 'barcode': barcode, // Removed - column doesn't exist in database schema
      'created_at': createdAt.toIso8601String(),
      // 'updated_at': updatedAt?.toIso8601String(), // Removed - column doesn't exist in database schema
    };
  }

  factory MedicineModel.fromEntity(Medicine medicine) {
    return MedicineModel(
      id: medicine.id,
      householdId: medicine.householdId,
      userId: medicine.userId,
      medicineId: medicine.medicineId,
      customName: medicine.customName,
      isCustom: medicine.isCustom,
      dosage: medicine.dosage,
      form: medicine.form,
      expiration: medicine.expiration,
      quantity: medicine.quantity,
      category: medicine.category,
      tags: medicine.tags,
      location: medicine.location,
      locationName: medicine.locationName,
      familyMemberId: medicine.familyMemberId,
      familyMemberName: medicine.familyMemberName,
      notes: medicine.notes,
      lowStockThreshold: medicine.lowStockThreshold,
      barcode: medicine.barcode,
      createdAt: medicine.createdAt,
      updatedAt: medicine.updatedAt,
      medicineName: medicine.medicineName,
      laboratoire: medicine.laboratoire,
      dci: medicine.dci,
      classe: medicine.classe,
      sousClasse: medicine.sousClasse,
      amm: medicine.amm,
    );
  }

  /// Convert to entity
  Medicine toEntity() {
    return Medicine(
      id: id,
      householdId: householdId,
      userId: userId,
      medicineId: medicineId,
      customName: customName,
      isCustom: isCustom,
      dosage: dosage,
      form: form,
      expiration: expiration,
      quantity: quantity,
      tags: tags,
      location: location,
      locationName: locationName,
      familyMemberId: familyMemberId,
      familyMemberName: familyMemberName,
      notes: notes,
      lowStockThreshold: lowStockThreshold,
      barcode: barcode,
      createdAt: createdAt,
      updatedAt: updatedAt,
      medicineName: medicineName,
      laboratoire: laboratoire,
      dci: dci,
      classe: classe,
      sousClasse: sousClasse,
      amm: amm,
    );
  }
}

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/medicine.dart';

class MedicineCard extends StatelessWidget {
  final Medicine medicine;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const MedicineCard({
    super.key,
    required this.medicine,
    this.onTap,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    // Standardized card: elevation 1, radius 12, 1px border; dynamic color kept via status chip only
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(color: AppColors.grey200, width: 1),
      ),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Top-right three-dots button (opens edit/delete menu)
          if (onEdit != null || onDelete != null)
            Positioned(
              top: 0,
              right: 0,
              child: _cornerMenuButton(context),
            ),
          InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _buildGridContent(),
            ),
          ),
        ],
      ),
    );
  }

  // Corner menu button styled like spec (35x35 teal, radius 12, white icon, shadow)
  Widget _cornerMenuButton(BuildContext context) {
    return PopupMenuButton<String>(
      onSelected: (value) {
        switch (value) {
          case 'edit':
            onEdit?.call();
            break;
          case 'delete':
            onDelete?.call();
            break;
        }
      },
      itemBuilder: (context) => [
        if (onEdit != null)
          PopupMenuItem<String>(
            value: 'edit',
            child: Row(
              children: [
                Icon(Icons.edit_outlined, size: 18, color: AppColors.grey600),
                const SizedBox(width: 8),
                const Text('Modifier'),
              ],
            ),
          ),
        if (onDelete != null)
          PopupMenuItem<String>(
            value: 'delete',
            child: Row(
              children: [
                Icon(Icons.delete_outline, size: 18, color: AppColors.error),
                const SizedBox(width: 8),
                Text('Supprimer', style: TextStyle(color: AppColors.error)),
              ],
            ),
          ),
      ],
      offset: const Offset(0, 40),
      elevation: 8,
      color: Colors.white,
      constraints: const BoxConstraints(minWidth: 160),
      child: Container(
        width: 35,
        height: 35,
        decoration: BoxDecoration(
          color: AppColors.teal,
          borderRadius: const BorderRadius.only(
            topRight: Radius.circular(12),
            bottomLeft: Radius.circular(12),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        alignment: Alignment.center,
        child: const Icon(Icons.more_vert, color: Colors.white, size: 18),
      ),
    );
  }

  // Content area per spec:
  // - Row 1: Medicine name (bold, navy, titleMedium + 2)
  // - Row 2: Dosage/form (smaller, black)
  // - Row 3: Expiry date (smaller, black)
  // - Row 4: Two columns -> Left: Location; Right: Status badge (retained, dynamic color)
  Widget _buildGridContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Row 1: Medicine name
        Text(
          medicine.name,
          style: AppTextStyles.titleMedium.copyWith(
            color: AppColors.navy,
            fontWeight: FontWeight.bold,
            fontSize: AppTextStyles.titleMedium.fontSize! + 2,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),

        // Row 2: Dosage / Form
        Text(
          _composeDosageForm(),
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.black,
            fontWeight: FontWeight.normal,
            fontSize: AppTextStyles.bodySmall.fontSize! - 2,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),

        // Row 3: Expiry
        Text(
          _composeExpiry(),
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.black,
            fontWeight: FontWeight.normal,
            fontSize: AppTextStyles.bodySmall.fontSize! - 2,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),

        // Row 4: Two columns -> Location | Status chip
        Row(
          children: [
            // Left: Location aspect
            Expanded(
              child: Text(
                _composeLocation(),
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.black,
                  fontWeight: FontWeight.normal,
                  fontSize: AppTextStyles.bodySmall.fontSize! - 2,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 12),
            // Right: Status badge (dynamic color retained)
            _buildStatusChip(),
          ],
        ),

        // Optional: Tags kept, but positioned after the grid block
        if (medicine.tags.isNotEmpty) ...[
          const SizedBox(height: 12),
          Wrap(
            spacing: 6,
            runSpacing: 6,
            children: medicine.tags.take(3).map((tag) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.teal.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  tag,
                  style: AppTextStyles.labelSmall.copyWith(color: AppColors.teal),
                ),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  String _composeDosageForm() {
    final parts = <String>[];
    final dosage = (medicine.dosage ?? '').trim();
    final form = (medicine.form ?? '').trim();
    if (dosage.isNotEmpty) parts.add(dosage);
    if (form.isNotEmpty) parts.add(form);
    return parts.isEmpty ? '' : parts.join(' ');
  }

  String _composeExpiry() {
    if (medicine.expiration == null) return 'Expiration: Non définie';
    return 'Expiration: ${DateFormat('dd/MM/yyyy').format(medicine.expiration!)}';
  }

  String _composeLocation() {
    final name = (medicine.locationName ?? '').trim();
    final code = (medicine.location ?? '').trim();
    final label = name.isNotEmpty ? name : code;
    return label.isNotEmpty ? 'Lieu: $label' : 'Lieu: Non défini';
  }

  Widget _buildStatusChip() {
    final status = medicine.status;
    final color = _getStatusColor();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        _getStatusText(status),
        style: AppTextStyles.labelSmall.copyWith(color: color),
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    Color? color,
  }) {
    final chipColor = color ?? AppColors.grey600;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: chipColor,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: AppTextStyles.labelSmall.copyWith(color: chipColor),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (medicine.status) {
      case MedicineStatus.expired:
        return AppColors.error;
      case MedicineStatus.expiringSoon:
        return AppColors.warning;
      case MedicineStatus.lowStock:
        return AppColors.warning;
      case MedicineStatus.outOfStock:
        return AppColors.error;
      case MedicineStatus.normal:
        return AppColors.success;
    }
  }

  Color _getExpirationColor() {
    if (medicine.expiration == null) return AppColors.grey600;

    final now = DateTime.now();
    final daysUntilExpiration = medicine.expiration!.difference(now).inDays;

    if (daysUntilExpiration < 0) return AppColors.error;
    if (daysUntilExpiration <= 30) return AppColors.warning;
    return AppColors.grey600;
  }

  String _getStatusText(MedicineStatus status) {
    switch (status) {
      case MedicineStatus.expired:
        return 'Expiré';
      case MedicineStatus.expiringSoon:
        return 'Expire bientôt';
      case MedicineStatus.lowStock:
        return 'Stock faible';
      case MedicineStatus.outOfStock:
        return 'Rupture';
      case MedicineStatus.normal:
        return 'Normal';
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/repositories/dashboard_repository.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_state.dart' as auth_state;
import '../../bloc/dashboard/dashboard_bloc.dart';
import '../../bloc/medicine/medicine_bloc.dart';

import '../../widgets/dashboard/statistics_grid.dart';
import '../../widgets/dashboard/recent_medicines.dart';
import '../../widgets/dashboard/expiring_medicines.dart';
import '../../widgets/dashboard/medicine_search.dart';
import '../../widgets/dashboard/todays_medicines.dart';

/// Dashboard page that mirrors the web app's dashboard functionality
class DashboardPage extends StatefulWidget {
  final String? filter;
  final String? locationId;
  final String? familyMemberId;

  const DashboardPage({
    super.key,
    this.filter,
    this.locationId,
    this.familyMemberId,
  });

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  void _loadDashboardData() {
    final authState = context.read<AuthBloc>().state;
    if (authState is auth_state.AuthAuthenticated &&
        authState.user.householdId != null) {
      final householdId = authState.user.householdId!;

      // Check if we have any filtering parameters
      final hasFiltering = widget.filter != null ||
          widget.locationId != null ||
          widget.familyMemberId != null;

      if (hasFiltering) {
        // Load filtered dashboard data
        final params = DashboardStatsParams(
          householdId: householdId,
          filter: widget.filter,
          locationId: widget.locationId,
          familyMemberId: widget.familyMemberId,
        );

        context.read<DashboardBloc>().add(
              DashboardFilteredLoadRequested(params: params),
            );
      } else {
        // Load standard dashboard data
        context.read<DashboardBloc>().add(
              DashboardLoadRequested(householdId: householdId),
            );
      }

      // Load medicines for search functionality
      context.read<MedicineBloc>().add(
            MedicineLoadRequested(householdId: householdId),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.grey50,
      body: Column(
        children: [
          // Gradient header background
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: _getHeaderGradient(),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(32),
                bottomRight: Radius.circular(32),
              ),
            ),
            child: SafeArea(
              bottom: false,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(20, 20, 20, 40),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Welcome Card with integrated header
                    BlocBuilder<AuthBloc, auth_state.AuthState>(
                      builder: (context, authState) {
                        String? userName;
                        if (authState is auth_state.AuthAuthenticated) {
                          userName = authState.user.displayName;
                        }
                        return _buildWelcomeCard(userName);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Content container with layered effect
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -4),
                  ),
                ],
              ),
              child: RefreshIndicator(
                onRefresh: () async {
                  _loadDashboardData();
                },
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 3. Search Bar (standalone)
                    const Padding(
                      padding: EdgeInsets.fromLTRB(20, 16, 20, 0),
                      child: MedicineSearch(),
                    ),

                    const SizedBox(height: 16),

                    // 4. Scrollable/Expandable Content
                    Expanded(
                      child: SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Statistics cards
                            BlocBuilder<DashboardBloc, DashboardState>(
                              builder: (context, state) {
                                if (state is DashboardLoading) {
                                  return _buildLoadingState();
                                }

                                if (state is DashboardError) {
                                  return _buildErrorState(state.message);
                                }

                                if (state is DashboardLoaded) {
                                  return StatisticsGrid(
                                    stats: state.stats,
                                    onStatisticTap: (type) {
                                      // Navigate to My Medicines with filter based on card type
                                      String filterParam = '';
                                      switch (type) {
                                        case 'expired':
                                          filterParam = '?filter=expired';
                                          break;
                                        case 'low_stock':
                                          filterParam = '?filter=low_stock';
                                          break;
                                        case 'expiring':
                                          filterParam = '?filter=expiring_soon';
                                          break;
                                        case 'total':
                                        default:
                                          filterParam = '?filter=all';
                                          break;
                                      }
                                      context.push('/medicines/my$filterParam');
                                    },
                                  );
                                }

                                return const SizedBox.shrink();
                              },
                            ),

                            const SizedBox(height: 32),

                            // Today's medicines section (positioned before medicine cards)
                            _buildSectionHeader('Médicaments d\'aujourd\'hui'),
                            const SizedBox(height: 16),
                            const TodaysMedicines(),

                            const SizedBox(height: 32),

                            // Expiring medicines section
                            _buildSectionHeader('Médicaments expirant bientôt'),
                            const SizedBox(height: 16),
                            const ExpiringMedicines(),

                            const SizedBox(height: 32),

                            // Recent medicines section
                            _buildSectionHeader('Ajouts récents'),
                            const SizedBox(height: 16),
                            const RecentMedicines(),

                            const SizedBox(height: 32), // Bottom padding
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build loading state widget
  Widget _buildLoadingState() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Center(
        child: Column(
          children: [
            CircularProgressIndicator(
              color: AppColors.teal,
            ),
            const SizedBox(height: 16),
            Text(
              'Chargement des données...',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build error state widget
  Widget _buildErrorState(String message) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            color: AppColors.error,
            size: 48,
          ),
          const SizedBox(height: 12),
          Text(
            'Erreur de chargement',
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.error,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.grey600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadDashboardData,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.white,
            ),
            child: const Text('Réessayer'),
          ),
        ],
      ),
    );
  }

  /// Build section header widget
  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: AppTextStyles.headlineSmall.copyWith(
        color: AppColors.navy,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  /// Gradient matching the white-blue background style
  LinearGradient _getHeaderGradient() {
    return const LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        Color(0xFFE3F0FF), // Light pastel blue
        Color(0xFFFFFFFF), // White bottom blend
      ],
    );
  }

  /// Welcome card with integrated header (greeting + avatar) and welcome message
  Widget _buildWelcomeCard(String? userName) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header row with greeting and avatar (matching mockup)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Left side: Greeting text
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    userName != null ? 'Bonjour,' : 'Bonjour',
                    style: AppTextStyles.headlineSmall.copyWith(
                      color: AppColors.navy,
                      fontWeight: FontWeight.bold,
                      fontSize: 28,
                    ),
                  ),
                  if (userName != null)
                    Text(
                      userName,
                      style: AppTextStyles.headlineSmall.copyWith(
                        color: AppColors.navy,
                        fontWeight: FontWeight.bold,
                        fontSize: 28,
                      ),
                    ),
                ],
              ),
              // Right side: Circular user avatar
              GestureDetector(
                onTap: () {
                  context.push('/settings');
                },
                child: Container(
                  width: 48,
                  height: 48,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.person,
                    color: Colors.grey,
                    size: 28,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Welcome message row with emoji
          Row(
            children: [
              const Text('👋', style: TextStyle(fontSize: 28)),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Bienvenue dans votre espace MedyTrack',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: AppColors.grey600,
                    fontSize: 18,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

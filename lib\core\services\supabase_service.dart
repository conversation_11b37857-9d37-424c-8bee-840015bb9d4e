import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/app_config.dart';
import '../network/network_info.dart';
import '../../utils/logger.dart';

/// Enhanced Supabase service with connection management, retry logic, and error handling
class SupabaseService {
  static SupabaseService? _instance;
  static SupabaseService get instance => _instance ??= SupabaseService._();

  SupabaseService._();

  late SupabaseClient _client;
  late NetworkInfo _networkInfo;
  Timer? _connectionTimer;
  bool _isInitialized = false;

  /// Connection status stream
  final _connectionStatusController = StreamController<bool>.broadcast();
  Stream<bool> get connectionStatus => _connectionStatusController.stream;

  /// Current connection status
  bool _isConnected = false;
  bool get isConnected => _isConnected;

  /// Initialize the Supabase service with enhanced configuration
  Future<void> initialize({
    required NetworkInfo networkInfo,
  }) async {
    if (_isInitialized) return;

    _networkInfo = networkInfo;

    try {
      // Initialize Supabase with enhanced configuration
      await Supabase.initialize(
        url: AppConfig.supabaseUrl,
        anonKey: AppConfig.supabaseAnonKey,
        authOptions: const FlutterAuthClientOptions(
          authFlowType: AuthFlowType.pkce,
          detectSessionInUri: true,
        ),
        realtimeClientOptions: const RealtimeClientOptions(
          logLevel: RealtimeLogLevel.info,
          timeout: Duration(seconds: 30),
        ),
        postgrestOptions: const PostgrestClientOptions(
          schema: 'public',
        ),
      );

      _client = Supabase.instance.client;
      _isInitialized = true;

      // Start connection monitoring
      _startConnectionMonitoring();

      AppLogger.log('Supabase service initialized successfully');
    } catch (e) {
      AppLogger.error('Failed to initialize Supabase service', error: e);
      rethrow;
    }
  }

  /// Get the Supabase client with connection validation
  SupabaseClient get client {
    if (!_isInitialized) {
      throw Exception(
          'Supabase service not initialized. Call initialize() first.');
    }
    return _client;
  }

  /// Execute a database operation with retry logic and error handling
  Future<T> executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 1),
    String? operationName,
  }) async {
    int attempts = 0;
    Exception? lastException;

    while (attempts < maxRetries) {
      try {
        // Check network connectivity before attempting operation
        if (!await _networkInfo.isConnected) {
          throw Exception('No network connection available');
        }

        final result = await operation();

        // Reset connection status on successful operation
        _updateConnectionStatus(true);

        return result;
      } catch (e) {
        attempts++;
        lastException = e is Exception ? e : Exception(e.toString());

        AppLogger.database(
            'Database operation failed (attempt $attempts/$maxRetries)',
            error: e);

        // Update connection status on error
        _updateConnectionStatus(false);

        // Don't retry on authentication errors or client errors
        if (_isNonRetryableError(e)) {
          AppLogger.warning('Non-retryable error detected, not retrying');
          break;
        }

        // Wait before retrying (exponential backoff)
        if (attempts < maxRetries) {
          final waitTime = delay * (attempts * attempts); // Exponential backoff
          debugPrint('⏳ Waiting ${waitTime.inSeconds}s before retry...');
          await Future.delayed(waitTime);
        }
      }
    }

    // All retries exhausted
    final errorMessage = operationName != null
        ? 'Failed to execute $operationName after $maxRetries attempts'
        : 'Database operation failed after $maxRetries attempts';

    throw Exception('$errorMessage: ${lastException?.toString()}');
  }

  /// Execute a real-time stream operation with error handling
  Stream<T> executeStreamWithRetry<T>(
    Stream<T> Function() streamOperation, {
    String? operationName,
  }) {
    return streamOperation().handleError((error) {
      debugPrint('❌ Stream operation failed: $error');
      _updateConnectionStatus(false);

      // Re-throw with enhanced error message
      final errorMessage = operationName != null
          ? 'Stream operation $operationName failed'
          : 'Stream operation failed';

      throw Exception('$errorMessage: ${error.toString()}');
    });
  }

  /// Check if an error should not be retried
  bool _isNonRetryableError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    // Authentication errors
    if (errorString.contains('401') || errorString.contains('unauthorized')) {
      return true;
    }

    // Permission errors
    if (errorString.contains('403') || errorString.contains('forbidden')) {
      return true;
    }

    // Bad request errors (client-side issues)
    if (errorString.contains('400') || errorString.contains('bad request')) {
      return true;
    }

    // Not found errors
    if (errorString.contains('404') || errorString.contains('not found')) {
      return true;
    }

    return false;
  }

  /// Start monitoring connection status
  void _startConnectionMonitoring() {
    // Monitor network connectivity
    _networkInfo.onConnectivityChanged.listen((isConnected) {
      _updateConnectionStatus(isConnected);
    });

    // Periodic connection health check
    _connectionTimer = Timer.periodic(const Duration(minutes: 1), (_) {
      _performHealthCheck();
    });
  }

  /// Perform a health check on the database connection
  Future<void> _performHealthCheck() async {
    try {
      // Simple query to test connection
      await _client.from('profiles').select('id').limit(1);
      _updateConnectionStatus(true);
    } catch (e) {
      debugPrint('🔍 Health check failed: $e');
      _updateConnectionStatus(false);
    }
  }

  /// Update connection status and notify listeners
  void _updateConnectionStatus(bool isConnected) {
    if (_isConnected != isConnected) {
      _isConnected = isConnected;
      _connectionStatusController.add(isConnected);

      debugPrint(isConnected
          ? '🟢 Database connection restored'
          : '🔴 Database connection lost');
    }
  }

  /// Get enhanced error message for user display
  String getErrorMessage(dynamic error) {
    if (error == null) return 'Une erreur inconnue s\'est produite';

    final errorString = error.toString().toLowerCase();

    if (errorString.contains('network') || errorString.contains('connection')) {
      return 'Problème de connexion réseau. Vérifiez votre connexion internet.';
    } else if (errorString.contains('timeout')) {
      return 'Délai d\'attente dépassé. Veuillez réessayer.';
    } else if (errorString.contains('401') ||
        errorString.contains('unauthorized')) {
      return 'Session expirée. Veuillez vous reconnecter.';
    } else if (errorString.contains('403') ||
        errorString.contains('forbidden')) {
      return 'Accès refusé. Vous n\'avez pas les permissions nécessaires.';
    } else if (errorString.contains('404') ||
        errorString.contains('not found')) {
      return 'Ressource non trouvée.';
    } else if (errorString.contains('400') ||
        errorString.contains('bad request')) {
      return 'Paramètres de requête invalides.';
    } else {
      return 'Erreur de base de données. Veuillez réessayer.';
    }
  }

  /// Dispose of resources
  void dispose() {
    _connectionTimer?.cancel();
    _connectionStatusController.close();
  }
}

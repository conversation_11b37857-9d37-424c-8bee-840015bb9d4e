# MedyTrack Mobile - Navigation and Routing Map

**Document Version:** 1.0
**Analysis Date:** 2025-01-30

## 1. Overview

This document provides a clear map of the user navigation and page routing within the MedyTrack Mobile application, based on a direct analysis of the v0.4.5 source code. The app uses the `go_router` package for declarative routing.

The primary goal is to provide an accurate source of truth for the implemented navigation flow.

## 2. Core Navigation Mechanisms

User navigation is primarily handled through three main components:

1.  **`GoRouter`**: The declarative routing package that manages the application's route stack.
2.  **`context.go()`**: Used for top-level navigation, often clearing the existing navigation stack (e.g., navigating from the `HamburgerMenu`).
3.  **`context.push()`**: Used for pushing a new page onto the stack without losing the history (e.g., opening an edit page).
4.  **`context.pop()`**: Used for returning to the previous page in the stack.

## 3. Architectural Inconsistencies Noted

The codebase analysis reveals a discrepancy in the Settings feature area:

- **Implemented Behavior**: The `HamburgerMenu` navigates to `/settings`, which loads `settings_page_redesign.dart`. This file is a single, large page containing multiple sections (Profile, Notifications, Appearance, etc.).
- **File Structure Suggestion**: The existence of `profile_security_page.dart` and `personalization_page.dart` suggests an intended architecture where `/settings` is a hub that links to these more specific sub-pages.
- **Conclusion**: The navigation to `profile_security_page.dart` and `personalization_page.dart` is **not currently implemented** in the main navigation flow. These pages appear to be orphaned or part of an incomplete refactoring, as documented in the `CHANGELOG.md` for v0.3.1. The routing map below reflects the code's current, active state.

## 4. Route Mapping Table

The following table details the primary routes, the corresponding page files, and how they are accessed.

| Route Path | Page File | Accessed From | Description |
| :--- | :--- | :--- | :--- |
| **Authentication (Implied)** |
| `/auth/login` | `(Not Provided)` | App Start (unauthenticated), Logout | User login screen. |
| **Main Dashboard** |
| `/dashboard` | `lib/presentation/pages/dashboard/new_dashboard_page.dart` | Post-login, Back navigation | The main application home screen. Displays today's reminders and statistics. |
| **Medicine Management** |
| `/medicines/my?filter=:filter` | `lib/presentation/pages/medicine/medicine_list_page_redesign.dart` | Dashboard Stat Cards | Displays a pre-filtered list of medicines (e.g., expired, low stock). |
| `/medicines?search=:query` | `lib/presentation/pages/medicine/medicine_list_page_redesign.dart` | Dashboard Search Bar | Displays a search-filtered list of medicines. |
| `/medicines/add` | `lib/presentation/pages/medicine/add_medicine_page_redesign.dart` | FAB on `medicine_list_page_redesign` | The multi-step form for adding a new medicine. |
| `/medicines/:id/edit` | `lib/presentation/pages/medicine/add_medicine_page_redesign.dart` | "Edit" action on a medicine card | Reuses the add medicine form to edit an existing one. |
| `/medicines/:id` | `(Not Provided)` | Tapping a medicine card | Navigates to a medicine's detail page. |
| **Reminder System** |
| `/reminders` | `lib/presentation/pages/reminders/reminders_page.dart` | `HamburgerMenu` | Lists all reminders, filterable by status (Active, Paused, Archived). |
| `/reminders/add` | `lib/presentation/pages/reminders/reminders_page.dart` | "Add Reminder" button | Form to create a new reminder. |
| `/reminders/edit/:id` | `lib/presentation/pages/reminders/edit_reminder_page.dart` | "Modifier" action on a reminder card | Form to modify an existing reminder. |
| `reminder-detail` (Named Route) | `(Not Provided)` | Tapping a reminder card | Navigates to a reminder's detail page. |
| **Settings** |
| `/settings` | `lib/presentation/pages/settings/settings_page_redesign.dart` | `HamburgerMenu` | A single page containing all app settings. |
| `/family` | `(Not Provided)` | "Gestion de la famille" tile on `/settings` | Page for managing family members. |
| **Orphaned Pages** |
| N/A | `lib/presentation/pages/settings/profile_security_page.dart` | Not currently navigated to. | Appears to be part of an incomplete refactor. |
| N/A | `lib/presentation/pages/settings/personalization_page.dart` | Not currently navigated to. | Appears to be part of an incomplete refactor. |

## 5. Visual Navigation Flow

Here is a simplified tree representing the **currently implemented** user flow:

```mermaid
graph TD
    subgraph Auth Flow
        A[App Start] --> B{Is Authenticated?};
        B -- No --> C[/auth/login];
        C -- Success --> E;
    end

    subgraph Main App
        B -- Yes --> E[/dashboard];
        E --> F[Hamburger Menu];
        E -- Stat Card / Search --> I[/medicines/my];
        E -- Today's Reminder Card --> H[Medicine Details /medicines/:id];

        F -- Dashboard --> E;
        F -- My Medicines --> I;
        F -- Reminders --> J[/reminders];
        F -- Settings --> K[/settings];
        F -- Logout --> C;

        I -- Add FAB --> G[/medicines/add];
        I -- Edit --> L[/medicines/:id/edit];
        I -- Select Medicine --> H;

        J -- Add --> M[/reminders/add];
        J -- Edit --> N[/reminders/edit/:id];
        J -- Select Reminder --> O[Reminder Details (Named Route)];

        K -- Manage Family --> P[/family];
    end

    style F fill:#e6f3ff,stroke:#333,stroke-width:2px
    style E fill:#d4edda,stroke:#333,stroke-width:2px
```

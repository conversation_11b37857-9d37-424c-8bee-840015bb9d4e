import '../entities/location.dart';

/// Repository interface for location management
abstract class LocationRepository {
  /// Get all locations for a household
  /// 
  /// [householdId] - Household ID
  /// 
  /// Returns a Stream of location lists that updates in real-time
  Stream<List<Location>> getHouseholdLocations(String householdId);
  
  /// Get a location by ID
  /// 
  /// [locationId] - Location ID
  /// 
  /// Returns a Future with the Location entity if found
  Future<Location?> getLocationById(String locationId);
  
  /// Create a new location
  /// 
  /// [location] - Location entity to create
  /// 
  /// Returns a Future with the created location
  Future<Location> createLocation(Location location);
  
  /// Update an existing location
  /// 
  /// [location] - Location entity with updated information
  /// 
  /// Returns a Future with the updated location
  Future<Location> updateLocation(Location location);
  
  /// Delete a location
  /// 
  /// [locationId] - Location ID to delete
  /// 
  /// Returns a Future that completes when the location is deleted
  Future<void> deleteLocation(String locationId);
  
  /// Initialize default locations for a household
  /// 
  /// [householdId] - Household ID
  /// 
  /// Returns a Future that completes when default locations are created
  Future<void> initializeDefaultLocations(String householdId);
}

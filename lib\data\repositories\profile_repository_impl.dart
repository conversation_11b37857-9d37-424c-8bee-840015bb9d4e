import 'package:dartz/dartz.dart';
import 'dart:io';
import '../../domain/entities/user.dart';
import '../../domain/repositories/profile_repository.dart';
import '../../core/error/failures.dart';
import '../../core/network/network_info.dart';
import '../datasources/profile_remote_data_source.dart';
import '../datasources/profile_local_data_source.dart';

class ProfileRepositoryImpl implements ProfileRepository {
  final ProfileRemoteDataSource remoteDataSource;
  final ProfileLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  ProfileRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, User>> updateProfileName(String name) async {
    if (await networkInfo.isConnected) {
      try {
        final user = await remoteDataSource.updateProfileName(name);
        return Right(user);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> updateEmail(String email) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.updateEmail(email);
        return const Right(null);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, String>> uploadAvatar(File imageFile) async {
    if (await networkInfo.isConnected) {
      try {
        final avatarUrl = await remoteDataSource.uploadAvatar(imageFile);
        return Right(avatarUrl);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> removeAvatar() async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.removeAvatar();
        return const Right(null);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, String?>> getAvatarUrl() async {
    if (await networkInfo.isConnected) {
      try {
        final avatarUrl = await remoteDataSource.getAvatarUrl();
        return Right(avatarUrl);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> changePassword(String currentPassword, String newPassword) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.changePassword(currentPassword, newPassword);
        return const Right(null);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteAccount(String password) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.deleteAccount(password);
        await localDataSource.clearPreferences();
        return const Right(null);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> updateLanguagePreference(String language) async {
    try {
      await localDataSource.saveLanguagePreference(language);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> getLanguagePreference() async {
    try {
      final language = await localDataSource.getLanguagePreference();
      return Right(language);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> updateThemePreference(bool isDarkMode) async {
    try {
      await localDataSource.saveThemePreference(isDarkMode);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> getThemePreference() async {
    try {
      final isDarkMode = await localDataSource.getThemePreference();
      return Right(isDarkMode);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }
}

import '../../domain/entities/tunisia_medicine.dart';

/// Data model for TunisiaMedicine entity
class TunisiaMedicineModel extends TunisiaMedicine {
  const TunisiaMedicineModel({
    required super.id,
    required super.nom,
    super.dci,
    super.dosage,
    super.forme,
    super.presentation,
    super.laboratoire,
    super.classe,
    super.sousClasse,
    super.amm,
    super.dateAmm,
    super.conditionnementPrimaire,
    super.specificationConditionnementPrimaire,
    super.tableau,
    super.dureeDeConservation,
    super.indications,
    super.gPB,
    super.veic,
    super.codeATC,
    super.prix,
    super.remboursement,
    super.createdAt,
    super.updatedAt,
  });

  /// Create from JSON
  factory TunisiaMedicineModel.fromJson(Map<String, dynamic> json) {
    return TunisiaMedicineModel(
      id: json['id'] as String,
      nom: json['nom'] as String,
      dci: json['dci'] as String?,
      dosage: json['dosage'] as String?,
      forme: json['forme'] as String?,
      presentation: json['presentation'] as String?,
      laboratoire: json['laboratoire'] as String?,
      classe: json['classe'] as String?,
      sousClasse: json['sous_classe'] as String?,
      amm: json['amm'] as String?,
      dateAmm: json['date_amm'] != null
          ? DateTime.parse(json['date_amm'] as String)
          : null,
      conditionnementPrimaire: json['conditionnement_primaire'] as String?,
      specificationConditionnementPrimaire:
          json['specification_conditionnement_primaire'] as String?,
      tableau: json['tableau'] as String?,
      dureeDeConservation: json['duree_de_conservation'] as String?,
      indications: json['indications'] as String?,
      gPB: json['g_p_b'] as String?,
      veic: json['veic'] as String?,
      codeATC: json['code_atc'] as String?,
      prix: json['prix'] as String?,
      remboursement: json['remboursement'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nom': nom,
      'dci': dci,
      'dosage': dosage,
      'forme': forme,
      'presentation': presentation,
      'laboratoire': laboratoire,
      'classe': classe,
      'sous_classe': sousClasse,
      'amm': amm,
      'date_amm': dateAmm?.toIso8601String(),
      'conditionnement_primaire': conditionnementPrimaire,
      'specification_conditionnement_primaire':
          specificationConditionnementPrimaire,
      'tableau': tableau,
      'duree_de_conservation': dureeDeConservation,
      'indications': indications,
      'g_p_b': gPB,
      'veic': veic,
      'code_atc': codeATC,
      'prix': prix,
      'remboursement': remboursement,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// Create from entity
  factory TunisiaMedicineModel.fromEntity(TunisiaMedicine entity) {
    return TunisiaMedicineModel(
      id: entity.id,
      nom: entity.nom,
      dci: entity.dci,
      dosage: entity.dosage,
      forme: entity.forme,
      presentation: entity.presentation,
      laboratoire: entity.laboratoire,
      classe: entity.classe,
      sousClasse: entity.sousClasse,
      amm: entity.amm,
      dateAmm: entity.dateAmm,
      conditionnementPrimaire: entity.conditionnementPrimaire,
      specificationConditionnementPrimaire:
          entity.specificationConditionnementPrimaire,
      tableau: entity.tableau,
      dureeDeConservation: entity.dureeDeConservation,
      indications: entity.indications,
      gPB: entity.gPB,
      veic: entity.veic,
      codeATC: entity.codeATC,
      prix: entity.prix,
      remboursement: entity.remboursement,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  /// Convert to entity
  TunisiaMedicine toEntity() {
    return TunisiaMedicine(
      id: id,
      nom: nom,
      dci: dci,
      dosage: dosage,
      forme: forme,
      presentation: presentation,
      laboratoire: laboratoire,
      classe: classe,
      sousClasse: sousClasse,
      amm: amm,
      dateAmm: dateAmm,
      conditionnementPrimaire: conditionnementPrimaire,
      specificationConditionnementPrimaire:
          specificationConditionnementPrimaire,
      tableau: tableau,
      dureeDeConservation: dureeDeConservation,
      indications: indications,
      gPB: gPB,
      veic: veic,
      codeATC: codeATC,
      prix: prix,
      remboursement: remboursement,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  /// Copy with method
  @override
  TunisiaMedicineModel copyWith({
    String? id,
    String? nom,
    String? dci,
    String? dosage,
    String? forme,
    String? presentation,
    String? laboratoire,
    String? classe,
    String? sousClasse,
    String? amm,
    DateTime? dateAmm,
    String? conditionnementPrimaire,
    String? specificationConditionnementPrimaire,
    String? tableau,
    String? dureeDeConservation,
    String? indications,
    String? gPB,
    String? veic,
    String? codeATC,
    String? prix,
    String? remboursement,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TunisiaMedicineModel(
      id: id ?? this.id,
      nom: nom ?? this.nom,
      dci: dci ?? this.dci,
      dosage: dosage ?? this.dosage,
      forme: forme ?? this.forme,
      presentation: presentation ?? this.presentation,
      laboratoire: laboratoire ?? this.laboratoire,
      classe: classe ?? this.classe,
      sousClasse: sousClasse ?? this.sousClasse,
      amm: amm ?? this.amm,
      dateAmm: dateAmm ?? this.dateAmm,
      conditionnementPrimaire:
          conditionnementPrimaire ?? this.conditionnementPrimaire,
      specificationConditionnementPrimaire:
          specificationConditionnementPrimaire ??
              this.specificationConditionnementPrimaire,
      tableau: tableau ?? this.tableau,
      dureeDeConservation: dureeDeConservation ?? this.dureeDeConservation,
      indications: indications ?? this.indications,
      gPB: gPB ?? this.gPB,
      veic: veic ?? this.veic,
      codeATC: codeATC ?? this.codeATC,
      prix: prix ?? this.prix,
      remboursement: remboursement ?? this.remboursement,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

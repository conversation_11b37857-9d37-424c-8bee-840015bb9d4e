import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:local_auth/local_auth.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide LocalStorage;

// Core
import '../network/network_info.dart';
import '../storage/local_storage.dart';
import '../storage/secure_storage.dart';
import '../services/supabase_service.dart';
import '../services/notification_service.dart';
import '../services/medicine_reminder_service.dart';
import '../services/reminder_notification_integration_service.dart';
import '../services/language_service.dart';

// Data Sources
import '../../data/datasources/auth_remote_data_source.dart';
import '../../data/datasources/auth_local_data_source.dart';
import '../../data/datasources/dashboard_remote_data_source.dart';
import '../../data/datasources/medicine_remote_data_source.dart';
import '../../data/datasources/profile_remote_data_source.dart';
import '../../data/datasources/profile_local_data_source.dart';
import '../../data/datasources/settings_local_data_source.dart';
import '../../data/datasources/settings_remote_data_source.dart';
import '../../data/datasources/reminder_remote_data_source.dart';

// Repositories
import '../../data/repositories/auth_repository_impl.dart';
import '../../data/repositories/dashboard_repository_impl.dart';
import '../../data/repositories/medicine_repository_impl.dart';
import '../../data/repositories/profile_repository_impl.dart';
import '../../data/repositories/tunisia_medicine_repository_impl.dart';
import '../../data/repositories/tag_repository_impl.dart';
import '../../data/repositories/location_repository_impl.dart';
import '../../data/repositories/family_member_repository_impl.dart';
import '../../data/repositories/settings_repository_impl.dart';
import '../../data/repositories/reminder_repository_impl.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/repositories/dashboard_repository.dart';
import '../../domain/repositories/medicine_repository.dart';
import '../../domain/repositories/profile_repository.dart';
import '../../domain/repositories/tunisia_medicine_repository.dart';
import '../../domain/repositories/tag_repository.dart';
import '../../domain/repositories/location_repository.dart';
import '../../domain/repositories/family_member_repository.dart';
import '../../domain/repositories/settings_repository.dart';
import '../../domain/repositories/reminder_repository.dart';

// Use Cases
import '../../domain/usecases/auth/sign_in_usecase.dart';
import '../../domain/usecases/auth/sign_up_usecase.dart';
import '../../domain/usecases/auth/sign_out_usecase.dart';
import '../../domain/usecases/auth/get_current_user_usecase.dart';
import '../../domain/usecases/auth/refresh_profile_usecase.dart';
import '../../domain/usecases/dashboard/get_dashboard_stats_usecase.dart';
import '../../domain/usecases/dashboard/get_filtered_dashboard_stats_usecase.dart';
import '../../domain/usecases/medicine/get_medicines_usecase.dart';
import '../../domain/usecases/medicine/add_medicine_usecase.dart';
import '../../domain/usecases/medicine/search_medicines_usecase.dart';
import '../../domain/usecases/profile/update_profile_name_usecase.dart';
import '../../domain/usecases/profile/update_email_usecase.dart';
import '../../domain/usecases/profile/upload_avatar_usecase.dart';
import '../../domain/usecases/profile/change_password_usecase.dart';
import '../../domain/usecases/profile/delete_account_usecase.dart';

// Reminder Use Cases
import '../../domain/usecases/reminder/add_reminder_usecase.dart';
import '../../domain/usecases/reminder/get_reminders_usecase.dart';
import '../../domain/usecases/reminder/get_batch_reminders_usecase.dart';
import '../../domain/usecases/reminder/update_reminder_usecase.dart';
import '../../domain/usecases/reminder/delete_reminder_usecase.dart';
import '../../domain/usecases/reminder/get_dose_history_usecase.dart';
import '../../domain/usecases/reminder/add_dose_history_usecase.dart';

// BLoCs
import '../../presentation/bloc/auth/auth_bloc.dart';
import '../../presentation/bloc/dashboard/dashboard_bloc.dart';
import '../../presentation/bloc/medicine/medicine_bloc.dart';
import '../../presentation/bloc/profile/profile_bloc.dart';
import '../../presentation/bloc/add_medicine/add_medicine_bloc.dart';
import '../../presentation/bloc/location/location_bloc.dart';
import '../../presentation/bloc/my_medicines/my_medicines_bloc.dart';
import '../../presentation/bloc/family_manager/family_manager_bloc.dart';
import '../../presentation/bloc/medicine_detail/medicine_detail_bloc.dart';
import '../../presentation/bloc/settings/settings_bloc.dart';
import '../../presentation/bloc/profile_security/profile_security_bloc.dart';
import '../../presentation/bloc/personalization/personalization_bloc.dart';
import '../../presentation/bloc/reminder/reminder_bloc.dart';

final getIt = GetIt.instance;

Future<void> configureDependencies() async {
  // External dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerLazySingleton(() => sharedPreferences);

  getIt.registerLazySingleton(() => const FlutterSecureStorage());
  getIt.registerLazySingleton(() => Connectivity());
  // Register Supabase client (initialized in main.dart)
  getIt.registerLazySingleton<SupabaseClient>(() => Supabase.instance.client);

  // Dio configuration
  getIt.registerLazySingleton(() {
    final dio = Dio();
    dio.options.connectTimeout = const Duration(seconds: 30);
    dio.options.receiveTimeout = const Duration(seconds: 30);
    dio.options.sendTimeout = const Duration(seconds: 30);
    return dio;
  });

  // Core
  getIt.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl(getIt()));

  getIt.registerLazySingleton<LocalStorage>(() => LocalStorageImpl(getIt()));

  getIt.registerLazySingleton<SecureStorage>(() => SecureStorageImpl(getIt()));

  // Enhanced Supabase service
  getIt.registerLazySingleton<SupabaseService>(() => SupabaseService.instance);

  // Notification services
  getIt.registerLazySingleton<NotificationService>(() => NotificationService());
  getIt.registerLazySingleton<MedicineReminderService>(
      () => MedicineReminderService());
  getIt.registerLazySingleton<ReminderNotificationIntegrationService>(
      () => ReminderNotificationIntegrationService());

  // Language service
  getIt.registerLazySingleton<LanguageService>(() => LanguageService());

  // Data sources
  getIt.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(supabaseClient: getIt()),
  );

  getIt.registerLazySingleton<AuthLocalDataSource>(
    () =>
        AuthLocalDataSourceImpl(localStorage: getIt(), secureStorage: getIt()),
  );

  getIt.registerLazySingleton<DashboardRemoteDataSource>(
    () => DashboardRemoteDataSourceImpl(supabaseClient: getIt()),
  );

  getIt.registerLazySingleton<MedicineRemoteDataSource>(
    () => MedicineRemoteDataSourceImpl(supabaseClient: getIt()),
  );

  getIt.registerLazySingleton<ProfileRemoteDataSource>(
    () => ProfileRemoteDataSourceImpl(supabaseClient: getIt()),
  );

  getIt.registerLazySingleton<ProfileLocalDataSource>(
    () => ProfileLocalDataSourceImpl(sharedPreferences: getIt()),
  );

  getIt.registerLazySingleton<SettingsRemoteDataSource>(
    () => SettingsRemoteDataSourceImpl(supabaseService: getIt()),
  );

  getIt.registerLazySingleton<SettingsLocalDataSource>(
    () => SettingsLocalDataSourceImpl(sharedPreferences: getIt()),
  );

  getIt.registerLazySingleton<ReminderRemoteDataSource>(
    () => ReminderRemoteDataSourceImpl(supabaseClient: getIt()),
  );

  // Repositories
  getIt.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      remoteDataSource: getIt(),
      localDataSource: getIt(),
      networkInfo: getIt(),
    ),
  );

  getIt.registerLazySingleton<DashboardRepository>(
    () => DashboardRepositoryImpl(
      remoteDataSource: getIt(),
      networkInfo: getIt(),
    ),
  );

  getIt.registerLazySingleton<MedicineRepository>(
    () =>
        MedicineRepositoryImpl(remoteDataSource: getIt(), networkInfo: getIt()),
  );

  getIt.registerLazySingleton<ProfileRepository>(
    () => ProfileRepositoryImpl(
      remoteDataSource: getIt(),
      localDataSource: getIt(),
      networkInfo: getIt(),
    ),
  );

  getIt.registerLazySingleton<TunisiaMedicineRepository>(
    () => TunisiaMedicineRepositoryImpl(getIt()),
  );

  getIt.registerLazySingleton<TagRepository>(
    () => TagRepositoryImpl(getIt()),
  );

  getIt.registerLazySingleton<LocationRepository>(
    () => LocationRepositoryImpl(getIt(), getIt()),
  );

  getIt.registerLazySingleton<FamilyMemberRepository>(
    () => FamilyMemberRepositoryImpl(getIt(), getIt()),
  );

  getIt.registerLazySingleton<SettingsRepository>(
    () => SettingsRepositoryImpl(
      remoteDataSource: getIt(),
      localDataSource: getIt(),
      networkInfo: getIt(),
      localAuth: LocalAuthentication(),
    ),
  );

  getIt.registerLazySingleton<ReminderRepository>(
    () => ReminderRepositoryImpl(
      remoteDataSource: getIt(),
      networkInfo: getIt(),
    ),
  );

  // Use cases
  getIt.registerLazySingleton(() => SignInUseCase(getIt()));
  getIt.registerLazySingleton(() => SignUpUseCase(getIt()));
  getIt.registerLazySingleton(() => SignOutUseCase(getIt()));
  getIt.registerLazySingleton(() => GetCurrentUserUseCase(getIt()));
  getIt.registerLazySingleton(() => RefreshProfileUseCase(getIt()));
  getIt.registerLazySingleton(() => GetDashboardStatsUseCase(getIt()));
  getIt.registerLazySingleton(() => GetFilteredDashboardStatsUseCase(getIt()));
  getIt.registerLazySingleton(() => GetMedicinesUseCase(getIt()));
  getIt.registerLazySingleton(() => AddMedicineUseCase(getIt()));
  getIt.registerLazySingleton(() => SearchMedicinesUseCase(getIt()));
  getIt.registerLazySingleton(() => UpdateProfileNameUseCase(getIt()));
  getIt.registerLazySingleton(() => UpdateEmailUseCase(getIt()));
  getIt.registerLazySingleton(() => UploadAvatarUseCase(getIt()));
  getIt.registerLazySingleton(() => ChangePasswordUseCase(getIt()));
  getIt.registerLazySingleton(() => DeleteAccountUseCase(getIt()));

  // Reminder Use Cases
  getIt.registerLazySingleton(() => GetRemindersUseCase(getIt()));
  getIt.registerLazySingleton(() => GetBatchRemindersUseCase(getIt()));
  getIt.registerLazySingleton(() => AddReminderUseCase(getIt()));
  getIt.registerLazySingleton(() => UpdateReminderUseCase(getIt()));
  getIt.registerLazySingleton(() => DeleteReminderUseCase(getIt()));
  getIt.registerLazySingleton(() => GetDoseHistoryUseCase(getIt()));
  getIt.registerLazySingleton(() => AddDoseHistoryUseCase(getIt()));

  // BLoCs
  getIt.registerFactory(
    () => AuthBloc(
      signInUseCase: getIt(),
      signUpUseCase: getIt(),
      signOutUseCase: getIt(),
      getCurrentUserUseCase: getIt(),
      refreshProfileUseCase: getIt(),
    ),
  );

  getIt.registerFactory(() => DashboardBloc(
        getDashboardStatsUseCase: getIt(),
        getFilteredDashboardStatsUseCase: getIt(),
      ));

  getIt.registerFactory(
    () => MedicineBloc(
      getMedicinesUseCase: getIt(),
      addMedicineUseCase: getIt(),
      searchMedicinesUseCase: getIt(),
    ),
  );

  getIt.registerFactory(
    () => ProfileBloc(
      getCurrentUserUseCase: getIt(),
      updateProfileNameUseCase: getIt(),
      updateEmailUseCase: getIt(),
      uploadAvatarUseCase: getIt(),
      changePasswordUseCase: getIt(),
      deleteAccountUseCase: getIt(),
      profileRepository: getIt(),
    ),
  );

  getIt.registerFactory(
    () => AddMedicineBloc(
      getIt<TunisiaMedicineRepository>(),
      getIt<TagRepository>(),
      getIt<LocationRepository>(),
      getIt<FamilyMemberRepository>(),
      getIt<MedicineRepository>(),
    ),
  );

  getIt.registerFactory(
    () => LocationBloc(getIt<LocationRepository>()),
  );

  getIt.registerFactory(
    () => MyMedicinesBloc(getIt<MedicineRepository>()),
  );

  getIt.registerFactory(
    () => FamilyManagerBloc(getIt<FamilyMemberRepository>()),
  );

  getIt.registerFactory(
    () => MedicineDetailBloc(medicineRepository: getIt()),
  );

  // Settings BLoCs
  getIt.registerFactory(() => SettingsBloc());

  getIt.registerFactory(() => ProfileSecurityBloc());

  getIt.registerFactory(() => PersonalizationBloc());

  getIt.registerFactory(
    () => ReminderBloc(
      getRemindersUseCase: getIt(),
      getBatchRemindersUseCase: getIt(),
      addReminderUseCase: getIt(),
      updateReminderUseCase: getIt(),
      deleteReminderUseCase: getIt(),
      getDoseHistoryUseCase: getIt(),
      addDoseHistoryUseCase: getIt(),
      getMedicinesUseCase: getIt(),
      notificationIntegrationService: getIt(),
    ),
  );
}

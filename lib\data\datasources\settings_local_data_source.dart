import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/settings_model.dart';

/// Abstract class for settings local data source
abstract class SettingsLocalDataSource {
  /// Get user settings from local storage
  Future<SettingsModel?> getUserSettings(String userId);

  /// Save user settings to local storage
  Future<void> saveUserSettings(SettingsModel settings);

  /// Get notification settings from local storage
  Future<NotificationSettingsModel?> getNotificationSettings(String userId);

  /// Save notification settings to local storage
  Future<void> saveNotificationSettings(String userId, NotificationSettingsModel settings);

  /// Get app settings from local storage
  Future<AppSettingsModel?> getAppSettings(String userId);

  /// Save app settings to local storage
  Future<void> saveAppSettings(String userId, AppSettingsModel settings);

  /// Get security settings from local storage
  Future<SecuritySettingsModel?> getSecuritySettings(String userId);

  /// Save security settings to local storage
  Future<void> saveSecuritySettings(String userId, SecuritySettingsModel settings);

  /// Get personalization settings from local storage
  Future<PersonalizationSettingsModel?> getPersonalizationSettings(String userId);

  /// Save personalization settings to local storage
  Future<void> savePersonalizationSettings(String userId, PersonalizationSettingsModel settings);

  /// Clear all settings for a user
  Future<void> clearUserSettings(String userId);

  /// Clear all settings
  Future<void> clearAllSettings();
}

/// Implementation of settings local data source using SharedPreferences
class SettingsLocalDataSourceImpl implements SettingsLocalDataSource {
  final SharedPreferences sharedPreferences;

  static const String _settingsPrefix = 'settings_';
  static const String _notificationPrefix = 'notification_settings_';
  static const String _appPrefix = 'app_settings_';
  static const String _securityPrefix = 'security_settings_';
  static const String _personalizationPrefix = 'personalization_settings_';

  SettingsLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<SettingsModel?> getUserSettings(String userId) async {
    final key = '$_settingsPrefix$userId';
    final jsonString = sharedPreferences.getString(key);
    
    if (jsonString != null) {
      final json = jsonDecode(jsonString);
      return SettingsModel.fromJson(json);
    }
    
    return null;
  }

  @override
  Future<void> saveUserSettings(SettingsModel settings) async {
    final key = '$_settingsPrefix${settings.userId}';
    final jsonString = jsonEncode(settings.toJson());
    await sharedPreferences.setString(key, jsonString);
  }

  @override
  Future<NotificationSettingsModel?> getNotificationSettings(String userId) async {
    final key = '$_notificationPrefix$userId';
    final jsonString = sharedPreferences.getString(key);
    
    if (jsonString != null) {
      final json = jsonDecode(jsonString);
      return NotificationSettingsModel.fromJson(json);
    }
    
    return null;
  }

  @override
  Future<void> saveNotificationSettings(String userId, NotificationSettingsModel settings) async {
    final key = '$_notificationPrefix$userId';
    final jsonString = jsonEncode(settings.toJson());
    await sharedPreferences.setString(key, jsonString);
  }

  @override
  Future<AppSettingsModel?> getAppSettings(String userId) async {
    final key = '$_appPrefix$userId';
    final jsonString = sharedPreferences.getString(key);
    
    if (jsonString != null) {
      final json = jsonDecode(jsonString);
      return AppSettingsModel.fromJson(json);
    }
    
    return null;
  }

  @override
  Future<void> saveAppSettings(String userId, AppSettingsModel settings) async {
    final key = '$_appPrefix$userId';
    final jsonString = jsonEncode(settings.toJson());
    await sharedPreferences.setString(key, jsonString);
  }

  @override
  Future<SecuritySettingsModel?> getSecuritySettings(String userId) async {
    final key = '$_securityPrefix$userId';
    final jsonString = sharedPreferences.getString(key);
    
    if (jsonString != null) {
      final json = jsonDecode(jsonString);
      return SecuritySettingsModel.fromJson(json);
    }
    
    return null;
  }

  @override
  Future<void> saveSecuritySettings(String userId, SecuritySettingsModel settings) async {
    final key = '$_securityPrefix$userId';
    final jsonString = jsonEncode(settings.toJson());
    await sharedPreferences.setString(key, jsonString);
  }

  @override
  Future<PersonalizationSettingsModel?> getPersonalizationSettings(String userId) async {
    final key = '$_personalizationPrefix$userId';
    final jsonString = sharedPreferences.getString(key);
    
    if (jsonString != null) {
      final json = jsonDecode(jsonString);
      return PersonalizationSettingsModel.fromJson(json);
    }
    
    return null;
  }

  @override
  Future<void> savePersonalizationSettings(String userId, PersonalizationSettingsModel settings) async {
    final key = '$_personalizationPrefix$userId';
    final jsonString = jsonEncode(settings.toJson());
    await sharedPreferences.setString(key, jsonString);
  }

  @override
  Future<void> clearUserSettings(String userId) async {
    final keys = [
      '$_settingsPrefix$userId',
      '$_notificationPrefix$userId',
      '$_appPrefix$userId',
      '$_securityPrefix$userId',
      '$_personalizationPrefix$userId',
    ];

    for (final key in keys) {
      await sharedPreferences.remove(key);
    }
  }

  @override
  Future<void> clearAllSettings() async {
    final keys = sharedPreferences.getKeys().where((key) =>
        key.startsWith(_settingsPrefix) ||
        key.startsWith(_notificationPrefix) ||
        key.startsWith(_appPrefix) ||
        key.startsWith(_securityPrefix) ||
        key.startsWith(_personalizationPrefix));

    for (final key in keys) {
      await sharedPreferences.remove(key);
    }
  }
}

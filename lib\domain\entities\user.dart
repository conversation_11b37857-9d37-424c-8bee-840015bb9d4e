import 'package:equatable/equatable.dart';

/// User entity that mirrors the web app's user model
class User extends Equatable {
  final String id;
  final String email;
  final String? name;
  final String? householdId;
  final String? householdName;
  final bool isOnboardingCompleted;
  final int expiryWarningDays; // Stored as months despite the name
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const User({
    required this.id,
    required this.email,
    this.name,
    this.householdId,
    this.householdName,
    this.isOnboardingCompleted = false,
    this.expiryWarningDays = 1, // Default 1 month
    this.createdAt,
    this.updatedAt,
  });

  /// Get user initials for avatar display
  String get initials {
    if (name == null || name!.isEmpty) {
      return email.isNotEmpty ? email[0].toUpperCase() : 'U';
    }
    
    final nameParts = name!.trim().split(' ');
    if (nameParts.length >= 2) {
      return '${nameParts[0][0]}${nameParts[1][0]}'.toUpperCase();
    } else {
      return nameParts[0][0].toUpperCase();
    }
  }

  /// Check if user has completed profile setup
  bool get hasCompleteProfile {
    return name != null && 
           name!.isNotEmpty && 
           householdId != null && 
           householdId!.isNotEmpty;
  }

  /// Check if user needs onboarding
  bool get needsOnboarding {
    return !isOnboardingCompleted || !hasCompleteProfile;
  }

  /// Get display name
  String get displayName {
    return name ?? email.split('@')[0];
  }

  /// Copy with method for immutable updates
  User copyWith({
    String? id,
    String? email,
    String? name,
    String? householdId,
    String? householdName,
    bool? isOnboardingCompleted,
    int? expiryWarningDays,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      householdId: householdId ?? this.householdId,
      householdName: householdName ?? this.householdName,
      isOnboardingCompleted: isOnboardingCompleted ?? this.isOnboardingCompleted,
      expiryWarningDays: expiryWarningDays ?? this.expiryWarningDays,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        name,
        householdId,
        householdName,
        isOnboardingCompleted,
        expiryWarningDays,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'User(id: $id, email: $email, name: $name, householdId: $householdId, '
           'householdName: $householdName, isOnboardingCompleted: $isOnboardingCompleted, '
           'expiryWarningDays: $expiryWarningDays)';
  }
}

import 'package:equatable/equatable.dart';
import '../../../domain/entities/settings.dart';
import '../../../domain/entities/family_member.dart';
import '../../../domain/entities/location.dart';
import '../../../domain/entities/tag.dart';

/// Base class for personalization states
abstract class PersonalizationState extends Equatable {
  const PersonalizationState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class PersonalizationInitial extends PersonalizationState {
  const PersonalizationInitial();
}

/// Loading state
class PersonalizationLoading extends PersonalizationState {
  const PersonalizationLoading();
}

/// Loaded state with personalization data
class PersonalizationLoaded extends PersonalizationState {
  final PersonalizationSettings settings;
  final List<FamilyMember> familyMembers;
  final List<Location> locations;
  final List<Tag> tags;
  final List<FamilyMember> filteredFamilyMembers;
  final List<Location> filteredLocations;
  final List<Tag> filteredTags;
  final String? searchQuery;

  const PersonalizationLoaded({
    required this.settings,
    required this.familyMembers,
    required this.locations,
    required this.tags,
    List<FamilyMember>? filteredFamilyMembers,
    List<Location>? filteredLocations,
    List<Tag>? filteredTags,
    this.searchQuery,
  })  : filteredFamilyMembers = filteredFamilyMembers ?? familyMembers,
        filteredLocations = filteredLocations ?? locations,
        filteredTags = filteredTags ?? tags;

  @override
  List<Object?> get props => [
        settings,
        familyMembers,
        locations,
        tags,
        filteredFamilyMembers,
        filteredLocations,
        filteredTags,
        searchQuery,
      ];

  /// Copy with method for state updates
  PersonalizationLoaded copyWith({
    PersonalizationSettings? settings,
    List<FamilyMember>? familyMembers,
    List<Location>? locations,
    List<Tag>? tags,
    List<FamilyMember>? filteredFamilyMembers,
    List<Location>? filteredLocations,
    List<Tag>? filteredTags,
    String? searchQuery,
  }) {
    return PersonalizationLoaded(
      settings: settings ?? this.settings,
      familyMembers: familyMembers ?? this.familyMembers,
      locations: locations ?? this.locations,
      tags: tags ?? this.tags,
      filteredFamilyMembers: filteredFamilyMembers ?? this.filteredFamilyMembers,
      filteredLocations: filteredLocations ?? this.filteredLocations,
      filteredTags: filteredTags ?? this.filteredTags,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  /// Get default location entity
  Location? get defaultLocationEntity {
    if (settings.defaultLocation.isEmpty) return null;
    return locations.where((l) => l.id == settings.defaultLocation).firstOrNull;
  }

  /// Get default family member entity
  FamilyMember? get defaultFamilyMemberEntity {
    if (settings.defaultFamilyMember.isEmpty) return null;
    return familyMembers.where((m) => m.id == settings.defaultFamilyMember).firstOrNull;
  }

  /// Get therapeutic tags
  List<Tag> get therapeuticTags {
    return tags.where((tag) => tag.category == 'therapeutic').toList();
  }

  /// Get usage tags
  List<Tag> get usageTags {
    return tags.where((tag) => tag.category == 'usage').toList();
  }
}

/// Error state
class PersonalizationError extends PersonalizationState {
  final String message;
  final PersonalizationSettings? settings;
  final List<FamilyMember>? familyMembers;
  final List<Location>? locations;
  final List<Tag>? tags;

  const PersonalizationError({
    required this.message,
    this.settings,
    this.familyMembers,
    this.locations,
    this.tags,
  });

  @override
  List<Object?> get props => [message, settings, familyMembers, locations, tags];
}

/// Success state for operations
class PersonalizationOperationSuccess extends PersonalizationState {
  final String message;
  final PersonalizationSettings settings;
  final List<FamilyMember> familyMembers;
  final List<Location> locations;
  final List<Tag> tags;

  const PersonalizationOperationSuccess({
    required this.message,
    required this.settings,
    required this.familyMembers,
    required this.locations,
    required this.tags,
  });

  @override
  List<Object?> get props => [message, settings, familyMembers, locations, tags];
}

/// State for updating settings
class PersonalizationSettingsUpdating extends PersonalizationState {
  final PersonalizationSettings settings;
  final String section; // 'threshold', 'location', 'member', 'categories'

  const PersonalizationSettingsUpdating({
    required this.settings,
    required this.section,
  });

  @override
  List<Object?> get props => [settings, section];
}

/// State for creating/updating family member
class FamilyMemberOperating extends PersonalizationState {
  final FamilyMember? member;
  final String operation; // 'create', 'update', 'delete'

  const FamilyMemberOperating({
    this.member,
    required this.operation,
  });

  @override
  List<Object?> get props => [member, operation];
}

/// State for creating/updating location
class LocationOperating extends PersonalizationState {
  final Location? location;
  final String operation; // 'create', 'update', 'delete'

  const LocationOperating({
    this.location,
    required this.operation,
  });

  @override
  List<Object?> get props => [location, operation];
}

/// State for creating/updating tag
class TagOperating extends PersonalizationState {
  final Tag? tag;
  final String operation; // 'create', 'update', 'delete'

  const TagOperating({
    this.tag,
    required this.operation,
  });

  @override
  List<Object?> get props => [tag, operation];
}

/// State for family member operation success
class FamilyMemberOperationSuccess extends PersonalizationState {
  final String message;
  final FamilyMember? member;
  final String operation;
  final List<FamilyMember> familyMembers;

  const FamilyMemberOperationSuccess({
    required this.message,
    this.member,
    required this.operation,
    required this.familyMembers,
  });

  @override
  List<Object?> get props => [message, member, operation, familyMembers];
}

/// State for location operation success
class LocationOperationSuccess extends PersonalizationState {
  final String message;
  final Location? location;
  final String operation;
  final List<Location> locations;

  const LocationOperationSuccess({
    required this.message,
    this.location,
    required this.operation,
    required this.locations,
  });

  @override
  List<Object?> get props => [message, location, operation, locations];
}

/// State for tag operation success
class TagOperationSuccess extends PersonalizationState {
  final String message;
  final Tag? tag;
  final String operation;
  final List<Tag> tags;

  const TagOperationSuccess({
    required this.message,
    this.tag,
    required this.operation,
    required this.tags,
  });

  @override
  List<Object?> get props => [message, tag, operation, tags];
}

/// State for search results
class PersonalizationSearchResult extends PersonalizationState {
  final String query;
  final List<FamilyMember> familyMembers;
  final List<Location> locations;
  final List<Tag> tags;

  const PersonalizationSearchResult({
    required this.query,
    required this.familyMembers,
    required this.locations,
    required this.tags,
  });

  @override
  List<Object?> get props => [query, familyMembers, locations, tags];
}

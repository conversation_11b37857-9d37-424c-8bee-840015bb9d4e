import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/reminder.dart';
import '../../repositories/reminder_repository.dart';

@injectable
class AddReminderUseCase implements UseCase<Reminder, AddReminderParams> {
  final ReminderRepository repository;

  AddReminderUseCase(this.repository);

  @override
  Future<Either<Failure, Reminder>> call(AddReminderParams params) async {
    return await repository.addReminder(params.reminder);
  }
}

class AddReminderParams {
  final Reminder reminder;

  AddReminderParams({required this.reminder});
}

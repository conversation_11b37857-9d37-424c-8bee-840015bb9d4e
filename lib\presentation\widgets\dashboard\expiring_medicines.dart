import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/medicine.dart';
import '../../bloc/medicine/medicine_bloc.dart';

class ExpiringMedicines extends StatelessWidget {
  const ExpiringMedicines({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MedicineBloc, MedicineState>(
      builder: (context, state) {
        if (state is MedicineLoading) {
          return _buildLoadingState();
        }

        if (state is MedicineError) {
          return _buildErrorState(state.message);
        }

        if (state is MedicineLoaded) {
          // Filter medicines expiring in the next 30 days
          final now = DateTime.now();
          final thirtyDaysFromNow = now.add(const Duration(days: 30));

          final expiringMedicines = state.medicines.where((medicine) {
            return medicine.expiration != null &&
                medicine.expiration!.isAfter(now) &&
                medicine.expiration!.isBefore(thirtyDaysFromNow);
          }).toList()
            ..sort((a, b) => a.expiration!
                .compareTo(b.expiration!)); // Sort by expiration date

          if (expiringMedicines.isEmpty) {
            return _buildEmptyState();
          }

          return _buildExpiringMedicinesList(context, expiringMedicines);
        }

        return _buildEmptyState();
      },
    );
  }

  Widget _buildLoadingState() {
    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.grey200),
      ),
      child: Center(
        child: CircularProgressIndicator(
          color: AppColors.teal,
        ),
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.grey200),
      ),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: AppColors.error,
          ),
          const SizedBox(height: 12),
          Text(
            'Erreur de chargement',
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.error,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            message,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.grey500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.grey50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.grey200),
      ),
      child: Column(
        children: [
          Icon(
            Icons.schedule,
            size: 48,
            color: AppColors.grey400,
          ),
          const SizedBox(height: 12),
          Text(
            'Aucun médicament expirant',
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.grey600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Les médicaments expirant bientôt apparaîtront ici',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.grey500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildExpiringMedicinesList(
      BuildContext context, List<Medicine> expiringMedicines) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.grey200),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.warning,
                  color: AppColors.warning,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '${expiringMedicines.length} médicament${expiringMedicines.length > 1 ? 's' : ''} expirant bientôt',
                    style: AppTextStyles.titleSmall.copyWith(
                      color: AppColors.warning,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Medicine list
          ...expiringMedicines.take(5).map((medicine) => _ExpiringMedicineItem(
                medicine: medicine,
                onTap: () => context.push('/medicines/${medicine.id}'),
              )),

          // View all button if more than 5
          if (expiringMedicines.length > 5)
            Padding(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () =>
                      context.push('/medicines/my?filter=expiring_soon'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.warning,
                    foregroundColor: AppColors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: Text(
                      'Voir tous les médicaments expirant (${expiringMedicines.length})'),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class _ExpiringMedicineItem extends StatelessWidget {
  final Medicine medicine;
  final VoidCallback onTap;

  const _ExpiringMedicineItem({
    required this.medicine,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final daysUntilExpiration = medicine.daysUntilExpiration ?? 0;
    final isUrgent = daysUntilExpiration <= 7;

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: AppColors.grey200,
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            // Warning icon
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isUrgent
                    ? AppColors.error.withValues(alpha: 0.1)
                    : AppColors.warning.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                isUrgent ? Icons.error : Icons.warning,
                color: isUrgent ? AppColors.error : AppColors.warning,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),

            // Medicine info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    medicine.displayName,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      if (medicine.locationName != null) ...[
                        Icon(
                          Icons.location_on,
                          size: 14,
                          color: AppColors.grey500,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          medicine.locationName!,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.grey600,
                          ),
                        ),
                        const SizedBox(width: 12),
                      ],
                      Icon(
                        Icons.inventory,
                        size: 14,
                        color: AppColors.grey500,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${medicine.quantity}',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.grey600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Expiration info
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  medicine.formattedExpiration,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: isUrgent ? AppColors.error : AppColors.warning,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '$daysUntilExpiration jour${daysUntilExpiration > 1 ? 's' : ''}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.grey500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

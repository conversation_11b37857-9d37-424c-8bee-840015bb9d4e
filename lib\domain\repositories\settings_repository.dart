import 'package:dartz/dartz.dart';
import '../entities/settings.dart';
import '../../core/error/failures.dart';

/// Repository interface for settings management
abstract class SettingsRepository {
  /// Get user settings
  /// 
  /// [userId] - User ID
  /// 
  /// Returns a Future with Either failure or Settings entity
  Future<Either<Failure, Settings>> getUserSettings(String userId);

  /// Update user settings
  /// 
  /// [settings] - Settings entity to update
  /// 
  /// Returns a Future with Either failure or updated Settings entity
  Future<Either<Failure, Settings>> updateUserSettings(Settings settings);

  /// Get notification settings
  /// 
  /// [userId] - User ID
  /// 
  /// Returns a Future with Either failure or NotificationSettings entity
  Future<Either<Failure, NotificationSettings>> getNotificationSettings(String userId);

  /// Update notification settings
  /// 
  /// [userId] - User ID
  /// [settings] - NotificationSettings entity to update
  /// 
  /// Returns a Future with Either failure or updated NotificationSettings entity
  Future<Either<Failure, NotificationSettings>> updateNotificationSettings(
    String userId,
    NotificationSettings settings,
  );

  /// Get app settings
  /// 
  /// [userId] - User ID
  /// 
  /// Returns a Future with Either failure or AppSettings entity
  Future<Either<Failure, AppSettings>> getAppSettings(String userId);

  /// Update app settings
  /// 
  /// [userId] - User ID
  /// [settings] - AppSettings entity to update
  /// 
  /// Returns a Future with Either failure or updated AppSettings entity
  Future<Either<Failure, AppSettings>> updateAppSettings(
    String userId,
    AppSettings settings,
  );

  /// Get security settings
  /// 
  /// [userId] - User ID
  /// 
  /// Returns a Future with Either failure or SecuritySettings entity
  Future<Either<Failure, SecuritySettings>> getSecuritySettings(String userId);

  /// Update security settings
  /// 
  /// [userId] - User ID
  /// [settings] - SecuritySettings entity to update
  /// 
  /// Returns a Future with Either failure or updated SecuritySettings entity
  Future<Either<Failure, SecuritySettings>> updateSecuritySettings(
    String userId,
    SecuritySettings settings,
  );

  /// Get personalization settings
  /// 
  /// [userId] - User ID
  /// 
  /// Returns a Future with Either failure or PersonalizationSettings entity
  Future<Either<Failure, PersonalizationSettings>> getPersonalizationSettings(String userId);

  /// Update personalization settings
  /// 
  /// [userId] - User ID
  /// [settings] - PersonalizationSettings entity to update
  /// 
  /// Returns a Future with Either failure or updated PersonalizationSettings entity
  Future<Either<Failure, PersonalizationSettings>> updatePersonalizationSettings(
    String userId,
    PersonalizationSettings settings,
  );

  /// Reset settings to default values
  /// 
  /// [userId] - User ID
  /// [householdId] - Household ID
  /// 
  /// Returns a Future with Either failure or default Settings entity
  Future<Either<Failure, Settings>> resetToDefaults(String userId, String householdId);

  /// Export user settings as JSON
  /// 
  /// [userId] - User ID
  /// 
  /// Returns a Future with Either failure or JSON string
  Future<Either<Failure, String>> exportSettings(String userId);

  /// Import user settings from JSON
  /// 
  /// [userId] - User ID
  /// [jsonData] - JSON string containing settings data
  /// 
  /// Returns a Future with Either failure or imported Settings entity
  Future<Either<Failure, Settings>> importSettings(String userId, String jsonData);

  /// Check if biometric authentication is available on device
  /// 
  /// Returns a Future with Either failure or boolean indicating availability
  Future<Either<Failure, bool>> isBiometricAvailable();

  /// Validate PIN code
  /// 
  /// [pinCode] - PIN code to validate
  /// 
  /// Returns a Future with Either failure or boolean indicating validity
  Future<Either<Failure, bool>> validatePinCode(String pinCode);

  /// Update user's expiry warning threshold
  /// 
  /// [userId] - User ID
  /// [days] - Number of days for expiry warning
  /// 
  /// Returns a Future with Either failure or success
  Future<Either<Failure, void>> updateExpiryWarningThreshold(String userId, int days);
}

import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/reminder.dart';
import '../../repositories/reminder_repository.dart';

@injectable
class GetRemindersUseCase implements UseCase<List<Reminder>, GetRemindersParams> {
  final ReminderRepository repository;

  GetRemindersUseCase(this.repository);

  @override
  Future<Either<Failure, List<Reminder>>> call(GetRemindersParams params) async {
    return await repository.getRemindersForUserMedicine(params.userMedicineId);
  }
}

class GetRemindersParams {
  final String userMedicineId;

  GetRemindersParams({required this.userMedicineId});
}

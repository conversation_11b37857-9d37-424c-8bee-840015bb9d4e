import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../bloc/my_medicines/my_medicines_event.dart';

class MedicineSortDialog extends StatelessWidget {
  final MedicineSortOption currentSort;
  final Function(MedicineSortOption) onSortChanged;

  const MedicineSortDialog({
    super.key,
    required this.currentSort,
    required this.onSortChanged,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        'Trier par',
        style: AppTextStyles.titleMedium.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      contentPadding: const EdgeInsets.symmetric(vertical: 16),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildSortGroup(
              context,
              title: 'Nom',
              options: [
                MedicineSortOption.nameAsc,
                MedicineSortOption.nameDesc,
              ],
            ),
            const Divider(height: 1),
            _buildSortGroup(
              context,
              title: 'Date d\'expiration',
              options: [
                MedicineSortOption.expirationAsc,
                MedicineSortOption.expirationDesc,
              ],
            ),
            const Divider(height: 1),
            _buildSortGroup(
              context,
              title: 'Quantité',
              options: [
                MedicineSortOption.quantityAsc,
                MedicineSortOption.quantityDesc,
              ],
            ),
            const Divider(height: 1),
            _buildSortGroup(
              context,
              title: 'Date d\'ajout',
              options: [
                MedicineSortOption.createdAtAsc,
                MedicineSortOption.createdAtDesc,
              ],
            ),
            const Divider(height: 1),
            _buildSortGroup(
              context,
              title: 'Emplacement',
              options: [
                MedicineSortOption.locationAsc,
                MedicineSortOption.locationDesc,
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Fermer'),
        ),
      ],
    );
  }

  Widget _buildSortGroup(
    BuildContext context, {
    required String title,
    required List<MedicineSortOption> options,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
          child: Text(
            title,
            style: AppTextStyles.labelMedium.copyWith(
              color: AppColors.grey600,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        ...options.map((option) => _buildSortOption(context, option)),
      ],
    );
  }

  Widget _buildSortOption(BuildContext context, MedicineSortOption option) {
    final isSelected = currentSort == option;

    return ListTile(
      dense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 24),
      leading: Icon(
        _getSortIcon(option),
        size: 20,
        color: isSelected ? AppColors.teal : AppColors.grey600,
      ),
      title: Text(
        option.displayName,
        style: AppTextStyles.bodyMedium.copyWith(
          color: isSelected ? AppColors.teal : AppColors.grey800,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
        ),
      ),
      trailing: isSelected
          ? Icon(
              Icons.check,
              color: AppColors.teal,
              size: 20,
            )
          : null,
      onTap: () {
        onSortChanged(option);
        Navigator.of(context).pop();
      },
    );
  }

  IconData _getSortIcon(MedicineSortOption option) {
    IconData baseIcon;

    switch (option) {
      case MedicineSortOption.nameAsc:
      case MedicineSortOption.nameDesc:
        baseIcon = Icons.sort_by_alpha;
        break;
      case MedicineSortOption.expirationAsc:
      case MedicineSortOption.expirationDesc:
        baseIcon = Icons.schedule;
        break;
      case MedicineSortOption.quantityAsc:
      case MedicineSortOption.quantityDesc:
        baseIcon = Icons.numbers;
        break;
      case MedicineSortOption.createdAtAsc:
      case MedicineSortOption.createdAtDesc:
        baseIcon = Icons.access_time;
        break;
      case MedicineSortOption.locationAsc:
      case MedicineSortOption.locationDesc:
        baseIcon = Icons.location_on;
        break;
    }

    return baseIcon;
  }
}

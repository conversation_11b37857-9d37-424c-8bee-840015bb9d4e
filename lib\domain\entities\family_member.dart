import 'package:equatable/equatable.dart';

/// Family member entity representing household members
class FamilyMember extends Equatable {
  final String id;
  final String householdId;
  final String name;
  final String? relationship;
  final DateTime? dateOfBirth;
  final String? gender;
  final String? notes;
  final String? avatarUrl;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const FamilyMember({
    required this.id,
    required this.householdId,
    required this.name,
    this.relationship,
    this.dateOfBirth,
    this.gender,
    this.notes,
    this.avatarUrl,
    required this.createdAt,
    this.updatedAt,
  });

  /// Get display name with proper capitalization
  String get displayName {
    return name.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  /// Get age if date of birth is available
  int? get age {
    if (dateOfBirth == null) return null;
    
    final now = DateTime.now();
    final difference = now.difference(dateOfBirth!);
    return (difference.inDays / 365).floor();
  }

  /// Get age group for medicine recommendations
  String get ageGroup {
    final currentAge = age;
    if (currentAge == null) return 'unknown';
    
    if (currentAge < 2) return 'infant';
    if (currentAge < 12) return 'child';
    if (currentAge < 18) return 'adolescent';
    if (currentAge < 65) return 'adult';
    return 'senior';
  }

  /// Get relationship display name
  String get relationshipDisplayName {
    if (relationship == null || relationship!.isEmpty) {
      return 'Membre de la famille';
    }
    
    switch (relationship!.toLowerCase()) {
      case 'parent':
        return 'Parent';
      case 'child':
        return 'Enfant';
      case 'spouse':
        return 'Conjoint(e)';
      case 'sibling':
        return 'Frère/Sœur';
      case 'grandparent':
        return 'Grand-parent';
      case 'grandchild':
        return 'Petit-enfant';
      case 'other':
        return 'Autre';
      default:
        return relationship!;
    }
  }

  /// Get gender display name
  String get genderDisplayName {
    if (gender == null || gender!.isEmpty) {
      return 'Non spécifié';
    }
    
    switch (gender!.toLowerCase()) {
      case 'male':
        return 'Homme';
      case 'female':
        return 'Femme';
      case 'other':
        return 'Autre';
      default:
        return gender!;
    }
  }

  /// Copy with method for immutable updates
  FamilyMember copyWith({
    String? id,
    String? householdId,
    String? name,
    String? relationship,
    DateTime? dateOfBirth,
    String? gender,
    String? notes,
    String? avatarUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FamilyMember(
      id: id ?? this.id,
      householdId: householdId ?? this.householdId,
      name: name ?? this.name,
      relationship: relationship ?? this.relationship,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      notes: notes ?? this.notes,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        householdId,
        name,
        relationship,
        dateOfBirth,
        gender,
        notes,
        avatarUrl,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'FamilyMember(id: $id, name: $name, relationship: $relationship, age: $age)';
  }
}

/// Predefined relationship types
class RelationshipTypes {
  static const List<Map<String, String>> values = [
    {'value': 'self', 'label': 'Moi-même'},
    {'value': 'spouse', 'label': 'Conjoint(e)'},
    {'value': 'child', 'label': 'Enfant'},
    {'value': 'parent', 'label': 'Parent'},
    {'value': 'sibling', 'label': 'Frère/Sœur'},
    {'value': 'grandparent', 'label': 'Grand-parent'},
    {'value': 'grandchild', 'label': 'Petit-enfant'},
    {'value': 'other', 'label': 'Autre'},
  ];
}

/// Gender types
class GenderTypes {
  static const List<Map<String, String>> values = [
    {'value': 'male', 'label': 'Homme'},
    {'value': 'female', 'label': 'Femme'},
    {'value': 'other', 'label': 'Autre'},
  ];
}

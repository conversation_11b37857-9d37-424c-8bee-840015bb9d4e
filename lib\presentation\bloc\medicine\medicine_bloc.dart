import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../domain/entities/medicine.dart';
import '../../../domain/usecases/medicine/get_medicines_usecase.dart';
import '../../../domain/usecases/medicine/add_medicine_usecase.dart';
import '../../../domain/usecases/medicine/search_medicines_usecase.dart';
import '../../../core/usecases/usecase.dart';

// Events
abstract class MedicineEvent extends Equatable {
  const MedicineEvent();
  @override
  List<Object?> get props => [];
}

class MedicineLoadRequested extends MedicineEvent {
  final String householdId;
  const MedicineLoadRequested({required this.householdId});
  @override
  List<Object?> get props => [householdId];
}

class MedicineClearRequested extends MedicineEvent {
  const MedicineClearRequested();
}

class MedicineDeleteRequested extends MedicineEvent {
  final String medicineId;
  final String householdId;
  const MedicineDeleteRequested(
      {required this.medicineId, required this.householdId});
  @override
  List<Object?> get props => [medicineId, householdId];
}

class MedicineSearchRequested extends MedicineEvent {
  final String householdId;
  final String query;
  const MedicineSearchRequested({
    required this.householdId,
    required this.query,
  });
  @override
  List<Object?> get props => [householdId, query];
}

// States
abstract class MedicineState extends Equatable {
  const MedicineState();
  @override
  List<Object?> get props => [];
}

class MedicineInitial extends MedicineState {
  const MedicineInitial();
}

class MedicineLoading extends MedicineState {
  const MedicineLoading();
}

class MedicineLoaded extends MedicineState {
  final List<Medicine> medicines;
  const MedicineLoaded({required this.medicines});
  @override
  List<Object?> get props => [medicines];
}

class MedicineError extends MedicineState {
  final String message;
  const MedicineError({required this.message});
  @override
  List<Object?> get props => [message];
}

class MedicineSearchResults extends MedicineState {
  final List<Medicine> searchResults;
  final String query;
  const MedicineSearchResults({
    required this.searchResults,
    required this.query,
  });
  @override
  List<Object?> get props => [searchResults, query];
}

// BLoC
class MedicineBloc extends Bloc<MedicineEvent, MedicineState> {
  final GetMedicinesUseCase getMedicinesUseCase;
  final AddMedicineUseCase addMedicineUseCase;
  final SearchMedicinesUseCase searchMedicinesUseCase;

  MedicineBloc({
    required this.getMedicinesUseCase,
    required this.addMedicineUseCase,
    required this.searchMedicinesUseCase,
  }) : super(const MedicineInitial()) {
    on<MedicineLoadRequested>(_onLoadRequested);
    on<MedicineClearRequested>(_onClearRequested);
    on<MedicineDeleteRequested>(_onDeleteRequested);
    on<MedicineSearchRequested>(_onSearchRequested);
  }

  Future<void> _onLoadRequested(
    MedicineLoadRequested event,
    Emitter<MedicineState> emit,
  ) async {
    emit(const MedicineLoading());

    try {
      final result = await getMedicinesUseCase(
          HouseholdParams(householdId: event.householdId));
      result.fold(
        (failure) => emit(MedicineError(message: failure.message)),
        (medicines) => emit(MedicineLoaded(medicines: medicines)),
      );
    } catch (e) {
      emit(MedicineError(message: 'Failed to load medicines: ${e.toString()}'));
    }
  }

  Future<void> _onClearRequested(
    MedicineClearRequested event,
    Emitter<MedicineState> emit,
  ) async {
    emit(const MedicineInitial());
  }

  Future<void> _onDeleteRequested(
    MedicineDeleteRequested event,
    Emitter<MedicineState> emit,
  ) async {
    // Note: This is a placeholder implementation
    // Real deletion should be handled by MyMedicinesBloc or a dedicated DeleteMedicineUseCase
    try {
      emit(const MedicineLoading());

      // Reload the list to reflect any changes
      add(MedicineLoadRequested(householdId: event.householdId));
    } catch (e) {
      emit(
          MedicineError(message: 'Failed to delete medicine: ${e.toString()}'));
    }
  }

  Future<void> _onSearchRequested(
    MedicineSearchRequested event,
    Emitter<MedicineState> emit,
  ) async {
    if (event.query.trim().isEmpty) {
      // If query is empty, load all medicines
      add(MedicineLoadRequested(householdId: event.householdId));
      return;
    }

    emit(const MedicineLoading());

    try {
      final result = await searchMedicinesUseCase(
        SearchMedicinesParams(
          householdId: event.householdId,
          query: event.query,
        ),
      );
      result.fold(
        (failure) => emit(MedicineError(message: failure.message)),
        (medicines) => emit(MedicineSearchResults(
          searchResults: medicines,
          query: event.query,
        )),
      );
    } catch (e) {
      emit(MedicineError(
          message: 'Failed to search medicines: ${e.toString()}'));
    }
  }
}

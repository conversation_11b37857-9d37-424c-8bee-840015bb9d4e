import 'package:equatable/equatable.dart';

import '../../../domain/entities/reminder.dart';

abstract class ReminderState extends Equatable {
  const ReminderState();

  @override
  List<Object?> get props => [];
}

class ReminderInitial extends ReminderState {}

class ReminderLoading extends ReminderState {}

class RemindersLoaded extends ReminderState {
  final List<Reminder> reminders;

  const RemindersLoaded(this.reminders);

  @override
  List<Object?> get props => [reminders];
}

class ReminderAdded extends ReminderState {
  final Reminder reminder;

  const ReminderAdded(this.reminder);

  @override
  List<Object?> get props => [reminder];
}

class ReminderUpdated extends ReminderState {
  final Reminder reminder;

  const ReminderUpdated(this.reminder);

  @override
  List<Object?> get props => [reminder];
}

class ReminderDeleted extends ReminderState {
  final String reminderId;

  const ReminderDeleted(this.reminderId);

  @override
  List<Object?> get props => [reminderId];
}

class DoseHistoryLoaded extends ReminderState {
  final List<DoseHistory> doseHistory;

  const DoseHistoryLoaded(this.doseHistory);

  @override
  List<Object?> get props => [doseHistory];
}

class DoseHistoryAdded extends ReminderState {
  final DoseHistory doseHistory;

  const DoseHistoryAdded(this.doseHistory);

  @override
  List<Object?> get props => [doseHistory];
}

class ReminderError extends ReminderState {
  final String message;

  const ReminderError(this.message);

  @override
  List<Object?> get props => [message];
}

class ReminderOperationSuccess extends ReminderState {
  final String message;

  const ReminderOperationSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

import 'package:dartz/dartz.dart';
import '../../entities/medicine.dart';
import '../../repositories/medicine_repository.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';

class GetMedicinesUseCase implements UseCase<List<Medicine>, HouseholdParams> {
  final MedicineRepository repository;

  GetMedicinesUseCase(this.repository);

  @override
  Future<Either<Failure, List<Medicine>>> call(HouseholdParams params) async {
    return await repository.getMedicines(params.householdId);
  }
}

import 'package:equatable/equatable.dart';
import '../../../domain/entities/tunisia_medicine.dart';
import '../../../domain/entities/tag.dart';
import '../../../domain/entities/location.dart';
import '../../../domain/entities/family_member.dart';

/// States for AddMedicineBloc
abstract class AddMedicineState extends Equatable {
  const AddMedicineState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class AddMedicineInitial extends AddMedicineState {
  const AddMedicineInitial();
}

/// Loading state
class AddMedicineLoading extends AddMedicineState {
  const AddMedicineLoading();
}

/// Main form state with all data
class AddMedicineFormState extends AddMedicineState {
  final String householdId;
  final bool isCustomMode;
  final bool isSearching;
  final List<TunisiaMedicine> searchResults;
  final TunisiaMedicine? selectedMedicine;
  final String? selectedMedicineName;
  final List<TunisiaMedicine> availableDosageForms;
  final List<Tag> availableTags;
  final List<Tag> selectedTags;
  final List<Location> availableLocations;
  final Location? selectedLocation;
  final List<FamilyMember> availableFamilyMembers;
  final FamilyMember? selectedFamilyMember;
  final Map<String, dynamic> formData;
  final Map<String, String?> formErrors;
  final bool isSubmitting;
  final bool isValid;

  const AddMedicineFormState({
    required this.householdId,
    this.isCustomMode = false,
    this.isSearching = false,
    this.searchResults = const [],
    this.selectedMedicine,
    this.selectedMedicineName,
    this.availableDosageForms = const [],
    this.availableTags = const [],
    this.selectedTags = const [],
    this.availableLocations = const [],
    this.selectedLocation,
    this.availableFamilyMembers = const [],
    this.selectedFamilyMember,
    this.formData = const {},
    this.formErrors = const {},
    this.isSubmitting = false,
    this.isValid = false,
  });

  /// Get form field value
  T? getFormValue<T>(String field) {
    return formData[field] as T?;
  }

  /// Get form field error
  String? getFormError(String field) {
    return formErrors[field];
  }

  /// Check if form has any errors
  bool get hasErrors => formErrors.values.any((error) => error != null);

  /// Get medicine name (from selected medicine or custom input)
  String? get medicineName {
    if (!isCustomMode && selectedMedicine != null) {
      return selectedMedicine!.nom;
    }
    return getFormValue<String>('customName');
  }

  /// Get medicine dosage (from selected medicine or custom input)
  String? get medicineDosage {
    if (!isCustomMode && selectedMedicine != null) {
      return selectedMedicine!.dosage;
    }
    return getFormValue<String>('dosage');
  }

  /// Get medicine form (from selected medicine or custom input)
  String? get medicineForm {
    if (!isCustomMode && selectedMedicine != null) {
      return selectedMedicine!.forme;
    }
    return getFormValue<String>('form');
  }

  /// Update form data
  AddMedicineFormState updateFormData(String field, dynamic value) {
    final newFormData = Map<String, dynamic>.from(formData);
    newFormData[field] = value;
    return copyWith(formData: newFormData);
  }

  /// Update form errors
  AddMedicineFormState updateFormErrors(Map<String, String?> errors) {
    return copyWith(formErrors: errors);
  }

  /// Copy with method for immutable updates
  AddMedicineFormState copyWith({
    String? householdId,
    bool? isCustomMode,
    bool? isSearching,
    List<TunisiaMedicine>? searchResults,
    TunisiaMedicine? selectedMedicine,
    bool clearSelectedMedicine = false,
    String? selectedMedicineName,
    bool clearSelectedMedicineName = false,
    List<TunisiaMedicine>? availableDosageForms,
    List<Tag>? availableTags,
    List<Tag>? selectedTags,
    List<Location>? availableLocations,
    Location? selectedLocation,
    bool clearSelectedLocation = false,
    List<FamilyMember>? availableFamilyMembers,
    FamilyMember? selectedFamilyMember,
    bool clearSelectedFamilyMember = false,
    Map<String, dynamic>? formData,
    Map<String, String?>? formErrors,
    bool? isSubmitting,
    bool? isValid,
  }) {
    return AddMedicineFormState(
      householdId: householdId ?? this.householdId,
      isCustomMode: isCustomMode ?? this.isCustomMode,
      isSearching: isSearching ?? this.isSearching,
      searchResults: searchResults ?? this.searchResults,
      selectedMedicine: clearSelectedMedicine
          ? null
          : (selectedMedicine ?? this.selectedMedicine),
      selectedMedicineName: clearSelectedMedicineName
          ? null
          : (selectedMedicineName ?? this.selectedMedicineName),
      availableDosageForms: availableDosageForms ?? this.availableDosageForms,
      availableTags: availableTags ?? this.availableTags,
      selectedTags: selectedTags ?? this.selectedTags,
      availableLocations: availableLocations ?? this.availableLocations,
      selectedLocation: clearSelectedLocation
          ? null
          : (selectedLocation ?? this.selectedLocation),
      availableFamilyMembers:
          availableFamilyMembers ?? this.availableFamilyMembers,
      selectedFamilyMember: clearSelectedFamilyMember
          ? null
          : (selectedFamilyMember ?? this.selectedFamilyMember),
      formData: formData ?? this.formData,
      formErrors: formErrors ?? this.formErrors,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      isValid: isValid ?? this.isValid,
    );
  }

  @override
  List<Object?> get props => [
        householdId,
        isCustomMode,
        isSearching,
        searchResults,
        selectedMedicine,
        availableTags,
        selectedTags,
        availableLocations,
        selectedLocation,
        availableFamilyMembers,
        selectedFamilyMember,
        formData,
        formErrors,
        isSubmitting,
        isValid,
      ];
}

/// Success state after medicine is added
class AddMedicineSuccess extends AddMedicineState {
  final String message;

  const AddMedicineSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

/// Error state
class AddMedicineError extends AddMedicineState {
  final String message;

  const AddMedicineError({required this.message});

  @override
  List<Object?> get props => [message];
}

import 'package:shared_preferences/shared_preferences.dart';

abstract class ProfileLocalDataSource {
  Future<void> saveLanguagePreference(String language);
  Future<String> getLanguagePreference();
  Future<void> saveThemePreference(bool isDarkMode);
  Future<bool> getThemePreference();
  Future<void> clearPreferences();
}

class ProfileLocalDataSourceImpl implements ProfileLocalDataSource {
  final SharedPreferences sharedPreferences;

  static const String _languageKey = 'language_preference';
  static const String _themeKey = 'theme_preference';

  ProfileLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<void> saveLanguagePreference(String language) async {
    await sharedPreferences.setString(_languageKey, language);
  }

  @override
  Future<String> getLanguagePreference() async {
    return sharedPreferences.getString(_languageKey) ?? 'fr';
  }

  @override
  Future<void> saveThemePreference(bool isDarkMode) async {
    await sharedPreferences.setBool(_themeKey, isDarkMode);
  }

  @override
  Future<bool> getThemePreference() async {
    return sharedPreferences.getBool(_themeKey) ?? false;
  }

  @override
  Future<void> clearPreferences() async {
    await sharedPreferences.remove(_languageKey);
    await sharedPreferences.remove(_themeKey);
  }
}

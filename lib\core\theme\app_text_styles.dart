import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'app_colors.dart';

/// Typography system that mirrors the web app's text styles
/// Uses Lexend for French/English and <PERSON><PERSON><PERSON> for Arabic text
class AppTextStyles {
  // Font Families
  static String get lexendFontFamily => GoogleFonts.lexend().fontFamily!;
  static String get tajawalFontFamily => GoogleFonts.tajawal().fontFamily!;

  // Base Text Style (using Lexend as default)
  static TextStyle get _baseStyle => GoogleFonts.lexend(
        color: AppColors.grey900,
        height: 1.5, // Line height matching web app
      );

  // Display Styles (Large headings) - Reduced by 2pt
  static final TextStyle displayLarge = _baseStyle.copyWith(
    fontSize: 30,
    fontWeight: FontWeight.w700,
    height: 1.2,
  );

  static final TextStyle displayMedium = _baseStyle.copyWith(
    fontSize: 26,
    fontWeight: FontWeight.w700,
    height: 1.3,
  );

  static final TextStyle displaySmall = _baseStyle.copyWith(
    fontSize: 22,
    fontWeight: FontWeight.w600,
    height: 1.3,
  );

  // Headline Styles (Section headings) - Reduced by 2pt
  static final TextStyle headlineLarge = _baseStyle.copyWith(
    fontSize: 22,
    fontWeight: FontWeight.w700,
    height: 1.3,
  );

  static final TextStyle headlineMedium = _baseStyle.copyWith(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    height: 1.4,
  );

  static final TextStyle headlineSmall = _baseStyle.copyWith(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    height: 1.4,
  );

  // Title Styles (Card titles, button labels) - Reduced by 2pt
  static final TextStyle titleLarge = _baseStyle.copyWith(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    height: 1.4,
  );

  static final TextStyle titleMedium = _baseStyle.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.4,
  );

  static final TextStyle titleSmall = _baseStyle.copyWith(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    height: 1.4,
  );

  // Body Styles (Regular content)
  static final TextStyle bodyLarge = _baseStyle.copyWith(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    height: 1.5,
  );

  static final TextStyle bodyMedium = _baseStyle.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    height: 1.5,
  );

  static final TextStyle bodySmall = _baseStyle.copyWith(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    height: 1.5,
  );

  // Label Styles (Form labels, captions)
  static final TextStyle labelLarge = _baseStyle.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.4,
  );

  static final TextStyle labelMedium = _baseStyle.copyWith(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    height: 1.4,
  );

  static final TextStyle labelSmall = _baseStyle.copyWith(
    fontSize: 11,
    fontWeight: FontWeight.w500,
    height: 1.4,
  );

  // Specialized Styles (matching web app patterns)

  // Greeting text (used in ModernHeader) - Reduced by 2pt
  static final TextStyle greeting = _baseStyle.copyWith(
    fontSize: 26,
    fontWeight: FontWeight.w700,
    color: AppColors.navy,
    height: 1.2,
  );

  // Sub-greeting text
  static final TextStyle subGreeting = _baseStyle.copyWith(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: AppColors.grey600,
    height: 1.4,
  );

  // Medicine name (bold navy, 18px)
  static final TextStyle medicineName = _baseStyle.copyWith(
    fontSize: 18,
    fontWeight: FontWeight.w700,
    color: AppColors.navy,
    height: 1.3,
  );

  // Medicine details (dosage, form)
  static final TextStyle medicineDetails = _baseStyle.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: AppColors.black,
    height: 1.3,
  );

  // Statistics numbers
  static final TextStyle statisticsNumber = _baseStyle.copyWith(
    fontSize: 24,
    fontWeight: FontWeight.w700,
    color: AppColors.navy,
    height: 1.2,
  );

  // Enhanced statistics numbers for new status cards (40-44dp)
  static final TextStyle statusCardNumber = _baseStyle.copyWith(
    fontSize: 42,
    fontWeight: FontWeight.w700,
    height: 1.1,
  );

  // Status card category title (16sp, bold)
  static final TextStyle statusCardTitle = _baseStyle.copyWith(
    fontSize: 16,
    fontWeight: FontWeight.w700,
    height: 1.3,
  );

  // Status card task count (14sp, regular)
  static final TextStyle statusCardSubtitle = _baseStyle.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    height: 1.3,
  );

  // Statistics labels
  static final TextStyle statisticsLabel = _baseStyle.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: AppColors.grey600,
    height: 1.3,
  );

  // Status badges
  static final TextStyle statusBadge = _baseStyle.copyWith(
    fontSize: 12,
    fontWeight: FontWeight.w600,
    height: 1.2,
  );

  // Button text
  static final TextStyle buttonText = _baseStyle.copyWith(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    height: 1.2,
  );

  // Form input text
  static final TextStyle formInput = _baseStyle.copyWith(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: AppColors.grey900,
    height: 1.4,
  );

  // Form hint text
  static final TextStyle formHint = _baseStyle.copyWith(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: AppColors.grey500,
    height: 1.4,
  );

  // Form label text
  static final TextStyle formLabel = _baseStyle.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: AppColors.grey700,
    height: 1.3,
  );

  // Error text
  static final TextStyle errorText = _baseStyle.copyWith(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: AppColors.error,
    height: 1.3,
  );

  // Caption text
  static final TextStyle caption = _baseStyle.copyWith(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: AppColors.grey600,
    height: 1.3,
  );

  // Navigation text
  static final TextStyle navigation = _baseStyle.copyWith(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: AppColors.grey600,
    height: 1.2,
  );

  // Navigation active text
  static final TextStyle navigationActive = _baseStyle.copyWith(
    fontSize: 12,
    fontWeight: FontWeight.w600,
    color: AppColors.teal,
    height: 1.2,
  );

  /// Utility method to create custom text style
  static TextStyle custom({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    double? letterSpacing,
    TextDecoration? decoration,
  }) {
    return _baseStyle.copyWith(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      height: height,
      letterSpacing: letterSpacing,
      decoration: decoration,
    );
  }
}

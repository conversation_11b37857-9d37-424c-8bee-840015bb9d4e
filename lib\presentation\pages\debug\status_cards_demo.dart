import 'package:flutter/material.dart';
import '../../widgets/dashboard/enhanced_status_card.dart';
import '../../../core/theme/app_colors.dart';

/// Demo page to showcase the enhanced status cards implementation
/// This page can be used for visual testing and validation
class StatusCardsDemoPage extends StatelessWidget {
  const StatusCardsDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Enhanced Status Cards Demo'),
        backgroundColor: AppColors.navy,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.background,
              Colors.white,
            ],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Enhanced Status Cards',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppColors.navy,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Redesigned dashboard statistics cards with 1:1 aspect ratio, enhanced typography, and improved visual hierarchy.',
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.grey600,
                ),
              ),
              const SizedBox(height: 32),

              // Main grid of status cards
              GridView.count(
                crossAxisCount: 2,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.0,
                children: [
                  EnhancedStatusCardFactory.total(
                    value: '24',
                    subtitle: 'médicaments',
                    onTap: () => _showCardTapped(context, 'Total'),
                  ),
                  EnhancedStatusCardFactory.expired(
                    value: '3',
                    subtitle: 'expirés',
                    onTap: () => _showCardTapped(context, 'Expired'),
                  ),
                  EnhancedStatusCardFactory.lowStock(
                    value: '5',
                    subtitle: 'stock faible',
                    onTap: () => _showCardTapped(context, 'Low Stock'),
                  ),
                  EnhancedStatusCardFactory.expiringSoon(
                    value: '7',
                    subtitle: 'expirent bientôt',
                    onTap: () => _showCardTapped(context, 'Expiring Soon'),
                  ),
                ],
              ),

              const SizedBox(height: 40),

              // Custom examples section
              const Text(
                'Custom Examples',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.navy,
                ),
              ),
              const SizedBox(height: 16),

              // Single row with custom cards
              Row(
                children: [
                  Expanded(
                    child: EnhancedStatusCard(
                      title: 'Locations',
                      value: '8',
                      subtitle: 'emplacements',
                      primaryColor: AppColors.purple,
                      backgroundColor: AppColors.purple.withValues(alpha: 0.1),
                      backgroundGradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppColors.purple.withValues(alpha: 0.15),
                          AppColors.purple.withValues(alpha: 0.05),
                        ],
                      ),
                      icon: Icons.location_on,
                      backgroundIcon: Icons.location_city,
                      onTap: () => _showCardTapped(context, 'Locations'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: EnhancedStatusCard(
                      title: 'Famille',
                      value: '4',
                      subtitle: 'membres',
                      primaryColor: AppColors.blue,
                      backgroundColor: AppColors.blue.withValues(alpha: 0.1),
                      backgroundGradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppColors.blue.withValues(alpha: 0.15),
                          AppColors.blue.withValues(alpha: 0.05),
                        ],
                      ),
                      icon: Icons.family_restroom,
                      backgroundIcon: Icons.people,
                      onTap: () => _showCardTapped(context, 'Family'),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 40),

              // Design specifications
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.grey50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.border),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Design Specifications',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.navy,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildSpecItem('Aspect Ratio', '1:1 (Perfect Square)'),
                    _buildSpecItem('Border Radius', '20dp'),
                    _buildSpecItem('Main Number', '42dp, Bold, Top-left'),
                    _buildSpecItem('Category Title', '16sp, Bold'),
                    _buildSpecItem('Subtitle', '14sp, Regular'),
                    _buildSpecItem('Arrow Chip', '32x32, Bottom-right'),
                    _buildSpecItem('Background Icon', '56x56, 10% opacity'),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSpecItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.grey700,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.grey900,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showCardTapped(BuildContext context, String cardType) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$cardType card tapped!'),
        duration: const Duration(seconds: 2),
        backgroundColor: AppColors.teal,
      ),
    );
  }
}

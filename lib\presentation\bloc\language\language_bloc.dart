import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/services/language_service.dart';
import 'language_event.dart';
import 'language_state.dart';

/// BLoC for managing app language
class LanguageBloc extends Bloc<LanguageEvent, LanguageState> {
  final LanguageService _languageService;

  LanguageBloc({required LanguageService languageService})
      : _languageService = languageService,
        super(const LanguageInitial()) {
    on<LanguageLoadRequested>(_onLoadRequested);
    on<LanguageChangeRequested>(_onChangeRequested);
    on<LocaleChangeRequested>(_onLocaleChangeRequested);
  }

  /// Handle language load request
  Future<void> _onLoadRequested(
    LanguageLoadRequested event,
    Emitter<LanguageState> emit,
  ) async {
    emit(const LanguageLoading());

    try {
      final languageCode = await _languageService.getCurrentLanguage();
      final locale = await _languageService.getCurrentLocale();

      emit(LanguageLoaded(
        languageCode: languageCode,
        locale: locale,
      ));
    } catch (e) {
      emit(LanguageError(message: 'Failed to load language: $e'));
    }
  }

  /// Handle language change request
  Future<void> _onChangeRequested(
    LanguageChangeRequested event,
    Emitter<LanguageState> emit,
  ) async {
    if (kDebugMode) {
      print('🌐 LanguageBloc: Changing language to ${event.languageCode}');
    }

    emit(const LanguageLoading());

    try {
      await _languageService.setLanguage(event.languageCode);
      final locale = await _languageService.getCurrentLocale();

      if (kDebugMode) {
        print(
            '🌐 LanguageBloc: Language changed successfully to ${event.languageCode}, locale: $locale');
      }

      emit(LanguageLoaded(
        languageCode: event.languageCode,
        locale: locale,
      ));
    } catch (e) {
      if (kDebugMode) {
        print('🌐 LanguageBloc: Error changing language: $e');
      }
      emit(LanguageError(message: 'Failed to change language: $e'));
    }
  }

  /// Handle locale change request
  Future<void> _onLocaleChangeRequested(
    LocaleChangeRequested event,
    Emitter<LanguageState> emit,
  ) async {
    emit(const LanguageLoading());

    try {
      await _languageService.setLocale(event.locale);

      emit(LanguageLoaded(
        languageCode: event.locale.languageCode,
        locale: event.locale,
      ));
    } catch (e) {
      emit(LanguageError(message: 'Failed to change locale: $e'));
    }
  }
}

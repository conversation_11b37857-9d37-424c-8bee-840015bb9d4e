import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

/// Enhanced settings tile with animations and additional features
class EnhancedSettingsTile extends StatefulWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final VoidCallback? onTap;
  final Widget? trailing;
  final Color? iconColor;
  final bool enabled;
  final bool isLoading;
  final String? badge;
  final Color? badgeColor;
  final bool showChevron;
  final EdgeInsets? padding;
  final double? height;

  const EnhancedSettingsTile({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.onTap,
    this.trailing,
    this.iconColor,
    this.enabled = true,
    this.isLoading = false,
    this.badge,
    this.badgeColor,
    this.showChevron = true,
    this.padding,
    this.height,
  });

  @override
  State<EnhancedSettingsTile> createState() => _EnhancedSettingsTileState();
}

class _EnhancedSettingsTileState extends State<EnhancedSettingsTile>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _colorAnimation = ColorTween(
      begin: Colors.white,
      end: AppColors.grey50,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            height: widget.height ?? 72,
            margin: const EdgeInsets.symmetric(vertical: 4),
            decoration: BoxDecoration(
              color: widget.enabled ? _colorAnimation.value : AppColors.grey50,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.grey200,
                width: 1,
              ),
              boxShadow: widget.enabled
                  ? [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: widget.enabled && !widget.isLoading
                    ? () {
                        _animationController.forward().then((_) {
                          _animationController.reverse();
                        });
                        widget.onTap?.call();
                      }
                    : null,
                child: Padding(
                  padding: widget.padding ??
                      const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                  child: Row(
                    children: [
                      // Icon with badge
                      Stack(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: (widget.iconColor ?? AppColors.teal)
                                  .withValues(alpha: widget.enabled ? 0.1 : 0.05),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              widget.icon,
                              color: widget.enabled
                                  ? (widget.iconColor ?? AppColors.teal)
                                  : AppColors.grey400,
                              size: 20,
                            ),
                          ),
                          if (widget.badge != null)
                            Positioned(
                              top: -2,
                              right: -2,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: widget.badgeColor ?? AppColors.error,
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  widget.badge!,
                                  style: AppTextStyles.bodySmall.copyWith(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),

                      const SizedBox(width: 16),

                      // Title and subtitle
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              widget.title,
                              style: AppTextStyles.titleSmall.copyWith(
                                color: widget.enabled
                                    ? AppColors.black
                                    : AppColors.grey400,
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (widget.subtitle != null) ...[
                              const SizedBox(height: 2),
                              Text(
                                widget.subtitle!,
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: widget.enabled
                                      ? AppColors.grey600
                                      : AppColors.grey400,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ],
                        ),
                      ),

                      const SizedBox(width: 16),

                      // Trailing widget
                      if (widget.isLoading)
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: AppColors.teal,
                          ),
                        )
                      else if (widget.trailing != null)
                        widget.trailing!
                      else if (widget.showChevron && widget.onTap != null)
                        Icon(
                          Icons.chevron_right,
                          color: widget.enabled
                              ? AppColors.grey400
                              : AppColors.grey300,
                          size: 20,
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Settings tile with progress indicator
class ProgressSettingsTile extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final double progress; // 0.0 to 1.0
  final String? progressText;
  final VoidCallback? onTap;
  final Color? iconColor;
  final Color? progressColor;
  final bool enabled;

  const ProgressSettingsTile({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    required this.progress,
    this.progressText,
    this.onTap,
    this.iconColor,
    this.progressColor,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 72,
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: enabled ? Colors.white : AppColors.grey50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.grey200,
          width: 1,
        ),
        boxShadow: enabled
            ? [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: enabled ? onTap : null,
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            child: Row(
              children: [
                // Icon
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: (iconColor ?? AppColors.teal)
                        .withValues(alpha: enabled ? 0.1 : 0.05),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: enabled
                        ? (iconColor ?? AppColors.teal)
                        : AppColors.grey400,
                    size: 20,
                  ),
                ),

                const SizedBox(width: 16),

                // Title, subtitle, and progress
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              title,
                              style: AppTextStyles.titleSmall.copyWith(
                                color: enabled
                                    ? AppColors.black
                                    : AppColors.grey400,
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (progressText != null)
                            Text(
                              progressText!,
                              style: AppTextStyles.bodySmall.copyWith(
                                color: progressColor ?? AppColors.teal,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      if (subtitle != null) ...[
                        Text(
                          subtitle!,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: enabled
                                ? AppColors.grey600
                                : AppColors.grey400,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                      ],
                      // Progress bar
                      ClipRRect(
                        borderRadius: BorderRadius.circular(2),
                        child: LinearProgressIndicator(
                          value: progress,
                          backgroundColor: AppColors.grey200,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            progressColor ?? AppColors.teal,
                          ),
                          minHeight: 4,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // Chevron
                if (onTap != null)
                  Icon(
                    Icons.chevron_right,
                    color: enabled ? AppColors.grey400 : AppColors.grey300,
                    size: 20,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

import '../entities/tunisia_medicine.dart';

/// Repository interface for tunisia_medicines reference table
abstract class TunisiaMedicineRepository {
  /// Search for medicines in the tunisia_medicines table
  /// 
  /// [query] - Search query string
  /// [limit] - Maximum number of results to return
  /// 
  /// Returns a Future with a list of matching TunisiaMedicine entities
  Future<List<TunisiaMedicine>> searchMedicines(String query, {int limit = 10});
  
  /// Get a medicine by ID
  /// 
  /// [id] - Medicine ID
  /// 
  /// Returns a Future with the TunisiaMedicine entity if found
  Future<TunisiaMedicine?> getMedicineById(String id);
}

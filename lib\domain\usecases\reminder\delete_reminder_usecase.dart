import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../repositories/reminder_repository.dart';

@injectable
class DeleteReminderUseCase implements UseCase<Unit, DeleteReminderParams> {
  final ReminderRepository repository;

  DeleteReminderUseCase(this.repository);

  @override
  Future<Either<Failure, Unit>> call(DeleteReminderParams params) async {
    return await repository.deleteReminder(params.reminderId);
  }
}

class DeleteReminderParams {
  final String reminderId;

  DeleteReminderParams({required this.reminderId});
}

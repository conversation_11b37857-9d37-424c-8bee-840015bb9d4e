import 'package:equatable/equatable.dart';

import '../../../domain/entities/location.dart';

/// Base class for location events
abstract class LocationEvent extends Equatable {
  const LocationEvent();

  @override
  List<Object?> get props => [];
}

/// Initialize locations for a household
class LocationsInitialized extends LocationEvent {
  final String householdId;

  const LocationsInitialized({required this.householdId});

  @override
  List<Object?> get props => [householdId];
}

/// Load locations from repository
class LocationsLoaded extends LocationEvent {
  const LocationsLoaded();
}

/// Refresh locations
class LocationsRefreshed extends LocationEvent {
  const LocationsRefreshed();
}

/// Create a new location
class LocationCreated extends LocationEvent {
  final Location location;

  const LocationCreated({required this.location});

  @override
  List<Object?> get props => [location];
}

/// Update an existing location
class LocationUpdated extends LocationEvent {
  final Location location;

  const LocationUpdated({required this.location});

  @override
  List<Object?> get props => [location];
}

/// Delete a location
class LocationDeleted extends LocationEvent {
  final String locationId;

  const LocationDeleted({required this.locationId});

  @override
  List<Object?> get props => [locationId];
}

/// Search locations
class LocationsSearched extends LocationEvent {
  final String query;

  const LocationsSearched({required this.query});

  @override
  List<Object?> get props => [query];
}

/// Clear search
class LocationSearchCleared extends LocationEvent {
  const LocationSearchCleared();
}

/// Initialize default locations for household
class DefaultLocationsInitialized extends LocationEvent {
  final String householdId;

  const DefaultLocationsInitialized({required this.householdId});

  @override
  List<Object?> get props => [householdId];
}

/// Select a location for editing
class LocationSelected extends LocationEvent {
  final Location? location;

  const LocationSelected({this.location});

  @override
  List<Object?> get props => [location];
}

/// Clear selected location
class LocationSelectionCleared extends LocationEvent {
  const LocationSelectionCleared();
}

/// Validate location form
class LocationFormValidated extends LocationEvent {
  final String name;
  final String? description;
  final String icon;

  const LocationFormValidated({
    required this.name,
    this.description,
    required this.icon,
  });

  @override
  List<Object?> get props => [name, description, icon];
}

/// Reset location form
class LocationFormReset extends LocationEvent {
  const LocationFormReset();
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../presentation/bloc/auth/auth_bloc.dart';
import '../../presentation/bloc/auth/auth_state.dart' as auth_state;

/// Utility class for Supabase operations and validation
class SupabaseUtils {
  /// Validates if a string is a valid UUID format
  static bool isValidUUID(String? value) {
    if (value == null || value.isEmpty) return false;

    // UUID v4 regex pattern
    final uuidRegex = RegExp(
        r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$');

    return uuidRegex.hasMatch(value);
  }

  /// Safely retrieves household ID from authentication state
  /// Returns null if user is not authenticated or household ID is invalid
  static String? getHouseholdId(BuildContext context) {
    try {
      final authState = context.read<AuthBloc>().state;

      if (authState is auth_state.AuthAuthenticated) {
        final householdId = authState.householdId;

        // Validate that household ID is a proper UUID
        if (isValidUUID(householdId)) {
          return householdId;
        } else {
          debugPrint('Invalid household ID format: $householdId');
          return null;
        }
      }

      debugPrint('User not authenticated, cannot retrieve household ID');
      return null;
    } catch (e) {
      debugPrint('Error retrieving household ID: $e');
      return null;
    }
  }

  /// Safely retrieves user ID from authentication state
  /// Returns null if user is not authenticated or user ID is invalid
  static String? getUserId(BuildContext context) {
    try {
      final authState = context.read<AuthBloc>().state;

      if (authState is auth_state.AuthAuthenticated) {
        final userId = authState.user.id;

        // Validate that user ID is a proper UUID
        if (isValidUUID(userId)) {
          return userId;
        } else {
          debugPrint('Invalid user ID format: $userId');
          return null;
        }
      }

      debugPrint('User not authenticated, cannot retrieve user ID');
      return null;
    } catch (e) {
      debugPrint('Error retrieving user ID: $e');
      return null;
    }
  }

  /// Validates required parameters before making Supabase queries
  static bool validateRequiredParams(Map<String, dynamic> params) {
    for (final entry in params.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value == null || (value is String && value.isEmpty)) {
        debugPrint('Required parameter $key is null or empty');
        return false;
      }

      // Special validation for UUID fields
      if (key.endsWith('_id') && value is String) {
        if (!isValidUUID(value)) {
          debugPrint('Invalid UUID format for $key: $value');
          return false;
        }
      }
    }

    return true;
  }

  /// Formats Supabase query filters correctly
  static Map<String, dynamic> formatQueryFilters({
    required String householdId,
    Map<String, dynamic>? additionalFilters,
  }) {
    final filters = <String, dynamic>{
      'household_id': householdId,
    };

    if (additionalFilters != null) {
      filters.addAll(additionalFilters);
    }

    return filters;
  }

  /// Standard error messages for common Supabase errors
  static String getErrorMessage(dynamic error) {
    if (error == null) return 'Une erreur inconnue s\'est produite';

    final errorString = error.toString().toLowerCase();

    if (errorString.contains('400')) {
      return 'Paramètres de requête invalides';
    } else if (errorString.contains('401')) {
      return 'Non autorisé - veuillez vous reconnecter';
    } else if (errorString.contains('403')) {
      return 'Accès refusé';
    } else if (errorString.contains('404')) {
      return 'Ressource non trouvée';
    } else if (errorString.contains('network')) {
      return 'Erreur de connexion réseau';
    } else {
      return 'Erreur de base de données: ${error.toString()}';
    }
  }
}

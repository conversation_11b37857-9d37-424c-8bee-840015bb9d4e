import 'package:injectable/injectable.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../core/utils/supabase_utils.dart';
import '../../core/services/supabase_service.dart';
import '../../domain/entities/family_member.dart';
import '../../domain/repositories/family_member_repository.dart';
import '../models/family_member_model.dart';

@Injectable(as: FamilyMemberRepository)
class FamilyMemberRepositoryImpl implements FamilyMemberRepository {
  final SupabaseClient _supabaseClient;
  final SupabaseService _supabaseService;

  FamilyMemberRepositoryImpl(this._supabaseClient, this._supabaseService);

  @override
  Stream<List<FamilyMember>> getHouseholdMembers(String householdId) {
    // Validate household ID before creating stream
    if (!SupabaseUtils.isValidUUID(householdId)) {
      return Stream.error(
          Exception('Invalid household ID format: $householdId'));
    }

    return _supabaseService.executeStreamWithRetry(
      () => _supabaseClient
          .from('family_members')
          .stream(primaryKey: ['id'])
          .eq('household_id', householdId)
          .order('name')
          .map((data) => data
              .map((json) => FamilyMemberModel.fromJson(json))
              .map((model) => model.toEntity())
              .toList()),
      operationName: 'getHouseholdMembers',
    );
  }

  @override
  Future<FamilyMember?> getFamilyMemberById(String memberId) async {
    try {
      // Validate member ID
      if (!SupabaseUtils.isValidUUID(memberId)) {
        throw Exception('Invalid member ID format: $memberId');
      }

      final response = await _supabaseClient
          .from('family_members')
          .select(
              'id, household_id, name, avatar_url, birth_date, relation, created_at') // Select actual database fields (prioritize relation over role)
          .eq('id', memberId)
          .maybeSingle();

      if (response == null) {
        return null;
      }

      final model = FamilyMemberModel.fromJson(response);
      return model.toEntity();
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }

  @override
  Future<FamilyMember> createFamilyMember(FamilyMember member) async {
    return await _supabaseService.executeWithRetry(
      () async {
        // Validate required parameters
        final requiredParams = {
          'household_id': member.householdId,
          'name': member.name,
        };

        if (!SupabaseUtils.validateRequiredParams(requiredParams)) {
          throw Exception(
              'Missing required parameters for family member creation');
        }

        final model = FamilyMemberModel.fromEntity(member);
        final response = await _supabaseClient
            .from('family_members')
            .insert(model.toJson())
            .select(
                'id, household_id, name, avatar_url, birth_date, relation, created_at') // Select actual database fields (prioritize relation over role)
            .single();

        final createdModel = FamilyMemberModel.fromJson(response);
        return createdModel.toEntity();
      },
      operationName: 'createFamilyMember',
    );
  }

  @override
  Future<FamilyMember> updateFamilyMember(FamilyMember member) async {
    try {
      // Validate required parameters
      final requiredParams = {
        'id': member.id,
        'household_id': member.householdId,
        'name': member.name,
      };

      if (!SupabaseUtils.validateRequiredParams(requiredParams)) {
        throw Exception('Missing required parameters for family member update');
      }

      final model = FamilyMemberModel.fromEntity(member);
      final response = await _supabaseClient
          .from('family_members')
          .update(model.toJson())
          .eq('id', member.id)
          .select()
          .single();

      final updatedModel = FamilyMemberModel.fromJson(response);
      return updatedModel.toEntity();
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }

  @override
  Future<void> deleteFamilyMember(String memberId) async {
    try {
      // Validate member ID
      if (!SupabaseUtils.isValidUUID(memberId)) {
        throw Exception('Invalid member ID format: $memberId');
      }

      await _supabaseClient.from('family_members').delete().eq('id', memberId);
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }
}

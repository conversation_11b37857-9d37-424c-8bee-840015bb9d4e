import 'package:equatable/equatable.dart';

/// Tunisia medicine entity for database search functionality
/// Mirrors the tunisia_medicines table from the web app
class TunisiaMedicine extends Equatable {
  final String id;
  final String nom;
  final String? laboratoire;
  final String? dci;
  final String? dosage;
  final String? forme;
  final String? presentation;
  final String? classe;
  final String? sousClasse;
  final String? amm;
  final DateTime? dateAmm;
  final String? conditionnementPrimaire;
  final String? specificationConditionnementPrimaire;
  final String? tableau;
  final String? dureeDeConservation;
  final String? indications;
  final String? gPB;
  final String? veic;
  final String? codeATC;
  final String? prix;
  final String? remboursement;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const TunisiaMedicine({
    required this.id,
    required this.nom,
    this.laboratoire,
    this.dci,
    this.dosage,
    this.forme,
    this.presentation,
    this.classe,
    this.sousClasse,
    this.amm,
    this.dateAmm,
    this.conditionnementPrimaire,
    this.specificationConditionnementPrimaire,
    this.tableau,
    this.dureeDeConservation,
    this.indications,
    this.gPB,
    this.veic,
    this.codeATC,
    this.prix,
    this.remboursement,
    this.createdAt,
    this.updatedAt,
  });

  /// Get display name for the medicine
  String get displayName => nom;

  /// Get full description with dosage and form
  String get fullDescription {
    final parts = <String>[nom];
    if (dosage != null && dosage!.isNotEmpty) {
      parts.add(dosage!);
    }
    if (forme != null && forme!.isNotEmpty) {
      parts.add('($forme)');
    }
    return parts.join(' ');
  }

  /// Get laboratory information
  String get laboratoryInfo => laboratoire ?? 'Laboratoire non spécifié';

  /// Get therapeutic class information
  String get therapeuticClass {
    if (classe != null && classe!.isNotEmpty) {
      if (sousClasse != null && sousClasse!.isNotEmpty) {
        return '$classe - $sousClasse';
      }
      return classe!;
    }
    return 'Classe non spécifiée';
  }

  /// Check if medicine has complete information
  bool get hasCompleteInfo {
    return nom.isNotEmpty &&
        laboratoire != null &&
        dci != null &&
        dosage != null &&
        forme != null;
  }

  /// Copy with method for immutable updates
  TunisiaMedicine copyWith({
    String? id,
    String? nom,
    String? laboratoire,
    String? dci,
    String? dosage,
    String? forme,
    String? presentation,
    String? classe,
    String? sousClasse,
    String? amm,
    DateTime? dateAmm,
    String? conditionnementPrimaire,
    String? specificationConditionnementPrimaire,
    String? tableau,
    String? dureeDeConservation,
    String? indications,
    String? gPB,
    String? veic,
    String? codeATC,
    String? prix,
    String? remboursement,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TunisiaMedicine(
      id: id ?? this.id,
      nom: nom ?? this.nom,
      laboratoire: laboratoire ?? this.laboratoire,
      dci: dci ?? this.dci,
      dosage: dosage ?? this.dosage,
      forme: forme ?? this.forme,
      presentation: presentation ?? this.presentation,
      classe: classe ?? this.classe,
      sousClasse: sousClasse ?? this.sousClasse,
      amm: amm ?? this.amm,
      dateAmm: dateAmm ?? this.dateAmm,
      conditionnementPrimaire:
          conditionnementPrimaire ?? this.conditionnementPrimaire,
      specificationConditionnementPrimaire:
          specificationConditionnementPrimaire ??
              this.specificationConditionnementPrimaire,
      tableau: tableau ?? this.tableau,
      dureeDeConservation: dureeDeConservation ?? this.dureeDeConservation,
      indications: indications ?? this.indications,
      gPB: gPB ?? this.gPB,
      veic: veic ?? this.veic,
      codeATC: codeATC ?? this.codeATC,
      prix: prix ?? this.prix,
      remboursement: remboursement ?? this.remboursement,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        nom,
        laboratoire,
        dci,
        dosage,
        forme,
        presentation,
        classe,
        sousClasse,
        amm,
        dateAmm,
        conditionnementPrimaire,
        specificationConditionnementPrimaire,
        tableau,
        dureeDeConservation,
        indications,
        gPB,
        veic,
        codeATC,
        prix,
        remboursement,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'TunisiaMedicine(id: $id, nom: $nom, laboratoire: $laboratoire, '
        'dosage: $dosage, forme: $forme)';
  }
}

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// Events for language management
abstract class LanguageEvent extends Equatable {
  const LanguageEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load current language
class LanguageLoadRequested extends LanguageEvent {
  const LanguageLoadRequested();
}

/// Event to change language
class LanguageChangeRequested extends LanguageEvent {
  final String languageCode;

  const LanguageChangeRequested({required this.languageCode});

  @override
  List<Object?> get props => [languageCode];
}

/// Event to change locale
class LocaleChangeRequested extends LanguageEvent {
  final Locale locale;

  const LocaleChangeRequested({required this.locale});

  @override
  List<Object?> get props => [locale];
}

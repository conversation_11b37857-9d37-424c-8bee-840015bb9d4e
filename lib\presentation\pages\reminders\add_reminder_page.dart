import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/supabase_utils.dart';
import '../../../domain/entities/medicine.dart';
import '../../../domain/entities/reminder.dart';
import '../../../domain/entities/reminder_schedule.dart';
import '../../bloc/medicine/medicine_bloc.dart';
import '../../bloc/reminder/reminder_bloc.dart';
import '../../bloc/reminder/reminder_event.dart';
import '../../bloc/reminder/reminder_state.dart';
import '../../widgets/common/form_components.dart';

class AddReminderPage extends StatefulWidget {
  const AddReminderPage({super.key});

  @override
  State<AddReminderPage> createState() => _AddReminderPageState();
}

class _AddReminderPageState extends State<AddReminderPage> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  final int _totalSteps = 5;

  // Accumulated state
  Medicine? _selectedMedicine;

  // Track last saved reminder to prevent duplicates
  Reminder? _lastSavedReminder;

  // Frequency step selections
  ReminderScheduleFrequencyType? _chosenFrequency;

  // Step 2 details
  int _everyXDays = 1;
  int _everyXWeeks = 1;
  int _everyXMonths = 1;
  List<int> _weekdays = []; // 1..7 Mon..Sun
  int _timesPerDay = 1;
  int _everyXHours = 6;
  DateTime? _startDate;
  List<String> _times = []; // HH:mm strings

  // Step 3/4: dosage and instructions
  final _dosageAmountCtrl = TextEditingController();
  String _dosageUnit = 'comprimé';
  final List<String> _dosageUnits = const [
    'comprimé',
    'gélule',
    'goutte',
    'ml',
    'mg',
    'g',
    'cuillère à café',
    'cuillère à soupe',
    'sachet',
    'ampoule',
    'suppositoire',
    'patch',
    'inhalation',
    'injection',
  ];

  DateTime? _endDate;
  final _instructionsCtrl = TextEditingController();

  Timer? _searchDebounceTimer;

  @override
  void initState() {
    super.initState();
    _bootstrapMedicineSearch();
    _startDate = DateTime.now();
    _times = ['08:00'];
    _timesPerDay = 1;
    _chosenFrequency = ReminderScheduleFrequencyType.everyXDays;
  }

  void _bootstrapMedicineSearch() {
    final householdId = SupabaseUtils.getHouseholdId(context);
    if (householdId != null) {
      context.read<MedicineBloc>().add(MedicineLoadRequested(householdId: householdId));
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _dosageAmountCtrl.dispose();
    _instructionsCtrl.dispose();
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  void _next() {
    if (_validateStep(_currentPage)) {
      if (_currentPage < _totalSteps - 1) {
        setState(() => _currentPage += 1);
        _pageController.animateToPage(
          _currentPage,
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  void _back() {
    if (_currentPage > 0) {
      setState(() => _currentPage -= 1);
      _pageController.animateToPage(
        _currentPage,
        duration: const Duration(milliseconds: 250),
        curve: Curves.easeInOut,
      );
    } else {
      context.pop();
    }
  }

  bool _validateStep(int page) {
    switch (page) {
      case 0:
        if (_selectedMedicine == null) {
          _showSnack('Veuillez sélectionner un médicament.');
          return false;
        }
        return true;
      case 1:
        if (_chosenFrequency == null) {
          _showSnack('Veuillez choisir une fréquence.');
          return false;
        }
        return true;
      case 2:
        if (_chosenFrequency == ReminderScheduleFrequencyType.everyXDays && _everyXDays < 1) {
          _showSnack('L\'intervalle en jours doit être ≥ 1.');
          return false;
        }
        if (_chosenFrequency == ReminderScheduleFrequencyType.weekly && _weekdays.isEmpty) {
          _showSnack('Veuillez sélectionner au moins un jour de la semaine.');
          return false;
        }
        if (_chosenFrequency == ReminderScheduleFrequencyType.monthly && _everyXMonths < 1) {
          _showSnack('L\'intervalle en mois doit être ≥ 1.');
          return false;
        }
        if (_chosenFrequency == ReminderScheduleFrequencyType.everyXHours && _everyXHours < 1) {
          _showSnack('L\'intervalle en heures doit être ≥ 1.');
          return false;
        }
        final needsTimes = _chosenFrequency != ReminderScheduleFrequencyType.asNeeded &&
            _chosenFrequency != ReminderScheduleFrequencyType.everyXHours &&
            _chosenFrequency != ReminderScheduleFrequencyType.customDates;
        if (needsTimes && _times.isEmpty) {
          _showSnack('Veuillez définir au moins une heure.');
          return false;
        }
        return true;
      case 3:
        final value = _dosageAmountCtrl.text.trim();
        final regex = RegExp(r'^(\d+(\.\d+)?|\d+\/\d+)$');
        if (value.isEmpty || !regex.hasMatch(value)) {
          _showSnack('Veuillez saisir un dosage valide (ex: 1, 1/2, 2.5).');
          return false;
        }
        return true;
      case 4:
        if (_startDate != null && _endDate != null && _endDate!.isBefore(_startDate!)) {
          _showSnack('La date de fin doit être après la date de début.');
          return false;
        }
        return true;
      default:
        return true;
    }
  }

  void _showSnack(String msg) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(msg)));
  }

  void _commitAndSave() {
    final dosageAmount = _parseDosage(_dosageAmountCtrl.text.trim());
    final schedule = ReminderSchedule(
      frequencyType: _chosenFrequency!,
      intervalDays: _chosenFrequency == ReminderScheduleFrequencyType.everyXDays ? _everyXDays : null,
      intervalWeeks: _chosenFrequency == ReminderScheduleFrequencyType.weekly ? _everyXWeeks : null,
      intervalMonths: _chosenFrequency == ReminderScheduleFrequencyType.monthly ? _everyXMonths : null,
      intervalHours: _chosenFrequency == ReminderScheduleFrequencyType.everyXHours ? _everyXHours : null,
      daysOfWeek: _chosenFrequency == ReminderScheduleFrequencyType.weekly ? _weekdays : const [],
      times: _times,
      specificDates: const [],
      startDate: _startDate,
      endDate: _endDate,
      dosageAmount: dosageAmount,
      dosageUnit: _dosageUnit,
      instructions: _instructionsCtrl.text.trim().isEmpty ? null : _instructionsCtrl.text.trim(),
    );

    final errors = schedule.validate();
    if (errors.isNotEmpty) {
      _showSnack(errors.first);
      return;
    }

    final mapped = schedule.toReminderInsertion(
      userMedicineId: _selectedMedicine!.id,
      nowFallback: DateTime.now(),
      name: _selectedMedicine!.name,
    );

    final normalizedType = _normalizeFrequencyTypeForBackend(mapped['frequencyType'] as String);
    final normalizedValue = _normalizeFrequencyValueForBackend(
      mapped['frequencyType'] as String,
      mapped['frequencyValue'] as int?,
    );

    final reminder = Reminder(
      userMedicineId: mapped['userMedicineId'],
      name: mapped['name'],
      dosageAmount: dosageAmount,
      dosageUnit: _dosageUnit,
      times: List<String>.from(mapped['times'] ?? const []),
      frequencyType: normalizedType,
      frequencyValue: normalizedValue,
      frequencyDays: List<int>.from(mapped['frequencyDays'] ?? const []),
      specificDates: List<DateTime>.from(mapped['specificDates'] ?? const []),
      startDate: mapped['startDate'],
      endDate: mapped['endDate'],
      notes: mapped['notes'],
      isActive: true,
      status: ReminderStatus.active,
    );

    if (reminder == _lastSavedReminder) {
      _showSnack('Vous avez déjà enregistré ce rappel.');
      return;
    }

    context.read<ReminderBloc>().add(AddReminder(reminder));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ReminderBloc, ReminderState>(
      listener: (context, state) {
        if (state is ReminderAdded) {
          setState(() {
            _lastSavedReminder = state.reminder;
          });
          _showSnack('Rappel enregistré avec succès !');
          // Navigate to the reminders page after a successful save
          context.go('/reminders');
        } else if (state is ReminderError) {
          _showSnack('Erreur: ${state.message}');
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.teal,
        body: Column(
          children: [
            Container(
              padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: _back,
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.arrow_back,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      'Nouveau rappel',
                      style: AppTextStyles.headlineMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Text(
                    '${_currentPage + 1}/$_totalSteps',
                    style: AppTextStyles.labelLarge.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, -4),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    const SizedBox(height: 24),
                    _buildProgressIndicator(),
                    const SizedBox(height: 32),
                    Expanded(
                      child: PageView(
                        controller: _pageController,
                        physics: const NeverScrollableScrollPhysics(),
                        children: [
                          _buildStep0Medicine(),
                          _buildStep1Frequency(),
                          _buildStep2FrequencyDetail(),
                          _buildStep3Dosage(),
                          _buildStep4DurationAndInstructions(),
                        ],
                      ),
                    ),
                    _buildNavigationButtons(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Row(
      children: List.generate(_totalSteps, (index) {
        return Expanded(
          child: Container(
            height: 4,
            margin: EdgeInsets.only(right: index < _totalSteps - 1 ? 8 : 0),
            decoration: BoxDecoration(
              color: index <= _currentPage ? AppColors.teal : AppColors.grey300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildNavigationButtons() {
    final isLast = _currentPage == _totalSteps - 1;
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          if (_currentPage > 0)
            Expanded(
              child: ModernButton(
                text: 'Précédent',
                isOutlined: true,
                onPressed: _back,
              ),
            ),
          if (_currentPage > 0) const SizedBox(width: 16),
          Expanded(
            child: ModernButton(
              text: isLast ? 'Enregistrer' : 'Suivant',
              onPressed: isLast ? _commitAndSave : _next,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStep0Medicine() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sélection du médicament',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.navy,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Recherchez et sélectionnez votre médicament.',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.grey600,
            ),
          ),
          const SizedBox(height: 20),
          _buildMedicineSearchField(),
        ],
      ),
    );
  }

  Widget _buildMedicineSearchField() {
    return BlocBuilder<MedicineBloc, MedicineState>(
      builder: (context, state) {
        List<Medicine> medicines = [];
        bool isLoading = false;

        if (state is MedicineLoaded) {
          medicines = state.medicines;
        } else if (state is MedicineSearchResults) {
          medicines = state.searchResults;
        } else if (state is MedicineLoading) {
          isLoading = true;
        }

        return Autocomplete<Medicine>(
          displayStringForOption: (Medicine medicine) => medicine.name,
          optionsBuilder: (TextEditingValue textEditingValue) {
            if (textEditingValue.text.isEmpty) {
              return const Iterable<Medicine>.empty();
            }

            final query = textEditingValue.text.toLowerCase();
            return medicines.where((medicine) {
              return medicine.displayName.toLowerCase().contains(query) ||
                  medicine.name.toLowerCase().contains(query) ||
                  (medicine.medicineName?.toLowerCase().contains(query) ?? false) ||
                  (medicine.dci?.toLowerCase().contains(query) ?? false) ||
                  (medicine.laboratoire?.toLowerCase().contains(query) ?? false);
            });
          },
          onSelected: (Medicine medicine) {
            setState(() {
              _selectedMedicine = medicine;
            });
          },
          fieldViewBuilder: (context, textEditingController, focusNode, onFieldSubmitted) {
            if (_selectedMedicine != null && textEditingController.text.isEmpty) {
              textEditingController.text = _selectedMedicine!.displayName;
            }

            return TextFormField(
              controller: textEditingController,
              focusNode: focusNode,
              decoration: InputDecoration(
                labelText: 'Médicament',
                hintText: 'Rechercher un médicament...', 
                prefixIcon: const Icon(Icons.search, color: AppColors.teal),
                suffixIcon: isLoading
                    ? const Padding(
                        padding: EdgeInsets.all(12.0),
                        child: SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(AppColors.teal),
                          ),
                        ),
                      )
                    : textEditingController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              textEditingController.clear();
                              setState(() {
                                _selectedMedicine = null;
                              });
                            },
                          )
                        : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: AppColors.grey300),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: AppColors.grey300),
                ), 
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: AppColors.teal, width: 2),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              onChanged: (value) {
                _searchDebounceTimer?.cancel();
                if (value.isNotEmpty && value.length >= 2) {
                  _searchDebounceTimer = Timer(const Duration(milliseconds: 400), () {
                    final householdId = SupabaseUtils.getHouseholdId(context);
                    if (householdId != null) {
                      context.read<MedicineBloc>().add(
                            MedicineSearchRequested(householdId: householdId, query: value),
                          );
                    }
                  });
                } else if (value.isEmpty) {
                  final householdId = SupabaseUtils.getHouseholdId(context);
                  if (householdId != null) {
                    context.read<MedicineBloc>().add(MedicineLoadRequested(householdId: householdId));
                  }
                }
              },
            );
          },
          optionsViewBuilder: (context, onSelected, options) {
            return Align(
              alignment: Alignment.topLeft,
              child: Material(
                elevation: 4,
                borderRadius: BorderRadius.circular(12),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxHeight: 220),
                  child: ListView.builder(
                    padding: const EdgeInsets.all(8),
                    itemCount: options.length,
                    itemBuilder: (context, index) {
                      final medicine = options.elementAt(index);
                      return ListTile(
                        leading: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: AppColors.teal.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: const Icon(Icons.medication, color: AppColors.teal, size: 20),
                        ),
                        title: Text(
                          medicine.name,
                          style: AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.w500),
                        ),
                        subtitle: medicine.form != null
                            ? Text(
                                medicine.form!,
                                style: AppTextStyles.bodySmall.copyWith(color: AppColors.grey600),
                              )
                            : null,
                        onTap: () => onSelected(medicine),
                      );
                    },
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildStep1Frequency() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Fréquence',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.navy,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Choisissez la fréquence de prise de votre médicament.',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.grey600,
            ),
          ),
          const SizedBox(height: 20),
          _freqOptionTile(
            title: 'Tous les X jours',
            type: ReminderScheduleFrequencyType.everyXDays,
            icon: Icons.repeat,
          ),
          _freqOptionTile(
            title: 'Toutes les X semaines (jours spécifiques)',
            type: ReminderScheduleFrequencyType.weekly,
            icon: Icons.date_range,
          ),
          _freqOptionTile(
            title: 'Tous les X mois',
            type: ReminderScheduleFrequencyType.monthly,
            icon: Icons.calendar_today,
          ),
          _freqOptionTile(
            title: 'Si nécessaire',
            type: ReminderScheduleFrequencyType.asNeeded,
            icon: Icons.emergency_share,
          ),
          _freqOptionTile(
            title: 'Toutes les X heures',
            type: ReminderScheduleFrequencyType.everyXHours,
            icon: Icons.access_time,
          ),
        ],
      ),
    );
  }

  Widget _freqOptionTile({
    required String title,
    required ReminderScheduleFrequencyType type,
    required IconData icon,
  }) {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: RadioListTile<ReminderScheduleFrequencyType>(
        value: type,
        groupValue: _chosenFrequency,
        onChanged: (v) => setState(() => _chosenFrequency = v),
        title: Text(title, style: AppTextStyles.titleSmall),
        secondary: Icon(icon, color: AppColors.teal),
        activeColor: AppColors.teal,
      ),
    );
  }

  Widget _buildStep2FrequencyDetail() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Détails de la fréquence',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.navy,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Configurez les détails de la fréquence sélectionnée.',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.grey600,
            ),
          ),
          const SizedBox(height: 20),
          if (_chosenFrequency == ReminderScheduleFrequencyType.everyXDays) ...[
            _numberTile(
              label: 'Tous les X jours',
              value: _everyXDays,
              onChanged: (v) => setState(() => _everyXDays = v),
              min: 1,
              max: 90,
            ),
            const SizedBox(height: 12),
            _timeEditors(),
          ] else if (_chosenFrequency == ReminderScheduleFrequencyType.weekly) ...[
            _numberTile(
              label: 'Toutes les X semaines',
              value: _everyXWeeks,
              onChanged: (v) => setState(() => _everyXWeeks = v),
              min: 1,
              max: 12,
            ),
            const SizedBox(height: 12),
            _weekdaySelector(),
            const SizedBox(height: 12),
            _timeEditors(),
          ] else if (_chosenFrequency == ReminderScheduleFrequencyType.monthly) ...[
            _numberTile(
              label: 'Tous les X mois',
              value: _everyXMonths,
              onChanged: (v) => setState(() => _everyXMonths = v),
              min: 1,
              max: 24,
            ),
            const SizedBox(height: 12),
            _timeEditors(),
          ] else if (_chosenFrequency == ReminderScheduleFrequencyType.everyXHours) ...[
            _numberTile(
              label: 'Toutes les X heures',
              value: _everyXHours,
              onChanged: (v) => setState(() => _everyXHours = v),
              min: 1,
              max: 24,
            ),
            const SizedBox(height: 12),
            _datePickerTile(
              label: 'Date de début',
              date: _startDate,
              onPick: (d) => setState(() => _startDate = d),
            ),
          ] else if (_chosenFrequency == ReminderScheduleFrequencyType.asNeeded) ...[
            _datePickerTile(
              label: 'Date de début',
              date: _startDate,
              onPick: (d) => setState(() => _startDate = d),
            ),
          ],
        ],
      ),
    );
  }

  Widget _timeEditors() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Heures de prise', style: AppTextStyles.titleSmall),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            ..._times.asMap().entries.map((e) => _timeChip(e.key, e.value)),
            ActionChip(
              avatar: const Icon(Icons.add, size: 16),
              label: const Text('Ajouter une heure'),
              onPressed: () async {
                final t = await _pickTime(context, initial: _times.isNotEmpty ? _times.last : '08:00');
                if (t != null) {
                  setState(() => _times = [..._times, t]);
                }
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _timeChip(int index, String time) {
    return InputChip(
      label: Text(time),
      onPressed: () async {
        final t = await _pickTime(context, initial: time);
        if (t != null) {
          setState(() {
            _times[index] = t;
          });
        }
      },
      onDeleted: () {
        setState(() {
          _times.removeAt(index);
        });
      },
      deleteIcon: const Icon(Icons.close, size: 16),
    );
  }

  Future<String?> _pickTime(BuildContext context, {required String initial}) async {
    final pieces = initial.split(':');
    final h = int.tryParse(pieces[0]) ?? 8;
    final m = int.tryParse(pieces[1]) ?? 0;
    final t = await showTimePicker(context: context, initialTime: TimeOfDay(hour: h, minute: m));
    if (t == null) return null;
    return '${t.hour.toString().padLeft(2, '0')}:${t.minute.toString().padLeft(2, '0')}';
  }

  Widget _weekdaySelector() {
    final names = const ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'];
    return Wrap(
      spacing: 8,
      children: List.generate(7, (i) {
        final day = i + 1;
        final selected = _weekdays.contains(day);
        return FilterChip(
          label: Text(names[i]),
          selected: selected,
          onSelected: (s) {
            setState(() {
              if (s) {
                _weekdays = [..._weekdays, day];
              } else {
                _weekdays = _weekdays.where((d) => d != day).toList();
              }
            });
          },
          selectedColor: AppColors.teal.withOpacity(0.2),
          checkmarkColor: AppColors.teal,
        );
      }),
    );
  }

  Widget _numberTile({
    required String label,
    required int value,
    required ValueChanged<int> onChanged,
    int min = 1,
    int max = 100,
  }) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      title: Text(label, style: AppTextStyles.titleSmall),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            onPressed: value > min ? () => onChanged(value - 1) : null,
            icon: const Icon(Icons.remove_circle_outline),
          ),
          Text('$value', style: AppTextStyles.titleSmall),
          IconButton(
            onPressed: value < max ? () => onChanged(value + 1) : null,
            icon: const Icon(Icons.add_circle_outline),
          ),
        ],
      ),
    );
  }

  Widget _buildStep3Dosage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Dosage',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.navy,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Spécifiez la quantité et l\'unité de votre médicament.',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.grey600,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                flex: 2,
                child: ModernTextField(
                  controller: _dosageAmountCtrl,
                  label: 'Quantité',
                  hint: 'Ex: 1, 1/2, 2.5...', 
                  keyboardType: TextInputType.text,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                flex: 1,
                child: ModernDropdown<String>(
                  label: 'Unité',
                  value: _dosageUnit,
                  items: _dosageUnits
                      .map((u) => DropdownMenuItem<String>(
                            value: u,
                            child: Text(u, overflow: TextOverflow.ellipsis),
                          ))
                      .toList(),
                  onChanged: (v) => setState(() => _dosageUnit = v ?? _dosageUnit),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStep4DurationAndInstructions() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Durée & Notes',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.navy,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Définissez la durée du traitement et ajoutez des instructions.',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.grey600,
            ),
          ),
          const SizedBox(height: 20),
          _datePickerTile(
            label: 'Date de début',
            date: _startDate,
            onPick: (d) => setState(() => _startDate = d),
          ),
          const SizedBox(height: 8),
          _datePickerTile(
            label: 'Date de fin (optionnelle)',
            date: _endDate,
            firstDate: _startDate,
            allowNull: true,
            onPick: (d) => setState(() => _endDate = d),
          ),
          const SizedBox(height: 12),
          ModernTextField(
            controller: _instructionsCtrl,
            label: 'Notes',
            hint: 'Ex: Prendre avec de la nourriture...', 
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _datePickerTile({
    required String label,
    required DateTime? date,
    DateTime? firstDate,
    DateTime? lastDate,
    required ValueChanged<DateTime?> onPick,
    bool allowNull = false,
  }) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Icon(Icons.calendar_today, color: AppColors.teal),
      title: Text(label),
      subtitle: Text(
        date != null ? _formatDate(date) : (allowNull ? 'Optionnel' : 'Obligatoire'),
        style: AppTextStyles.bodySmall.copyWith(color: AppColors.grey700),
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (allowNull && date != null)
            IconButton(
              icon: Icon(Icons.clear, color: AppColors.error),
              onPressed: () => onPick(null),
            ),
          Icon(Icons.chevron_right, color: AppColors.grey500),
        ],
      ),
      onTap: () async {
        final now = DateTime.now();
        final init = date ?? firstDate ?? now;
        final fd = firstDate ?? now;
        final ld = lastDate ?? now.add(const Duration(days: 365));
        final picked = await showDatePicker(
          context: context,
          initialDate: init,
          firstDate: fd,
          lastDate: ld,
        );
        onPick(picked);
      },
    );
  }

  String _formatDate(DateTime d) {
    final dd = d.day.toString().padLeft(2, '0');
    final mm = d.month.toString().padLeft(2, '0');
    final yyyy = d.year.toString();
    return '$dd/$mm/$yyyy';
  }

  double? _parseDosage(String input) {
    if (input.isEmpty) return null;
    if (input.contains('/')) {
      final parts = input.split('/');
      if (parts.length == 2) {
        final n = double.tryParse(parts[0]);
        final d = double.tryParse(parts[1]);
        if (n != null && d != null && d != 0) {
          return n / d;
        }
      }
      return null;
    }
    return double.tryParse(input);
  }

  String _normalizeFrequencyTypeForBackend(String freq) {
    switch (freq) {
      case 'EVERY_X_DAYS':
        return 'DAILY';
      case 'MONTHLY':
        return 'WEEKLY';
      case 'AS_NEEDED':
        return 'DAILY';
      default:
        return freq;
    }
  }

  int? _normalizeFrequencyValueForBackend(String originalFreq, int? value) {
    if (originalFreq == 'HOURLY_INTERVAL') return value;
    return null;
  }
}

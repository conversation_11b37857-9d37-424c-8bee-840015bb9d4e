import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/supabase_utils.dart';
import '../../../domain/entities/medicine.dart';
import '../../../domain/entities/reminder.dart';
import '../../../domain/entities/reminder_schedule.dart';
import '../../bloc/medicine/medicine_bloc.dart';
import '../../bloc/reminder/reminder_bloc.dart';
import '../../bloc/reminder/reminder_event.dart';
import '../../bloc/reminder/reminder_state.dart';

/// Test implementation of the new multi-step reminder creation flow with pagination.
/// Steps:
///   0 - Medicine selection
///   1 - Frequency selection (Every X days/weeks/months, As needed, Every X hours)
///   2 - Frequency detail (conditional UI for intervals / times)
///   3 - Dosage definition (amount + unit + time selectors consistent with frequency)
///   4 - Duration & Instructions + Summary preview and Save
class AddReminderTestPage extends StatefulWidget {
  const AddReminderTestPage({super.key});

  @override
  State<AddReminderTestPage> createState() => _AddReminderTestPageState();
}

class _AddReminderTestPageState extends State<AddReminderTestPage> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  // Accumulated state
  Medicine? _selectedMedicine;

  // Frequency step selections
  ReminderScheduleFrequencyType? _chosenFrequency;

  // Step 2 details
  int _everyXDays = 1;
  int _everyXWeeks = 1;
  int _everyXMonths = 1;
  List<int> _weekdays = []; // 1..7 Mon..Sun
  int _timesPerDay = 1;
  int _everyXHours = 6;
  DateTime? _startDate;
  List<String> _times = []; // HH:mm strings

  // Step 3/4: dosage and instructions
  final _dosageAmountCtrl = TextEditingController();
  String _dosageUnit = 'comprimé';
  final List<String> _dosageUnits = const [
    'comprimé',
    'gélule',
    'goutte',
    'ml',
    'mg',
    'g',
    'cuillère à café',
    'cuillère à soupe',
    'sachet',
    'ampoule',
    'suppositoire',
    'patch',
    'inhalation',
    'injection',
  ];

  DateTime? _endDate;
  final _instructionsCtrl = TextEditingController();

  Timer? _searchDebounceTimer;

  @override
  void initState() {
    super.initState();
    _bootstrapMedicineSearch();
    _startDate = DateTime.now();
    _times = ['08:00'];
    _timesPerDay = 1;
    _chosenFrequency = ReminderScheduleFrequencyType.everyXDays;
  }

  void _bootstrapMedicineSearch() {
    final householdId = SupabaseUtils.getHouseholdId(context);
    if (householdId != null) {
      context.read<MedicineBloc>().add(MedicineLoadRequested(householdId: householdId));
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _dosageAmountCtrl.dispose();
    _instructionsCtrl.dispose();
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  void _next() {
    if (_validateStep(_currentPage)) {
      if (_currentPage < 4) {
        setState(() => _currentPage += 1);
        _pageController.animateToPage(
          _currentPage,
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  void _back() {
    if (_currentPage > 0) {
      setState(() => _currentPage -= 1);
      _pageController.animateToPage(
        _currentPage,
        duration: const Duration(milliseconds: 250),
        curve: Curves.easeInOut,
      );
    } else {
      context.pop();
    }
  }

  bool _validateStep(int page) {
    switch (page) {
      case 0:
        if (_selectedMedicine == null) {
          _showSnack('Veuillez sélectionner un médicament.');
          return false;
        }
        return true;
      case 1:
        if (_chosenFrequency == null) {
          _showSnack('Veuillez choisir une fréquence.');
          return false;
        }
        return true;
      case 2:
        if (_chosenFrequency == ReminderScheduleFrequencyType.everyXDays && _everyXDays < 1) {
          _showSnack('L\'intervalle en jours doit être ≥ 1.');
          return false;
        }
        if (_chosenFrequency == ReminderScheduleFrequencyType.weekly && _weekdays.isEmpty) {
          _showSnack('Veuillez sélectionner au moins un jour de la semaine.');
          return false;
        }
        if (_chosenFrequency == ReminderScheduleFrequencyType.monthly && _everyXMonths < 1) {
          _showSnack('L\'intervalle en mois doit être ≥ 1.');
          return false;
        }
        if (_chosenFrequency == ReminderScheduleFrequencyType.everyXHours && _everyXHours < 1) {
          _showSnack('L\'intervalle en heures doit être ≥ 1.');
          return false;
        }
        // times are not required for AS_NEEDED and Every X hours; required otherwise
        final needsTimes = _chosenFrequency != ReminderScheduleFrequencyType.asNeeded &&
            _chosenFrequency != ReminderScheduleFrequencyType.everyXHours &&
            _chosenFrequency != ReminderScheduleFrequencyType.customDates;
        if (needsTimes && _times.isEmpty) {
          _showSnack('Veuillez définir au moins une heure.');
          return false;
        }
        return true;
      case 3:
        // dosage format validation
        final value = _dosageAmountCtrl.text.trim();
        final regex = RegExp(r'^(\d+(\.\d+)?|\d+\/\d+)$');
        if (value.isEmpty || !regex.hasMatch(value)) {
          _showSnack('Veuillez saisir un dosage valide (ex: 1, 1/2, 2.5).');
          return false;
        }
        return true;
      case 4:
        if (_startDate != null && _endDate != null && _endDate!.isBefore(_startDate!)) {
          _showSnack('La date de fin doit être après la date de début.');
          return false;
        }
        return true;
      default:
        return true;
    }
  }

  void _showSnack(String msg) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(msg)));
  }

  // =================== Shared helpers (declared early to avoid forward-ref issues) ===================

  String _normalizeFrequencyTypeForBackend(String freq) {
    // Backend supports only: DAILY, WEEKLY, HOURLY_INTERVAL, SPECIFIC_DATES
    switch (freq) {
      case 'EVERY_X_DAYS':
        return 'DAILY';
      case 'MONTHLY':
        return 'WEEKLY';
      case 'AS_NEEDED':
        return 'DAILY';
      default:
        return freq;
    }
  }

  int? _normalizeFrequencyValueForBackend(String originalFreq, int? value) {
    // Keep value only for hourly interval
    if (originalFreq == 'HOURLY_INTERVAL') return value;
    return null;
  }

  Widget _sectionHeader(IconData icon, String title) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.teal.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: AppColors.teal, size: 20),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: AppTextStyles.titleLarge.copyWith(fontWeight: FontWeight.w600, color: AppColors.grey800),
        ),
      ],
    );
  }

  Widget _numberTile({
    required String label,
    required int value,
    required ValueChanged<int> onChanged,
    int min = 1,
    int max = 100,
  }) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      title: Text(label, style: AppTextStyles.titleSmall),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            onPressed: value > min ? () => onChanged(value - 1) : null,
            icon: const Icon(Icons.remove_circle_outline),
          ),
          Text('$value', style: AppTextStyles.titleSmall),
          IconButton(
            onPressed: value < max ? () => onChanged(value + 1) : null,
            icon: const Icon(Icons.add_circle_outline),
          ),
        ],
      ),
    );
  }

  Future<String?> _pickTime(BuildContext context, {required String initial}) async {
    final pieces = initial.split(':');
    final h = int.tryParse(pieces[0]) ?? 8;
    final m = int.tryParse(pieces[1]) ?? 0;
    final t = await showTimePicker(context: context, initialTime: TimeOfDay(hour: h, minute: m));
    if (t == null) return null;
    return '${t.hour.toString().padLeft(2, '0')}:${t.minute.toString().padLeft(2, '0')}';
  }

  String _weekdayShort(int d) {
    switch (d) {
      case 1:
        return 'Lun';
      case 2:
        return 'Mar';
      case 3:
        return 'Mer';
      case 4:
        return 'Jeu';
      case 5:
        return 'Ven';
      case 6:
        return 'Sam';
      case 7:
        return 'Dim';
      default:
        return '';
    }
  }

  String _formatDate(DateTime d) {
    final dd = d.day.toString().padLeft(2, '0');
    final mm = d.month.toString().padLeft(2, '0');
    final yyyy = d.year.toString();
    return '$dd/$mm/$yyyy';
  }

  double? _parseDosage(String input) {
    if (input.isEmpty) return null;
    if (input.contains('/')) {
      final parts = input.split('/');
      if (parts.length == 2) {
        final n = double.tryParse(parts[0]);
        final d = double.tryParse(parts[1]);
        if (n != null && d != null && d != 0) {
          return n / d;
        }
      }
      return null;
    }
    return double.tryParse(input);
  }

  void _commitAndSave() {
    // Build schedule
    final dosageAmount = _parseDosage(_dosageAmountCtrl.text.trim());
    final schedule = ReminderSchedule(
      frequencyType: _chosenFrequency!,
      intervalDays: _chosenFrequency == ReminderScheduleFrequencyType.everyXDays ? _everyXDays : null,
      intervalWeeks: _chosenFrequency == ReminderScheduleFrequencyType.weekly ? _everyXWeeks : null,
      intervalMonths: _chosenFrequency == ReminderScheduleFrequencyType.monthly ? _everyXMonths : null,
      intervalHours: _chosenFrequency == ReminderScheduleFrequencyType.everyXHours ? _everyXHours : null,
      daysOfWeek: _chosenFrequency == ReminderScheduleFrequencyType.weekly ? _weekdays : const [],
      times: _times,
      specificDates: const [],
      startDate: _startDate,
      endDate: _endDate,
      dosageAmount: dosageAmount,
      dosageUnit: _dosageUnit,
      instructions: _instructionsCtrl.text.trim().isEmpty ? null : _instructionsCtrl.text.trim(),
    );

    // Validate using model rules
    final errors = schedule.validate();
    if (errors.isNotEmpty) {
      _showSnack(errors.first);
      return;
    }

    // Map to Reminder insertion fields then create Reminder entity
    final mapped = schedule.toReminderInsertion(
      userMedicineId: _selectedMedicine!.id,
      nowFallback: DateTime.now(),
      name: _selectedMedicine!.displayName,
    );

    // Normalize to currently supported backend values to avoid 400s:
    final normalizedType = _normalizeFrequencyTypeForBackend(mapped['frequencyType'] as String);
    final normalizedValue = _normalizeFrequencyValueForBackend(
      mapped['frequencyType'] as String,
      mapped['frequencyValue'] as int?,
    );

    final reminder = Reminder(
      userMedicineId: mapped['userMedicineId'],
      name: mapped['name'],
      // IMPORTANT: Do NOT send dosage fields to backend if column does not exist
      // dosageAmount / dosageUnit are excluded to avoid Postgrest PGRST204 error
      times: List<String>.from(mapped['times'] ?? const []),
      frequencyType: normalizedType,
      frequencyValue: normalizedValue,
      frequencyDays: List<int>.from(mapped['frequencyDays'] ?? const []),
      specificDates: List<DateTime>.from(mapped['specificDates'] ?? const []),
      startDate: mapped['startDate'],
      endDate: mapped['endDate'],
      notes: mapped['notes'],
      isActive: true,
      status: ReminderStatus.active,
    );

    context.read<ReminderBloc>().add(AddReminder(reminder));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      appBar: AppBar(
        backgroundColor: AppColors.teal,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: _back,
        ),
        title: Text(
          'Nouveau rappel (Test)',
          style: AppTextStyles.titleLarge.copyWith(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        centerTitle: false,
      ),
      body: BlocListener<ReminderBloc, ReminderState>(
        listener: (context, state) {
          if (state is ReminderOperationSuccess) {
            _showCompletionDialog(state.message);
          } else if (state is ReminderError) {
            _showErrorDialog(state.message);
          }
        },
        child: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
          ),
          child: Column(
            children: [
              _buildStepperHeader(),
              const SizedBox(height: 8),
              Expanded(
                child: PageView(
                  controller: _pageController,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    _buildStep0Medicine(),
                    _buildStep1Frequency(),
                    _buildStep2FrequencyDetail(),
                    _buildStep3Dosage(),
                    _buildStep4DurationAndInstructions(),
                  ],
                ),
              ),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStepperHeader() {
    final titles = [
      'Médicament',
      'Fréquence',
      'Détails',
      'Dosage',
      'Durée & Notes',
    ];
    return Padding(
      padding: const EdgeInsets.only(top: 12, left: 16, right: 16),
      child: Row(
        children: List.generate(titles.length, (i) {
          final active = i == _currentPage;
          return Expanded(
            child: Container(
              margin: EdgeInsets.only(left: i == 0 ? 0 : 6, right: i == titles.length - 1 ? 0 : 6),
              padding: const EdgeInsets.symmetric(vertical: 10),
              decoration: BoxDecoration(
                color: active ? AppColors.teal : AppColors.grey200,
                borderRadius: BorderRadius.circular(12),
              ),
              alignment: Alignment.center,
              child: Text(
                titles[i],
                style: AppTextStyles.bodySmall.copyWith(
                  color: active ? Colors.white : AppColors.grey700,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildFooter() {
    final isLast = _currentPage == 4;
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: AppColors.grey200)),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: _back,
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: AppColors.teal),
                foregroundColor: AppColors.teal,
                padding: const EdgeInsets.symmetric(vertical: 14),
              ),
              child: const Text('Retour'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              onPressed: isLast ? _commitAndSave : _next,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.teal,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 14),
              ),
              child: Text(isLast ? 'Enregistrer' : 'Suivant'),
            ),
          ),
        ],
      ),
    );
  }

  // Step 0: Medicine selection (reuse existing autocomplete/search)
  Widget _buildStep0Medicine() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _sectionHeader(Icons.medication, 'Sélection du médicament'),
          const SizedBox(height: 12),
          BlocBuilder<MedicineBloc, MedicineState>(
            builder: (context, state) {
              List<Medicine> medicines = [];
              bool isLoading = false;

              if (state is MedicineLoaded) {
                medicines = state.medicines;
              } else if (state is MedicineSearchResults) {
                medicines = state.searchResults;
              } else if (state is MedicineLoading) {
                isLoading = true;
              }

              return Autocomplete<Medicine>(
                displayStringForOption: (Medicine medicine) => medicine.displayName,
                optionsBuilder: (TextEditingValue textEditingValue) {
                  if (textEditingValue.text.isEmpty) {
                    return const Iterable<Medicine>.empty();
                  }

                  final query = textEditingValue.text.toLowerCase();
                  return medicines.where((medicine) {
                    return medicine.displayName.toLowerCase().contains(query) ||
                        medicine.name.toLowerCase().contains(query) ||
                        (medicine.medicineName?.toLowerCase().contains(query) ?? false) ||
                        (medicine.dci?.toLowerCase().contains(query) ?? false) ||
                        (medicine.laboratoire?.toLowerCase().contains(query) ?? false);
                  });
                },
                onSelected: (Medicine medicine) {
                  setState(() {
                    _selectedMedicine = medicine;
                  });
                },
                fieldViewBuilder: (context, textEditingController, focusNode, onFieldSubmitted) {
                  if (_selectedMedicine != null && textEditingController.text.isEmpty) {
                    textEditingController.text = _selectedMedicine!.displayName;
                  }

                  return TextFormField(
                    controller: textEditingController,
                    focusNode: focusNode,
                    decoration: InputDecoration(
                      labelText: 'Médicament',
                      hintText: 'Rechercher un médicament...',
                      prefixIcon: const Icon(Icons.search, color: AppColors.teal),
                      suffixIcon: isLoading
                          ? const Padding(
                              padding: EdgeInsets.all(12.0),
                              child: SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.teal),
                                ),
                              ),
                            )
                          : textEditingController.text.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    textEditingController.clear();
                                    setState(() {
                                      _selectedMedicine = null;
                                    });
                                  },
                                )
                              : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: AppColors.grey300),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: AppColors.grey300),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: AppColors.teal, width: 2),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                    onChanged: (value) {
                      _searchDebounceTimer?.cancel();
                      if (value.isNotEmpty && value.length >= 2) {
                        _searchDebounceTimer = Timer(const Duration(milliseconds: 400), () {
                          final householdId = SupabaseUtils.getHouseholdId(context);
                          if (householdId != null) {
                            context.read<MedicineBloc>().add(
                                  MedicineSearchRequested(householdId: householdId, query: value),
                                );
                          }
                        });
                      } else if (value.isEmpty) {
                        final householdId = SupabaseUtils.getHouseholdId(context);
                        if (householdId != null) {
                          context.read<MedicineBloc>().add(MedicineLoadRequested(householdId: householdId));
                        }
                      }
                    },
                  );
                },
                optionsViewBuilder: (context, onSelected, options) {
                  return Align(
                    alignment: Alignment.topLeft,
                    child: Material(
                      elevation: 4,
                      borderRadius: BorderRadius.circular(12),
                      child: ConstrainedBox(
                        constraints: const BoxConstraints(maxHeight: 220),
                        child: ListView.builder(
                          padding: const EdgeInsets.all(8),
                          itemCount: options.length,
                          itemBuilder: (context, index) {
                            final medicine = options.elementAt(index);
                            return ListTile(
                              leading: Container(
                                width: 40,
                                height: 40,
                                decoration: BoxDecoration(
                                  color: AppColors.teal.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: const Icon(Icons.medication, color: AppColors.teal, size: 20),
                              ),
                              title: Text(
                                medicine.name,
                                style: AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.w500),
                              ),
                              subtitle: medicine.form != null
                                  ? Text(
                                      medicine.form!,
                                      style: AppTextStyles.bodySmall.copyWith(color: AppColors.grey600),
                                    )
                                  : null,
                              onTap: () => onSelected(medicine),
                            );
                          },
                        ),
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  // Frequency option tile helper
  Widget _freqOptionTile({
    required String title,
    required ReminderScheduleFrequencyType type,
    required IconData icon,
  }) {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: RadioListTile<ReminderScheduleFrequencyType>(
        value: type,
        groupValue: _chosenFrequency,
        onChanged: (v) => setState(() => _chosenFrequency = v),
        title: Text(title, style: AppTextStyles.titleSmall),
        secondary: Icon(icon, color: AppColors.teal),
        activeColor: AppColors.teal,
      ),
    );
  }

  // Step 1: Frequency selection
  Widget _buildStep1Frequency() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: ListView(
        children: [
          _sectionHeader(Icons.repeat, 'Fréquence'),
          const SizedBox(height: 12),
          _freqOptionTile(
            title: 'Tous les X jours',
            type: ReminderScheduleFrequencyType.everyXDays,
            icon: Icons.repeat,
          ),
          _freqOptionTile(
            title: 'Toutes les X semaines (jours spécifiques)',
            type: ReminderScheduleFrequencyType.weekly,
            icon: Icons.date_range,
          ),
          _freqOptionTile(
            title: 'Tous les X mois',
            type: ReminderScheduleFrequencyType.monthly,
            icon: Icons.calendar_today,
          ),
          _freqOptionTile(
            title: 'Si nécessaire',
            type: ReminderScheduleFrequencyType.asNeeded,
            icon: Icons.emergency_share,
          ),
          _freqOptionTile(
            title: 'Toutes les X heures',
            type: ReminderScheduleFrequencyType.everyXHours,
            icon: Icons.access_time,
          ),
          const SizedBox(height: 16),
          Text(
            _buildFrequencyTeaser(),
            style: AppTextStyles.bodySmall.copyWith(color: AppColors.grey700),
          ),
        ],
      ),
    );
  }

  String _buildFrequencyTeaser() {
    switch (_chosenFrequency) {
      case ReminderScheduleFrequencyType.everyXDays:
        return 'Planifier une prise tous les $_everyXDays jour(s).';
      case ReminderScheduleFrequencyType.weekly:
        if (_weekdays.isEmpty) return 'Planifier des jours de semaine spécifiques.';
        return 'Planifier chaque semaine les: ${_weekdays.map(_weekdayShort).join(', ')}.';
      case ReminderScheduleFrequencyType.monthly:
        return 'Planifier tous les $_everyXMonths mois.';
      case ReminderScheduleFrequencyType.asNeeded:
        return 'Aucune heure requise. Rappel si nécessaire.';
      case ReminderScheduleFrequencyType.everyXHours:
        return 'Rappel toutes les $_everyXHours heures.';
      case ReminderScheduleFrequencyType.customDates:
        return 'Dates spécifiques (non configuré sur cet écran).';
      default:
        return '';
    }
  }

  // Step 2: Frequency detail
  Widget _buildStep2FrequencyDetail() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: ListView(
        children: [
          _sectionHeader(Icons.tune, 'Détails de la fréquence'),
          const SizedBox(height: 12),
          if (_chosenFrequency == ReminderScheduleFrequencyType.everyXDays) ...[
            _numberTile(
              label: 'Tous les X jours',
              value: _everyXDays,
              onChanged: (v) => setState(() => _everyXDays = v),
              min: 1,
              max: 90,
            ),
            const SizedBox(height: 12),
            _timeEditors(),
          ] else if (_chosenFrequency == ReminderScheduleFrequencyType.weekly) ...[
            _numberTile(
              label: 'Toutes les X semaines',
              value: _everyXWeeks,
              onChanged: (v) => setState(() => _everyXWeeks = v),
              min: 1,
              max: 12,
            ),
            const SizedBox(height: 12),
            _weekdaySelector(),
            const SizedBox(height: 12),
            _timeEditors(),
          ] else if (_chosenFrequency == ReminderScheduleFrequencyType.monthly) ...[
            _numberTile(
              label: 'Tous les X mois',
              value: _everyXMonths,
              onChanged: (v) => setState(() => _everyXMonths = v),
              min: 1,
              max: 24,
            ),
            const SizedBox(height: 12),
            _timeEditors(),
          ] else if (_chosenFrequency == ReminderScheduleFrequencyType.everyXHours) ...[
            _numberTile(
              label: 'Toutes les X heures',
              value: _everyXHours,
              onChanged: (v) => setState(() => _everyXHours = v),
              min: 1,
              max: 24,
            ),
            const SizedBox(height: 12),
            _datePickerTile(
              label: 'Date de début',
              date: _startDate,
              onPick: (d) => setState(() => _startDate = d),
            ),
            // Times are optional for every X hours
          ] else if (_chosenFrequency == ReminderScheduleFrequencyType.asNeeded) ...[
            _datePickerTile(
              label: 'Date de début',
              date: _startDate,
              onPick: (d) => setState(() => _startDate = d),
            ),
            // No times required
          ],
        ],
      ),
    );
  }

  Widget _timeEditors() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Heures de prise', style: AppTextStyles.titleSmall),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            ..._times.asMap().entries.map((e) => _timeChip(e.key, e.value)),
            ActionChip(
              avatar: const Icon(Icons.add, size: 16),
              label: const Text('Ajouter une heure'),
              onPressed: () async {
                final t = await _pickTime(context, initial: _times.isNotEmpty ? _times.last : '08:00');
                if (t != null) {
                  setState(() => _times = [..._times, t]);
                }
              },
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (_chosenFrequency == ReminderScheduleFrequencyType.everyXDays ||
            _chosenFrequency == ReminderScheduleFrequencyType.weekly ||
            _chosenFrequency == ReminderScheduleFrequencyType.monthly)
          Row(
            children: [
              const Text('Prises par jour:'),
              const SizedBox(width: 12),
              DropdownButton<int>(
                value: _timesPerDay.clamp(1, 8),
                items: List.generate(8, (i) => i + 1).map((n) => DropdownMenuItem(value: n, child: Text('$n'))).toList(),
                onChanged: (v) {
                  if (v == null) return;
                  setState(() {
                    _timesPerDay = v;
                    // Adjust times length
                    if (_times.length > v) {
                      _times = _times.take(v).toList();
                    } else if (_times.length < v) {
                      final last = _times.isNotEmpty ? _times.last : '08:00';
                      while (_times.length < v) {
                        _times.add(last);
                      }
                    }
                  });
                },
              ),
            ],
          ),
      ],
    );
  }

  Widget _weekdaySelector() {
    final names = const ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'];
    return Wrap(
      spacing: 8,
      children: List.generate(7, (i) {
        final day = i + 1;
        final selected = _weekdays.contains(day);
        return FilterChip(
          label: Text(names[i]),
          selected: selected,
          onSelected: (s) {
            setState(() {
              if (s) {
                _weekdays = [..._weekdays, day];
              } else {
                _weekdays = _weekdays.where((d) => d != day).toList();
              }
            });
          },
          selectedColor: AppColors.teal.withValues(alpha: 0.2),
          checkmarkColor: AppColors.teal,
        );
      }),
    );
  }

  Widget _timeChip(int index, String time) {
    return InputChip(
      label: Text(time),
      onPressed: () async {
        final t = await _pickTime(context, initial: time);
        if (t != null) {
          setState(() {
            _times[index] = t;
          });
        }
      },
      onDeleted: () {
        setState(() {
          _times.removeAt(index);
        });
      },
      deleteIcon: const Icon(Icons.close, size: 16),
    );
  }

  // Step 3: Dosage definition
  Widget _buildStep3Dosage() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: ListView(
        children: [
          _sectionHeader(Icons.local_pharmacy, 'Dosage'),
          const SizedBox(height: 12),
          LayoutBuilder(
            builder: (context, constraints) {
              final isNarrow = constraints.maxWidth < 360;
              if (isNarrow) {
                return Column(
                  children: [
                    TextFormField(
                      controller: _dosageAmountCtrl,
                      keyboardType: TextInputType.text,
                      decoration: InputDecoration(
                        hintText: 'Ex: 1, 1/2, 2.5...',
                        filled: true,
                        fillColor: AppColors.grey50,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: AppColors.grey300),
                        ),
                        contentPadding: const EdgeInsets.all(16),
                        prefixIcon: Icon(Icons.straighten, color: AppColors.teal),
                      ),
                    ),
                    const SizedBox(height: 12),
                    DropdownButtonFormField<String>(
                      value: _dosageUnit,
                      isExpanded: true,
                      isDense: true,
                      items: _dosageUnits
                          .map((u) => DropdownMenuItem<String>(
                                value: u,
                                child: Text(u, overflow: TextOverflow.ellipsis),
                              ))
                          .toList(),
                      onChanged: (v) => setState(() => _dosageUnit = v ?? _dosageUnit),
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: AppColors.grey50,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: AppColors.grey300),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                      ),
                    ),
                  ],
                );
              }
              return Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: TextFormField(
                  controller: _dosageAmountCtrl,
                  keyboardType: TextInputType.text,
                      decoration: InputDecoration(
                        hintText: 'Ex: 1, 1/2, 2.5...',
                        filled: true,
                        fillColor: AppColors.grey50,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: AppColors.grey300),
                        ),
                        contentPadding: const EdgeInsets.all(16),
                        prefixIcon: Icon(Icons.straighten, color: AppColors.teal),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Use Flexible + isExpanded to avoid RenderFlex overflow
                  Flexible(
                    child: DropdownButtonFormField<String>(
                      value: _dosageUnit,
                      isExpanded: true,
                      isDense: true,
                      items: _dosageUnits
                          .map((u) => DropdownMenuItem<String>(
                                value: u,
                                child: Text(u, overflow: TextOverflow.ellipsis),
                              ))
                          .toList(),
                      onChanged: (v) => setState(() => _dosageUnit = v ?? _dosageUnit),
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: AppColors.grey50,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: AppColors.grey300),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 16),
          // If frequency implies N times per day, ensure times length equals N
          if (_chosenFrequency != ReminderScheduleFrequencyType.asNeeded &&
              _chosenFrequency != ReminderScheduleFrequencyType.everyXHours)
            _timeEditors(),
        ],
      ),
    );
  }

  // Step 4: Duration & instructions + summary
  Widget _buildStep4DurationAndInstructions() {
    final summary = _previewSummary();
    return Padding(
      padding: const EdgeInsets.all(16),
      child: ListView(
        children: [
          _sectionHeader(Icons.event, 'Durée & Notes'),
          const SizedBox(height: 12),
          _datePickerTile(
            label: 'Date de début',
            date: _startDate,
            onPick: (d) => setState(() => _startDate = d),
          ),
          const SizedBox(height: 8),
          _datePickerTile(
            label: 'Date de fin (optionnelle)',
            date: _endDate,
            firstDate: _startDate,
            allowNull: true,
            onPick: (d) => setState(() => _endDate = d),
          ),
          const SizedBox(height: 12),
          TextFormField(
            controller: _instructionsCtrl,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Ex: Prendre avec de la nourriture, éviter l’alcool…',
              filled: true,
              fillColor: AppColors.grey50,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.grey300),
              ),
              contentPadding: const EdgeInsets.all(12),
              prefixIcon: Icon(Icons.edit_note, color: AppColors.teal),
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.teal.withValues(alpha: 0.08),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.teal.withValues(alpha: 0.25)),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.teal),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    summary,
                    style: AppTextStyles.bodySmall.copyWith(color: AppColors.teal, fontWeight: FontWeight.w600),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _previewSummary() {
    if (_selectedMedicine == null || _chosenFrequency == null) {
      return 'Le résumé s’affiche ici lorsque tous les champs requis sont définis.';
    }
    final dosageAmount = _parseDosage(_dosageAmountCtrl.text.trim());
    final schedule = ReminderSchedule(
      frequencyType: _chosenFrequency!,
      intervalDays: _chosenFrequency == ReminderScheduleFrequencyType.everyXDays ? _everyXDays : null,
      intervalWeeks: _chosenFrequency == ReminderScheduleFrequencyType.weekly ? _everyXWeeks : null,
      intervalMonths: _chosenFrequency == ReminderScheduleFrequencyType.monthly ? _everyXMonths : null,
      intervalHours: _chosenFrequency == ReminderScheduleFrequencyType.everyXHours ? _everyXHours : null,
      daysOfWeek: _chosenFrequency == ReminderScheduleFrequencyType.weekly ? _weekdays : const [],
      times: _times,
      startDate: _startDate,
      endDate: _endDate,
      dosageAmount: dosageAmount,
      dosageUnit: _dosageUnit,
      instructions: _instructionsCtrl.text.trim().isEmpty ? null : _instructionsCtrl.text.trim(),
    );
    return schedule.summary(medicineName: _selectedMedicine!.name);
  }

  Widget _datePickerTile({
    required String label,
    required DateTime? date,
    DateTime? firstDate,
    DateTime? lastDate,
    required ValueChanged<DateTime?> onPick,
    bool allowNull = false,
  }) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Icon(Icons.calendar_today, color: AppColors.teal),
      title: Text(label),
      subtitle: Text(
        date != null ? _formatDate(date) : (allowNull ? 'Optionnel' : 'Obligatoire'),
        style: AppTextStyles.bodySmall.copyWith(color: AppColors.grey700),
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (allowNull && date != null)
            IconButton(
              icon: Icon(Icons.clear, color: AppColors.error),
              onPressed: () => onPick(null),
            ),
          Icon(Icons.chevron_right, color: AppColors.grey500),
        ],
      ),
      onTap: () async {
        final now = DateTime.now();
        final init = date ?? firstDate ?? now;
        final fd = firstDate ?? now;
        final ld = lastDate ?? now.add(const Duration(days: 365));
        final picked = await showDatePicker(
          context: context,
          initialDate: init,
          firstDate: fd,
          lastDate: ld,
        );
        onPick(picked);
      },
    );
  }

  void _showCompletionDialog(String message) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text('Rappel créé'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.go('/reminders');
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text('Erreur'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }
}
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:timeline_tile/timeline_tile.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/supabase_utils.dart';
import '../../../core/services/enhanced_reminder_notification_service.dart';
import '../../../domain/entities/reminder.dart';
import '../../../domain/entities/medicine.dart';
import '../../bloc/reminder/reminder_bloc.dart';
import '../../../core/services/reminder_notification_integration_service.dart';
import '../../bloc/reminder/reminder_event.dart';
import '../../bloc/reminder/reminder_state.dart';
import '../../bloc/medicine/medicine_bloc.dart';
import 'snooze_dialog.dart';

class TodaysMedicines extends StatelessWidget {
  const TodaysMedicines({super.key});

  @override
  Widget build(BuildContext context) {
    // Render with a stable "sticky" cache to avoid flicker to empty after transient states.
    return BlocBuilder<ReminderBloc, ReminderState>(
      buildWhen: (prev, curr) {
        // Rebuild on initial load, reminders loaded, and dose history changes
        return curr is ReminderLoading ||
            curr is RemindersLoaded ||
            curr is DoseHistoryAdded ||
            curr is DoseHistoryLoaded;
      },
      builder: (context, reminderState) {
        return BlocBuilder<MedicineBloc, MedicineState>(
          builder: (context, medicineState) {
            // Maintain a sticky cache of the last non-empty today's list
            // Note: using a static variable scoped by widget type to persist across quick state transitions
            // This avoids a frame where UI goes empty (flicker) when bloc emits intermediate states.
            // ignore: prefer_const_declarations
            final String cacheKey = 'todays_medicines_cache';
            // Simple Inherited cache via PageStorage
            final bucket = PageStorage.of(context);
            List<_TodayReminder> stickyTodays = (bucket?.readState(context,
                    identifier: cacheKey) as List<_TodayReminder>?) ??
                <_TodayReminder>[];

            final bool isLoading = reminderState is ReminderLoading ||
                medicineState is MedicineLoading;
            if (isLoading &&
                stickyTodays.isNotEmpty &&
                medicineState is MedicineLoaded) {
              // While loading, render last known list to prevent blank UI
              return _buildTodaysMedicinesList(
                  context, stickyTodays, medicineState.medicines);
            }

            if (reminderState is ReminderError ||
                medicineState is MedicineError) {
              // On error, if we have a sticky list, keep showing it; otherwise show error text
              if (stickyTodays.isNotEmpty && medicineState is MedicineLoaded) {
                return _buildTodaysMedicinesList(
                    context, stickyTodays, medicineState.medicines);
              }
              return const Center(
                  child: Text('Échec du chargement des rappels.'));
            }

            // Build current reminders list
            List<Reminder> remindersList = [];
            if (reminderState is RemindersLoaded) {
              remindersList = reminderState.reminders;
            } else if (reminderState is DoseHistoryAdded ||
                reminderState is DoseHistoryLoaded) {
              // Stay on current cached reminders from previous RemindersLoaded
              final last =
                  context.select<ReminderBloc, ReminderState>((b) => b.state);
              if (last is RemindersLoaded) {
                remindersList = last.reminders;
              }
            }

            // If medicines not yet loaded but we have sticky, keep showing it
            if (medicineState is! MedicineLoaded) {
              if (stickyTodays.isNotEmpty) {
                return _buildTodaysMedicinesList(
                    context, stickyTodays, const <Medicine>[]);
              }
              return const Center(child: CircularProgressIndicator());
            }

            // Compute today's list from reminders
            final todaysReminders = _getTodaysReminders(remindersList);

            // If computed list is empty but we had a sticky previous one, keep sticky to avoid flicker/empty
            if (todaysReminders.isEmpty && stickyTodays.isNotEmpty) {
              return _buildTodaysMedicinesList(
                  context, stickyTodays, medicineState.medicines);
            }

            // Persist new sticky state if we have items
            if (todaysReminders.isNotEmpty) {
              bucket?.writeState(context, todaysReminders,
                  identifier: cacheKey);
              stickyTodays = todaysReminders;
            }

            // Final render
            if (stickyTodays.isNotEmpty) {
              return _buildTodaysMedicinesList(
                  context, stickyTodays, medicineState.medicines);
            }

            // Only show empty when truly no items and no cache
            return _buildEmptyState();
          },
        );
      },
    );
  }

  List<_TodayReminder> _getTodaysReminders(List<Reminder> reminders) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final todaysReminders = <_TodayReminder>[];

    for (final reminder in reminders.where((r) => r.isCurrentlyActive)) {
      bool isScheduledToday = false;

      switch (reminder.frequencyType) {
        case 'DAILY':
          isScheduledToday = true;
          break;
        case 'WEEKLY':
          if (reminder.frequencyDays.isNotEmpty) {
            final todayWeekday = now.weekday;
            isScheduledToday = reminder.frequencyDays.contains(todayWeekday);
          }
          break;
        case 'HOURLY_INTERVAL':
          isScheduledToday =
              reminder.startDate.isBefore(now.add(const Duration(days: 1)));
          break;
        case 'SPECIFIC_DATES':
          final today = DateTime(now.year, now.month, now.day);
          isScheduledToday = reminder.specificDates.any((date) {
            final dateOnly = DateTime(date.year, date.month, date.day);
            return dateOnly.isAtSameMomentAs(today);
          });
          break;
      }

      if (isScheduledToday) {
        for (final timeString in reminder.times) {
          final timeParts = timeString.split(':');
          final hour = int.parse(timeParts[0]);
          final minute = int.parse(timeParts[1]);
          final reminderDateTime =
              DateTime(today.year, today.month, today.day, hour, minute);

          todaysReminders.add(_TodayReminder(
            reminder: reminder,
            scheduledTime: reminderDateTime,
            timeString: timeString,
          ));
        }
      }
    }

    todaysReminders.sort((a, b) => a.scheduledTime.compareTo(b.scheduledTime));
    return todaysReminders;
  }

  Widget _buildEmptyState() {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              Icons.check_circle_outline,
              size: 48,
              color: AppColors.success,
            ),
            const SizedBox(height: 16),
            Text(
              'Aucun médicament pour aujourd’hui',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.grey600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Vous n’avez aucun rappel programmé pour aujourd’hui.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTodaysMedicinesList(BuildContext context,
      List<_TodayReminder> todaysReminders, List<Medicine> medicines) {
    return Column(
      children: [
        // Duplicate header removed - main header is in dashboard page
        // Padding(
        //   padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        //   child: Align(
        //     alignment: Alignment.centerLeft,
        //     child: Text(
        //       'Doses d’aujourd’hui: ${todaysReminders.length}',
        //       style: AppTextStyles.labelSmall.copyWith(
        //         color: AppColors.grey700,
        //         fontWeight: FontWeight.w600,
        //       ),
        //     ),
        //   ),
        // ),
        // Liste des items
        ...todaysReminders.asMap().entries.map((entry) {
          final index = entry.key;
          final todayReminder = entry.value;
          final medicine = medicines
                  .where((m) => m.id == todayReminder.reminder.userMedicineId)
                  .firstOrNull ??
              Medicine(
                id: '',
                householdId: '',
                customName: 'Unknown Medicine',
                isCustom: true,
                quantity: 0,
                lowStockThreshold: 0,
                tags: const [],
                createdAt: DateTime.now(),
              );

          if (medicine.id.isEmpty) return const SizedBox.shrink();

          return _TodayMedicineItem(
            todayReminder: todayReminder,
            medicine: medicine,
            isFirst: index == 0,
            isLast: index == todaysReminders.length - 1,
          );
        }),
        if (todaysReminders.length > 5)
          Padding(
            padding: const EdgeInsets.all(16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => context.push('/reminders'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.teal,
                  foregroundColor: AppColors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child:
                    Text('Voir tous les rappels (${todaysReminders.length})'),
              ),
            ),
          ),
      ],
    );
  }
}

class _TodayReminder {
  final Reminder reminder;
  final DateTime scheduledTime;
  final String timeString;

  _TodayReminder({
    required this.reminder,
    required this.scheduledTime,
    required this.timeString,
  });
}

DateTime _coerceScheduledAt(_TodayReminder tr) {
  final dt = tr.scheduledTime;
  if (dt is DateTime) return dt;

  final now = DateTime.now();
  final parts = tr.timeString.split(':');
  if (parts.length == 2) {
    final h = int.tryParse(parts[0]) ?? now.hour;
    final m = int.tryParse(parts[1]) ?? now.minute;
    return DateTime(now.year, now.month, now.day, h, m);
  }
  return now;
}

class _TodayMedicineItem extends StatefulWidget {
  final _TodayReminder todayReminder;
  final Medicine medicine;
  final bool isFirst;
  final bool isLast;

  const _TodayMedicineItem({
    required this.todayReminder,
    required this.medicine,
    required this.isFirst,
    required this.isLast,
  });

  @override
  State<_TodayMedicineItem> createState() => _TodayMedicineItemState();
}

enum _DoseUiStatus { pending, taken, snoozed, skipped }

class _TodayMedicineItemState extends State<_TodayMedicineItem>
    with SingleTickerProviderStateMixin {
  _DoseUiStatus _status = _DoseUiStatus.pending;
  DateTime? _takenAt;
  DateTime? _skippedAt;
  DateTime? _snoozedUntil;
  StreamSubscription? _streamSubscription;

  @override
  void initState() {
    super.initState();
    _hydrateInitialUiFromHistory();
  }

  @override
  void dispose() {
    _streamSubscription?.cancel();
    super.dispose();
  }

  Future<void> _hydrateInitialUiFromHistory() async {
    // Fetch latest dose history for this userMedicine and scheduled time and set UI accordingly.
    try {
      final bloc = context.read<ReminderBloc>();
      // Ask bloc to load dose history list for this medicine (if not already)
      bloc.add(LoadDoseHistory(widget.medicine.id));
      // Give bloc a moment to emit state synchronously if cached; otherwise, read current
      final state = bloc.state;
      if (state is DoseHistoryLoaded) {
        _applyLatestForScheduled(state.doseHistory);
      } else {
        // Listen once for loaded state
        _streamSubscription = bloc.stream.listen((s) {
          if (s is DoseHistoryLoaded) {
            _applyLatestForScheduled(s.doseHistory);
          }
        });
        // Cancel after short delay to avoid leaks if nothing comes (UI remains optimistic)
        Future.delayed(
            const Duration(seconds: 2), () => _streamSubscription?.cancel());
      }
    } catch (_) {
      // Ignore errors; UI stays pending until user acts
    }
  }

  void _applyLatestForScheduled(List<DoseHistory> all) {
    if (!mounted) return; // Prevent setState on unmounted widget

    // Match entries for this medicine and scheduled time (same minute precision)
    final scheduled = widget.todayReminder.scheduledTime;
    final list = all
        .where((h) =>
            h.userMedicineId == widget.medicine.id &&
            _isSameMinute(h.scheduledAt, scheduled))
        .toList()
      ..sort((a, b) =>
          (b.actionAt ?? b.scheduledAt).compareTo(a.actionAt ?? a.scheduledAt));

    if (list.isEmpty) return;

    // Find the latest effective status considering undo
    for (final h in list) {
      switch (h.status) {
        case 'UNDO_TAKEN':
          // roll back taken -> continue scanning for earlier state
          continue;
        case 'UNDO_SKIPPED':
          // roll back skipped
          continue;
        case 'TAKEN':
          setState(() {
            _status = _DoseUiStatus.taken;
            _takenAt = h.actionAt ?? DateTime.now();
          });
          return;
        case 'SKIPPED':
          setState(() {
            _status = _DoseUiStatus.skipped;
            _skippedAt = h.actionAt ?? DateTime.now();
          });
          return;
        case 'SNOOZED':
          setState(() {
            _status = _DoseUiStatus.snoozed;
            // Utiliser snoozeAt si présent (source de vérité), sinon fallback actionAt puis scheduled
            _snoozedUntil = h.snoozeAt ?? h.actionAt ?? scheduled;
          });
          return;
        default:
          // Unknown status; ignore and continue
          continue;
      }
    }
  }

  bool _isSameMinute(DateTime a, DateTime b) {
    return a.year == b.year &&
        a.month == b.month &&
        a.day == b.day &&
        a.hour == b.hour &&
        a.minute == b.minute;
  }

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final isOverdue = widget.todayReminder.scheduledTime.isBefore(now) &&
        _status == _DoseUiStatus.pending;

    // Visual styles based on status
    Color cardBg;
    BorderSide borderSide = BorderSide(color: AppColors.grey200, width: 1);
    double opacity = 1.0;
    TextStyle timeStyle = AppTextStyles.bodySmall.copyWith(
      color: _status == _DoseUiStatus.skipped
          ? AppColors.grey700
          : (isOverdue ? AppColors.error : AppColors.success),
      fontWeight: FontWeight.w600,
      fontSize: 11,
      decoration: _status == _DoseUiStatus.skipped
          ? TextDecoration.lineThrough
          : TextDecoration.none,
    );

    switch (_status) {
      case _DoseUiStatus.taken:
        cardBg = AppColors.mintLight; // light green
        break;
      case _DoseUiStatus.snoozed:
        cardBg = Colors.white;
        borderSide = BorderSide(color: AppColors.warning, width: 1.5);
        break;
      case _DoseUiStatus.skipped:
        cardBg = AppColors.grey200;
        opacity = 0.6;
        break;
      case _DoseUiStatus.pending:
      default:
        // Only grey-out for missed time if still pending; taken/snoozed/skipped handled above
        cardBg = isOverdue ? AppColors.grey200 : Colors.white;
    }

    return TimelineTile(
      alignment: TimelineAlign.manual,
      lineXY: 0.15, // Reduced from 0.25 to position timeline closer to left
      isFirst: widget.isFirst,
      isLast: widget.isLast,
      indicatorStyle: IndicatorStyle(
        width: 20,
        height: 20,
        padding: const EdgeInsets.all(4),
        indicator: Container(
          decoration: BoxDecoration(
            color: _status == _DoseUiStatus.skipped
                ? AppColors.grey700
                : (isOverdue
                    ? AppColors.error.withOpacity(0.8)
                    : AppColors.success.withOpacity(0.8)),
            shape: BoxShape.circle,
          ),
          child: Icon(
            _status == _DoseUiStatus.skipped
                ? Icons.remove_done
                : (isOverdue
                    ? Icons.history
                    : Icons.notifications_active_outlined),
            color: Colors.white,
            size: 12,
          ),
        ),
      ),
      beforeLineStyle: LineStyle(
        color: _status == _DoseUiStatus.skipped
            ? AppColors.grey700
            : (isOverdue
                ? AppColors.error.withOpacity(0.8)
                : AppColors.success.withOpacity(0.8)),
        thickness: 1,
      ),
      afterLineStyle: LineStyle(
        color: _status == _DoseUiStatus.skipped
            ? AppColors.grey700
            : (isOverdue
                ? AppColors.error.withOpacity(0.8)
                : AppColors.success.withOpacity(0.8)),
        thickness: 1,
      ),
      endChild: AnimatedOpacity(
        duration: const Duration(milliseconds: 200),
        opacity: opacity,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          child: Card(
            elevation: 1,
            margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: borderSide,
            ),
            color: cardBg,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.medicine.name,
                    style: AppTextStyles.titleMedium.copyWith(
                      color: AppColors.navy,
                      fontWeight: FontWeight.bold,
                      fontSize: AppTextStyles.titleMedium.fontSize! + 2,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${widget.medicine.dosage} ${widget.medicine.form}',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.black,
                      fontWeight: FontWeight.normal,
                      fontSize: AppTextStyles.bodySmall.fontSize! - 2,
                    ),
                  ),
                  const SizedBox(height: 12),
                  AnimatedSwitcher(
                    duration: const Duration(milliseconds: 200),
                    switchInCurve: Curves.easeOut,
                    switchOutCurve: Curves.easeIn,
                    child: _buildActionsOrStatus(context),
                  ),
                  const SizedBox(height: 4),
                  _buildStatusHelperText(),
                ],
              ),
            ),
          ),
        ),
      ),
      startChild: Container(
        alignment: Alignment.center, // Center the time below the indicator
        padding:
            const EdgeInsets.only(right: 4), // Minimal padding to avoid overlap
        child: Text(
          widget.todayReminder.timeString,
          style: timeStyle,
          textAlign: TextAlign.center, // Center the text
        ),
      ),
    );
  }

  Widget _buildActionsOrStatus(BuildContext context) {
    final bottomRight = Alignment.centerRight;

    switch (_status) {
      case _DoseUiStatus.taken:
        return Align(
          key: const ValueKey('taken'),
          alignment: bottomRight,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                'Pris à ${_formatTime(_takenAt ?? DateTime.now())}',
                style: AppTextStyles.labelSmall.copyWith(
                  color: AppColors.success,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 12),
              GestureDetector(
                onTap: () => _undoTaken(context),
                child: Text(
                  'Annuler la prise',
                  style: AppTextStyles.labelSmall.copyWith(
                    color: AppColors.navy,
                    fontWeight: FontWeight.w600,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ],
          ),
        );
      case _DoseUiStatus.snoozed:
        // Afficher un badge en haut à droite avec l'heure de report
        // et conserver les actions (Reporter, Ignorer, [spacer], Prendre)
        return Column(
          key: const ValueKey('snoozed'),
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Spacer(),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.warning.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                        color: AppColors.warning.withValues(alpha: 0.4)),
                  ),
                  child: Text(
                    'Reporté jusqu’à ${_formatTime(_snoozedUntil ?? DateTime.now())}',
                    style: AppTextStyles.labelSmall.copyWith(
                      color: AppColors.warning,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildTextOnlyActionButton(
                    'Reporter',
                    AppColors.grey600,
                    () => _snoozeReminder(context),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildTextOnlyActionButton(
                    'Ignorer',
                    AppColors.grey600,
                    () => _markAsSkipped(context),
                  ),
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: SizedBox(height: 36),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildTextActionButton(
                    'Prendre',
                    AppColors.navy,
                    Colors.white,
                    () => _markAsTaken(context),
                    isBold: true,
                  ),
                ),
              ],
            ),
          ],
        );
      case _DoseUiStatus.skipped:
        return Align(
          key: const ValueKey('skipped'),
          alignment: bottomRight,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                'Ignoré à ${_formatTime(_skippedAt ?? DateTime.now())}',
                style: AppTextStyles.labelSmall.copyWith(
                  color: AppColors.grey700,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 12),
              GestureDetector(
                onTap: () => _undoSkipped(context),
                child: Text(
                  'Annuler l’ignoré',
                  style: AppTextStyles.labelSmall.copyWith(
                    color: AppColors.navy,
                    fontWeight: FontWeight.w600,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ],
          ),
        );
      case _DoseUiStatus.pending:
      default:
        return Row(
          key: const ValueKey('actions'),
          children: [
            Expanded(
              child: _buildTextOnlyActionButton(
                'Reporter',
                AppColors.grey600,
                () => _snoozeReminder(context),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildTextOnlyActionButton(
                'Ignorer',
                AppColors.grey600,
                () => _markAsSkipped(context),
              ),
            ),
            const SizedBox(width: 8),
            const Expanded(
              child: SizedBox(height: 36),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildTextActionButton(
                'Prendre',
                AppColors.navy,
                Colors.white,
                () => _markAsTaken(context),
                isBold: true,
              ),
            ),
          ],
        );
    }
  }

  Widget _buildTextActionButton(
    String label,
    Color backgroundColor,
    Color textColor,
    VoidCallback onTap, {
    bool isBold = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 36,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8),
          border: backgroundColor == Colors.white
              ? Border.all(color: AppColors.grey300)
              : null,
        ),
        child: Center(
          child: Text(
            label,
            style: AppTextStyles.labelSmall.copyWith(
              color: textColor,
              fontSize: 12,
              fontWeight: isBold ? FontWeight.bold : FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextOnlyActionButton(
    String label,
    Color textColor,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        height: 36,
        child: Center(
          child: Text(
            label,
            style: AppTextStyles.labelSmall.copyWith(
              color: textColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  void _markAsTaken(BuildContext context) async {
    final userId = SupabaseUtils.getUserId(context);
    if (userId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Erreur : utilisateur non authentifié'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final scheduledAt = _coerceScheduledAt(widget.todayReminder);

    final doseHistory = DoseHistory(
      userId: userId,
      userMedicineId: widget.medicine.id,
      reminderId: widget.todayReminder.reminder.id,
      scheduledAt: scheduledAt,
      actionAt: DateTime.now(),
      status: 'TAKEN',
    );

    context.read<ReminderBloc>().add(AddDoseHistory(doseHistory));

    // Annuler les notifications liées à ce rappel (pré, principal, post, snoozes)
    try {
      final integration = ReminderNotificationIntegrationService();
      await integration.onDoseTaken(
        reminder: widget.todayReminder.reminder,
        medicine: widget.medicine,
        scheduledTime: scheduledAt,
      );
    } catch (_) {
      // Ignorer les erreurs d’annulation pour ne pas bloquer l’UI
    }

    // Optimistic UI update et feedback utilisateur
    setState(() {
      _status = _DoseUiStatus.taken;
      _takenAt = DateTime.now();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            '${widget.medicine.displayName} • Dose prise à ${_formatTime(_takenAt!)}'),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _markAsSkipped(BuildContext context) {
    final userId = SupabaseUtils.getUserId(context);
    if (userId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Erreur : utilisateur non authentifié'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final scheduledAt = _coerceScheduledAt(widget.todayReminder);

    final doseHistory = DoseHistory(
      userId: userId,
      userMedicineId: widget.medicine.id,
      reminderId: widget.todayReminder.reminder.id,
      scheduledAt: scheduledAt,
      actionAt: DateTime.now(),
      status: 'SKIPPED',
    );

    context.read<ReminderBloc>().add(AddDoseHistory(doseHistory));

    // Optimistic UI update and keep tile visible (do not remove from list)
    // Optimistic: reflect status immediately and keep tile rendered
    setState(() {
      _status = _DoseUiStatus.skipped;
      _skippedAt = DateTime.now();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${widget.medicine.displayName} • Dose ignorée'),
        backgroundColor: AppColors.grey700,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _snoozeReminder(BuildContext context) {
    showSnoozeDialog(
      context: context,
      medicineName: widget.medicine.displayName,
      onSnoozeSelected: (Duration snoozeDuration) async {
        final userId = SupabaseUtils.getUserId(context);
        if (userId == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Erreur : utilisateur non authentifié'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }

        final scheduledAt = _coerceScheduledAt(widget.todayReminder);

        // Calculer la cible de snooze (snooze_at) une seule fois et l'utiliser partout
        final snoozeTarget = DateTime.now().add(snoozeDuration);

        final doseHistory = DoseHistory(
          userId: userId,
          userMedicineId: widget.medicine.id,
          reminderId: widget.todayReminder.reminder.id,
          scheduledAt: scheduledAt,
          actionAt: DateTime.now(),
          status: 'SNOOZED',
          // Persist snooze_at pour cohérence UI/Notification/Backend
          snoozeAt: snoozeTarget,
        );

        context.read<ReminderBloc>().add(AddDoseHistory(doseHistory));

        // UI optimiste avec la même valeur
        setState(() {
          _status = _DoseUiStatus.snoozed;
          _snoozedUntil = snoozeTarget;
        });

        // Planifier la notification exactement à snoozeTarget
        final notificationService = EnhancedReminderNotificationService();
        await notificationService.scheduleSnoozeNotification(
          reminder: widget.todayReminder.reminder,
          medicine: widget.medicine,
          snoozeDuration: snoozeTarget.difference(DateTime.now()),
          snoozeIndex: DateTime.now().millisecondsSinceEpoch % 1000,
        );

        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Rappel reporté jusqu’à ${_formatTime(_snoozedUntil!)}'),
            backgroundColor: AppColors.warning,
            behavior: SnackBarBehavior.floating,
          ),
        );
      },
    );
  }

  String _formatSnoozeDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}h${duration.inMinutes % 60 > 0 ? ' ${duration.inMinutes % 60}min' : ''}';
    } else {
      return '${duration.inMinutes}min';
    }
  }

  // Helper hint line under actions/status to clarify state
  Widget _buildStatusHelperText() {
    switch (_status) {
      case _DoseUiStatus.taken:
        return Align(
          alignment: Alignment.centerRight,
          child: Text(
            'Vous pouvez annuler si nécessaire.',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.grey600,
              fontSize: 11,
            ),
          ),
        );
      case _DoseUiStatus.snoozed:
        // Pas de texte d’aide supplémentaire pour le report (badge déjà affiché)
        return const SizedBox.shrink();
      case _DoseUiStatus.skipped:
        return Align(
          alignment: Alignment.centerRight,
          child: Text(
            'Vous pouvez annuler pour rétablir la prise.',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.grey600,
              fontSize: 11,
            ),
          ),
        );
      case _DoseUiStatus.pending:
      default:
        return const SizedBox.shrink();
    }
  }

  String _formatTime(DateTime dt) {
    final hh = dt.hour.toString().padLeft(2, '0');
    final mm = dt.minute.toString().padLeft(2, '0');
    return '$hh:$mm';
  }

  void _undoTaken(BuildContext context) {
    final userId = SupabaseUtils.getUserId(context);
    if (userId == null) return;

    final scheduledAt = _coerceScheduledAt(widget.todayReminder);

    // Create a compensating history entry to roll back last change
    final doseHistory = DoseHistory(
      userId: userId,
      userMedicineId: widget.medicine.id,
      reminderId: widget.todayReminder.reminder.id,
      scheduledAt: scheduledAt,
      actionAt: DateTime.now(),
      status: 'UNDO_TAKEN',
    );
    context.read<ReminderBloc>().add(AddDoseHistory(doseHistory));

    setState(() {
      _status = _DoseUiStatus.pending;
      _takenAt = null;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Annulation de la prise effectuée'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _undoSkipped(BuildContext context) {
    final userId = SupabaseUtils.getUserId(context);
    if (userId == null) return;

    final scheduledAt = _coerceScheduledAt(widget.todayReminder);

    final doseHistory = DoseHistory(
      userId: userId,
      userMedicineId: widget.medicine.id,
      reminderId: widget.todayReminder.reminder.id,
      scheduledAt: scheduledAt,
      actionAt: DateTime.now(),
      status: 'UNDO_SKIPPED',
    );
    context.read<ReminderBloc>().add(AddDoseHistory(doseHistory));

    setState(() {
      _status = _DoseUiStatus.pending;
      _skippedAt = null;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Annulation de l’ignoré effectuée'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

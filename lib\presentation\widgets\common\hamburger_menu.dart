import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

/// Hamburger menu drawer component
/// Provides navigation to all major app sections
class HamburgerMenu extends StatelessWidget {
  final String? userName;
  final String? userEmail;
  final String? avatarUrl;
  final VoidCallback? onProfileTap;
  final VoidCallback? onLogoutTap;

  const HamburgerMenu({
    super.key,
    this.userName,
    this.userEmail,
    this.avatarUrl,
    this.onProfileTap,
    this.onLogoutTap,
  });

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: Colors.white,
      child: SafeArea(
        child: Column(
          children: [
            // Header with user info
            _buildUserHeader(context),
            
            const SizedBox(height: 24),
            
            // Navigation items
            Expanded(
              child: <PERSON>View(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                children: [
                  _buildMenuItem(
                    context,
                    icon: Icons.dashboard_outlined,
                    title: 'Tableau de bord',
                    onTap: () {
                      Navigator.pop(context);
                      context.go('/dashboard');
                    },
                  ),
                  
                  _buildMenuItem(
                    context,
                    icon: Icons.medication_outlined,
                    title: 'Mes médicaments',
                    onTap: () {
                      Navigator.pop(context);
                      context.go('/medicines/my');
                    },
                  ),
                  
                  _buildMenuItem(
                    context,
                    icon: Icons.add_circle_outline,
                    title: 'Ajouter un médicament',
                    onTap: () {
                      Navigator.pop(context);
                      context.go('/medicines/add');
                    },
                  ),
                  
                  _buildMenuItem(
                    context,
                    icon: Icons.notifications_outlined,
                    title: 'Rappels',
                    onTap: () {
                      Navigator.pop(context);
                      context.go('/reminders');
                    },
                  ),
                  
                  _buildMenuItem(
                    context,
                    icon: Icons.calendar_today_outlined,
                    title: 'Calendrier',
                    onTap: () {
                      Navigator.pop(context);
                      context.go('/calendar');
                    },
                  ),
                  
                  _buildMenuItem(
                    context,
                    icon: Icons.family_restroom_outlined,
                    title: 'Famille',
                    onTap: () {
                      Navigator.pop(context);
                      context.go('/family');
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Divider
                  Divider(
                    color: AppColors.grey300,
                    thickness: 1,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildMenuItem(
                    context,
                    icon: Icons.settings_outlined,
                    title: 'Paramètres',
                    onTap: () {
                      Navigator.pop(context);
                      context.go('/settings');
                    },
                  ),
                  
                  _buildMenuItem(
                    context,
                    icon: Icons.help_outline,
                    title: 'Aide',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Navigate to help screen
                    },
                  ),
                  
                  _buildMenuItem(
                    context,
                    icon: Icons.info_outline,
                    title: 'À propos',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Navigate to about screen
                    },
                  ),
                ],
              ),
            ),
            
            // Logout button
            Padding(
              padding: const EdgeInsets.all(16),
              child: _buildLogoutButton(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserHeader(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.teal,
            AppColors.tealDark,
          ],
        ),
      ),
      child: Column(
        children: [
          // Avatar
          GestureDetector(
            onTap: onProfileTap,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white,
                  width: 3,
                ),
              ),
              child: avatarUrl != null
                  ? ClipOval(
                      child: Image.network(
                        avatarUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => _buildDefaultAvatar(),
                      ),
                    )
                  : _buildDefaultAvatar(),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // User name
          if (userName != null)
            Text(
              userName!,
              style: AppTextStyles.titleLarge.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          
          // User email
          if (userEmail != null) ...[
            const SizedBox(height: 4),
            Text(
              userEmail!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.white.withValues(alpha: 0.9),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDefaultAvatar() {
    return Icon(
      Icons.person,
      color: AppColors.teal,
      size: 40,
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.grey100,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: AppColors.navy,
            size: 22,
          ),
        ),
        title: Text(
          title,
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.navy,
            fontWeight: FontWeight.w500,
          ),
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      ),
    );
  }

  Widget _buildLogoutButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: onLogoutTap,
        icon: Icon(
          Icons.logout,
          color: AppColors.error,
          size: 20,
        ),
        label: Text(
          'Se déconnecter',
          style: AppTextStyles.titleMedium.copyWith(
            color: AppColors.error,
            fontWeight: FontWeight.w500,
          ),
        ),
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: AppColors.error),
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }
}

/// Menu item data class for easier menu configuration
class MenuItem {
  final IconData icon;
  final String title;
  final String route;
  final VoidCallback? customAction;

  const MenuItem({
    required this.icon,
    required this.title,
    required this.route,
    this.customAction,
  });
}

/// Predefined menu items for consistency
class MenuItems {
  static const List<MenuItem> mainItems = [
    MenuItem(
      icon: Icons.dashboard_outlined,
      title: 'Tableau de bord',
      route: '/dashboard',
    ),
    MenuItem(
      icon: Icons.medication_outlined,
      title: 'Mes médicaments',
      route: '/medicines/my',
    ),
    MenuItem(
      icon: Icons.add_circle_outline,
      title: 'Ajouter un médicament',
      route: '/medicines/add',
    ),
    MenuItem(
      icon: Icons.notifications_outlined,
      title: 'Rappels',
      route: '/reminders',
    ),
    MenuItem(
      icon: Icons.calendar_today_outlined,
      title: 'Calendrier',
      route: '/calendar',
    ),
    MenuItem(
      icon: Icons.family_restroom_outlined,
      title: 'Famille',
      route: '/family',
    ),
  ];

  static const List<MenuItem> settingsItems = [
    MenuItem(
      icon: Icons.settings_outlined,
      title: 'Paramètres',
      route: '/settings',
    ),
    MenuItem(
      icon: Icons.help_outline,
      title: 'Aide',
      route: '/help',
    ),
    MenuItem(
      icon: Icons.info_outline,
      title: 'À propos',
      route: '/about',
    ),
  ];
}

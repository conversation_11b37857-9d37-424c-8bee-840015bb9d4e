import '../../domain/entities/tag.dart';

/// Data model for Tag entity
class TagModel extends Tag {
  const TagModel({
    required super.id,
    required super.householdId,
    required super.name,
    required super.color,
    required super.category,
    required super.createdAt,
    super.updatedAt,
  });

  /// Create from JSON
  factory TagModel.fromJson(Map<String, dynamic> json) {
    return TagModel(
      id: json['id'] as String,
      householdId: json['household_id'] as String,
      name: json['name'] as String,
      color: json['color'] as String,
      category: json['category'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'household_id': householdId,
      'name': name,
      'color': color,
      'category': category,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// Create from entity
  factory TagModel.fromEntity(Tag entity) {
    return TagModel(
      id: entity.id,
      householdId: entity.householdId,
      name: entity.name,
      color: entity.color,
      category: entity.category,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  /// Convert to entity
  Tag toEntity() {
    return Tag(
      id: id,
      householdId: householdId,
      name: name,
      color: color,
      category: category,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  /// Copy with method
  @override
  TagModel copyWith({
    String? id,
    String? householdId,
    String? name,
    String? color,
    String? category,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TagModel(
      id: id ?? this.id,
      householdId: householdId ?? this.householdId,
      name: name ?? this.name,
      color: color ?? this.color,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

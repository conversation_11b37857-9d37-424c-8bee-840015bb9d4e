import 'package:equatable/equatable.dart';

import '../../../domain/entities/family_member.dart';
import 'family_manager_event.dart';

/// Base class for family manager states
abstract class FamilyManagerState extends Equatable {
  const FamilyManagerState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class FamilyManagerInitial extends FamilyManagerState {
  const FamilyManagerInitial();
}

/// Loading state
class FamilyManagerLoading extends FamilyManagerState {
  const FamilyManagerLoading();
}

/// Loaded state with family members
class FamilyManagerLoaded extends FamilyManagerState {
  final String householdId;
  final List<FamilyMember> members;
  final List<FamilyMember> filteredMembers;
  final String searchQuery;
  final String? relationshipFilter;
  final FamilyMemberSortOption currentSort;
  final FamilyMember? selectedMember;
  final bool isSearching;

  const FamilyManagerLoaded({
    required this.householdId,
    required this.members,
    required this.filteredMembers,
    this.searchQuery = '',
    this.relationshipFilter,
    this.currentSort = FamilyMemberSortOption.nameAsc,
    this.selectedMember,
    this.isSearching = false,
  });

  /// Get members count
  int get membersCount => members.length;

  /// Get filtered members count
  int get filteredMembersCount => filteredMembers.length;

  /// Check if members are empty
  bool get isEmpty => members.isEmpty;

  /// Check if filtered members are empty
  bool get isFilteredEmpty => filteredMembers.isEmpty;

  /// Check if search is active
  bool get hasSearchQuery => searchQuery.isNotEmpty;

  /// Check if filter is active
  bool get hasActiveFilter => relationshipFilter != null;

  /// Get display members (filtered)
  List<FamilyMember> get displayMembers => filteredMembers;

  /// Get member statistics
  FamilyMemberStatistics get statistics => FamilyMemberStatistics.fromMembers(members);

  /// Copy with method for immutable updates
  FamilyManagerLoaded copyWith({
    String? householdId,
    List<FamilyMember>? members,
    List<FamilyMember>? filteredMembers,
    String? searchQuery,
    String? relationshipFilter,
    bool clearRelationshipFilter = false,
    FamilyMemberSortOption? currentSort,
    FamilyMember? selectedMember,
    bool clearSelectedMember = false,
    bool? isSearching,
  }) {
    return FamilyManagerLoaded(
      householdId: householdId ?? this.householdId,
      members: members ?? this.members,
      filteredMembers: filteredMembers ?? this.filteredMembers,
      searchQuery: searchQuery ?? this.searchQuery,
      relationshipFilter: clearRelationshipFilter ? null : (relationshipFilter ?? this.relationshipFilter),
      currentSort: currentSort ?? this.currentSort,
      selectedMember: clearSelectedMember ? null : (selectedMember ?? this.selectedMember),
      isSearching: isSearching ?? this.isSearching,
    );
  }

  @override
  List<Object?> get props => [
        householdId,
        members,
        filteredMembers,
        searchQuery,
        relationshipFilter,
        currentSort,
        selectedMember,
        isSearching,
      ];
}

/// Error state
class FamilyManagerError extends FamilyManagerState {
  final String message;

  const FamilyManagerError({required this.message});

  @override
  List<Object?> get props => [message];
}

/// Success state for operations
class FamilyManagerOperationSuccess extends FamilyManagerState {
  final String message;
  final FamilyManagerOperationType operationType;

  const FamilyManagerOperationSuccess({
    required this.message,
    required this.operationType,
  });

  @override
  List<Object?> get props => [message, operationType];
}

/// Form validation state
class FamilyMemberFormValidation extends FamilyManagerState {
  final Map<String, String?> errors;
  final bool isValid;

  const FamilyMemberFormValidation({
    required this.errors,
    required this.isValid,
  });

  /// Get error for specific field
  String? getError(String field) => errors[field];

  /// Check if field has error
  bool hasError(String field) => errors[field] != null;

  @override
  List<Object?> get props => [errors, isValid];
}

/// Operation types for success messages
enum FamilyManagerOperationType {
  memberCreated,
  memberUpdated,
  memberDeleted,
}

/// Extension for operation type messages
extension FamilyManagerOperationTypeExtension on FamilyManagerOperationType {
  String get message {
    switch (this) {
      case FamilyManagerOperationType.memberCreated:
        return 'Membre de la famille ajouté avec succès';
      case FamilyManagerOperationType.memberUpdated:
        return 'Membre de la famille mis à jour avec succès';
      case FamilyManagerOperationType.memberDeleted:
        return 'Membre de la famille supprimé avec succès';
    }
  }
}

/// Family member statistics class
class FamilyMemberStatistics extends Equatable {
  final int totalMembers;
  final int adults;
  final int children;
  final int seniors;
  final Map<String, int> relationshipCounts;

  const FamilyMemberStatistics({
    required this.totalMembers,
    required this.adults,
    required this.children,
    required this.seniors,
    required this.relationshipCounts,
  });

  /// Create statistics from list of family members
  factory FamilyMemberStatistics.fromMembers(List<FamilyMember> members) {
    int adults = 0;
    int children = 0;
    int seniors = 0;
    final relationshipCounts = <String, int>{};

    for (final member in members) {
      // Count age groups
      final ageGroup = member.ageGroup;
      switch (ageGroup) {
        case 'adult':
          adults++;
          break;
        case 'child':
        case 'infant':
        case 'adolescent':
          children++;
          break;
        case 'senior':
          seniors++;
          break;
      }

      // Count relationships
      final relationship = member.relationship ?? 'other';
      relationshipCounts[relationship] = (relationshipCounts[relationship] ?? 0) + 1;
    }

    return FamilyMemberStatistics(
      totalMembers: members.length,
      adults: adults,
      children: children,
      seniors: seniors,
      relationshipCounts: relationshipCounts,
    );
  }

  @override
  List<Object?> get props => [
        totalMembers,
        adults,
        children,
        seniors,
        relationshipCounts,
      ];
}

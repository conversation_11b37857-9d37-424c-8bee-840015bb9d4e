import 'package:equatable/equatable.dart';

import '../../../domain/entities/reminder.dart';

abstract class ReminderEvent extends Equatable {
  const ReminderEvent();

  @override
  List<Object?> get props => [];
}

class LoadReminders extends ReminderEvent {
  final String userMedicineId;

  const LoadReminders(this.userMedicineId);

  @override
  List<Object?> get props => [userMedicineId];
}

class LoadBatchReminders extends ReminderEvent {
  final List<String> userMedicineIds;

  const LoadBatchReminders(this.userMedicineIds);

  @override
  List<Object?> get props => [userMedicineIds];
}

class ClearReminders extends ReminderEvent {
  const ClearReminders();

  @override
  List<Object?> get props => [];
}

class AddReminder extends ReminderEvent {
  final Reminder reminder;

  const AddReminder(this.reminder);

  @override
  List<Object?> get props => [reminder];
}

class UpdateReminder extends ReminderEvent {
  final Reminder reminder;

  const UpdateReminder(this.reminder);

  @override
  List<Object?> get props => [reminder];
}

class DeleteReminder extends ReminderEvent {
  final String reminderId;

  const DeleteReminder(this.reminderId);

  @override
  List<Object?> get props => [reminderId];
}

class LoadDoseHistory extends ReminderEvent {
  final String userMedicineId;

  const LoadDoseHistory(this.userMedicineId);

  @override
  List<Object?> get props => [userMedicineId];
}

class AddDoseHistory extends ReminderEvent {
  final DoseHistory doseHistory;

  const AddDoseHistory(this.doseHistory);

  @override
  List<Object?> get props => [doseHistory];
}

class CancelReminder extends ReminderEvent {
  final String reminderId;
  const CancelReminder(this.reminderId);
  @override
  List<Object?> get props => [reminderId];
}
class ToggleReminderActive extends ReminderEvent {
  final String reminderId;
  final bool isActive;

  const ToggleReminderActive(this.reminderId, this.isActive);

  @override
  List<Object?> get props => [reminderId, isActive];
}

class PauseReminder extends ReminderEvent {
  final String reminderId;

  const PauseReminder(this.reminderId);

  @override
  List<Object?> get props => [reminderId];
}

class ResumeReminder extends ReminderEvent {
  final String reminderId;

  const ResumeReminder(this.reminderId);

  @override
  List<Object?> get props => [reminderId];
}

class ArchiveReminder extends ReminderEvent {
  final String reminderId;

  const ArchiveReminder(this.reminderId);

  @override
  List<Object?> get props => [reminderId];
}

import 'package:equatable/equatable.dart';
import 'dart:io';

/// Base class for profile security events
abstract class ProfileSecurityEvent extends Equatable {
  const ProfileSecurityEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load profile security data
class ProfileSecurityLoadRequested extends ProfileSecurityEvent {
  final String userId;

  const ProfileSecurityLoadRequested({required this.userId});

  @override
  List<Object?> get props => [userId];
}

/// Event to update profile name
class ProfileNameUpdateRequested extends ProfileSecurityEvent {
  final String name;

  const ProfileNameUpdateRequested({required this.name});

  @override
  List<Object?> get props => [name];
}

/// Event to update email
class EmailUpdateRequested extends ProfileSecurityEvent {
  final String email;

  const EmailUpdateRequested({required this.email});

  @override
  List<Object?> get props => [email];
}

/// Event to upload avatar
class AvatarUploadRequested extends ProfileSecurityEvent {
  final File imageFile;

  const AvatarUploadRequested({required this.imageFile});

  @override
  List<Object?> get props => [imageFile];
}

/// Event to remove avatar
class AvatarRemoveRequested extends ProfileSecurityEvent {
  const AvatarRemoveRequested();
}

/// Event to change password
class PasswordChangeRequested extends ProfileSecurityEvent {
  final String currentPassword;
  final String newPassword;

  const PasswordChangeRequested({
    required this.currentPassword,
    required this.newPassword,
  });

  @override
  List<Object?> get props => [currentPassword, newPassword];
}

/// Event to toggle biometric authentication
class BiometricToggleRequested extends ProfileSecurityEvent {
  final bool enabled;

  const BiometricToggleRequested({required this.enabled});

  @override
  List<Object?> get props => [enabled];
}

/// Event to set up PIN
class PinSetupRequested extends ProfileSecurityEvent {
  final String pinCode;

  const PinSetupRequested({required this.pinCode});

  @override
  List<Object?> get props => [pinCode];
}

/// Event to remove PIN
class PinRemoveRequested extends ProfileSecurityEvent {
  const PinRemoveRequested();
}

/// Event to toggle auto lock
class AutoLockToggleRequested extends ProfileSecurityEvent {
  final bool enabled;
  final int minutes;

  const AutoLockToggleRequested({
    required this.enabled,
    this.minutes = 5,
  });

  @override
  List<Object?> get props => [enabled, minutes];
}

/// Event to toggle session timeout
class SessionTimeoutToggleRequested extends ProfileSecurityEvent {
  final bool enabled;
  final int minutes;

  const SessionTimeoutToggleRequested({
    required this.enabled,
    this.minutes = 30,
  });

  @override
  List<Object?> get props => [enabled, minutes];
}

/// Event to validate current password
class CurrentPasswordValidationRequested extends ProfileSecurityEvent {
  final String password;

  const CurrentPasswordValidationRequested({required this.password});

  @override
  List<Object?> get props => [password];
}

/// Event to check password strength
class PasswordStrengthCheckRequested extends ProfileSecurityEvent {
  final String password;

  const PasswordStrengthCheckRequested({required this.password});

  @override
  List<Object?> get props => [password];
}

/// Event to delete account
class AccountDeleteRequested extends ProfileSecurityEvent {
  final String password;

  const AccountDeleteRequested({required this.password});

  @override
  List<Object?> get props => [password];
}

/// Event to refresh profile data
class ProfileRefreshRequested extends ProfileSecurityEvent {
  const ProfileRefreshRequested();
}

/// Base class for all exceptions in the application
abstract class AppException implements Exception {
  final String message;
  final String? code;

  const AppException(this.message, {this.code});

  @override
  String toString() => 'AppException(message: $message, code: $code)';
}

/// Server-related exceptions
class ServerException extends AppException {
  const ServerException([String message = 'Server error occurred', String? code])
      : super(message, code: code);
}

/// Network-related exceptions
class NetworkException extends AppException {
  const NetworkException([String message = 'Network error occurred', String? code])
      : super(message, code: code);
}

/// Cache-related exceptions
class CacheException extends AppException {
  const CacheException([String message = 'Cache error occurred', String? code])
      : super(message, code: code);
}

/// Authentication-related exceptions
class AuthException extends AppException {
  const AuthException([String message = 'Authentication error occurred', String? code])
      : super(message, code: code);
}

/// Validation-related exceptions
class ValidationException extends AppException {
  const ValidationException([String message = 'Validation error occurred', String? code])
      : super(message, code: code);
}

/// Permission-related exceptions
class PermissionException extends AppException {
  const PermissionException([String message = 'Permission error occurred', String? code])
      : super(message, code: code);
}

/// Not found exceptions
class NotFoundException extends AppException {
  const NotFoundException([String message = 'Resource not found', String? code])
      : super(message, code: code);
}

/// Conflict exceptions (e.g., duplicate data)
class ConflictException extends AppException {
  const ConflictException([String message = 'Conflict occurred', String? code])
      : super(message, code: code);
}

/// Unknown/unexpected exceptions
class UnknownException extends AppException {
  const UnknownException([String message = 'Unknown error occurred', String? code])
      : super(message, code: code);
}

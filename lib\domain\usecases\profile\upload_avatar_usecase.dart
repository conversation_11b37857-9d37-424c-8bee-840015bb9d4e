import 'package:dartz/dartz.dart';
import 'dart:io';
import '../../repositories/profile_repository.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';

class UploadAvatarParams {
  final File imageFile;

  UploadAvatarParams({required this.imageFile});
}

class UploadAvatarUseCase implements UseCase<String, UploadAvatarParams> {
  final ProfileRepository repository;

  UploadAvatarUseCase(this.repository);

  @override
  Future<Either<Failure, String>> call(UploadAvatarParams params) async {
    return await repository.uploadAvatar(params.imageFile);
  }
}

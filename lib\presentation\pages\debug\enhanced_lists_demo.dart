import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../domain/entities/medicine.dart';
import '../../../domain/entities/family_member.dart';
import '../../../domain/entities/location.dart';
import '../../widgets/medicine/enhanced_medicine_list_card.dart';
import '../../widgets/family/enhanced_family_member_list_card.dart';
import '../../widgets/location/enhanced_location_list_card.dart';

/// Demo page to showcase the enhanced listing components
/// This page demonstrates the new design specifications for all list cards
class EnhancedListsDemoPage extends StatelessWidget {
  const EnhancedListsDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Enhanced List Cards Demo'),
        backgroundColor: AppColors.navy,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.background,
              Colors.white,
            ],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Enhanced List Cards',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppColors.navy,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Redesigned list components with 72dp height, 16dp border radius, and consistent Material 3 styling.',
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.grey600,
                ),
              ),
              const SizedBox(height: 32),

              // Medicine Cards Section
              _buildSection(
                title: 'Medicine Cards',
                description:
                    'Enhanced medicine list cards with status badges and proper typography',
                children: [
                  EnhancedMedicineListCard(
                    medicine: _createSampleMedicine(
                        'Paracétamol 500mg', MedicineStatus.normal),
                    onTap: () => _showCardTapped(context, 'Medicine - Normal'),
                  ),
                  EnhancedMedicineListCard(
                    medicine: _createSampleMedicine(
                        'Ibuprofène 400mg', MedicineStatus.expiringSoon),
                    onTap: () =>
                        _showCardTapped(context, 'Medicine - Expiring Soon'),
                  ),
                  EnhancedMedicineListCard(
                    medicine: _createSampleMedicine(
                        'Aspirine 100mg', MedicineStatus.lowStock),
                    onTap: () =>
                        _showCardTapped(context, 'Medicine - Low Stock'),
                  ),
                  EnhancedMedicineListCard(
                    medicine: _createSampleMedicine(
                        'Vitamine D', MedicineStatus.expired),
                    onTap: () => _showCardTapped(context, 'Medicine - Expired'),
                  ),
                ],
              ),

              const SizedBox(height: 32),

              // Family Member Cards Section
              _buildSection(
                title: 'Family Member Cards',
                description:
                    'Enhanced family member cards with age badges and gender indicators',
                children: [
                  EnhancedFamilyMemberListCard(
                    member: _createSampleFamilyMember(
                        'Marie Dupont', 'Mère', 'female', 45),
                    onTap: () =>
                        _showCardTapped(context, 'Family Member - Marie'),
                  ),
                  EnhancedFamilyMemberListCard(
                    member: _createSampleFamilyMember(
                        'Jean Dupont', 'Père', 'male', 48),
                    onTap: () =>
                        _showCardTapped(context, 'Family Member - Jean'),
                  ),
                  EnhancedFamilyMemberListCard(
                    member: _createSampleFamilyMember(
                        'Sophie Dupont', 'Fille', 'female', 16),
                    onTap: () =>
                        _showCardTapped(context, 'Family Member - Sophie'),
                  ),
                  EnhancedFamilyMemberListCard(
                    member: _createSampleFamilyMember(
                        'Lucas Dupont', 'Fils', 'male', 8),
                    onTap: () =>
                        _showCardTapped(context, 'Family Member - Lucas'),
                  ),
                ],
              ),

              const SizedBox(height: 32),

              // Location Cards Section
              _buildSection(
                title: 'Location Cards',
                description:
                    'Enhanced location cards with medicine counts and type indicators',
                children: [
                  EnhancedLocationListCard(
                    location: _createSampleLocation('Pharmacie familiale',
                        'local_pharmacy', 'Armoire à pharmacie principale'),
                    medicineCount: 24,
                    onTap: () =>
                        _showCardTapped(context, 'Location - Pharmacie'),
                  ),
                  EnhancedLocationListCard(
                    location: _createSampleLocation(
                        'Cuisine', 'kitchen', 'Placard de la cuisine'),
                    medicineCount: 8,
                    onTap: () => _showCardTapped(context, 'Location - Cuisine'),
                  ),
                  EnhancedLocationListCard(
                    location: _createSampleLocation(
                        'Chambre parentale', 'bed', 'Table de nuit'),
                    medicineCount: 5,
                    onTap: () => _showCardTapped(context, 'Location - Chambre'),
                  ),
                  EnhancedLocationListCard(
                    location: _createSampleLocation(
                        'Bureau', 'work', 'Tiroir du bureau'),
                    medicineCount: 2,
                    onTap: () => _showCardTapped(context, 'Location - Bureau'),
                  ),
                ],
              ),

              const SizedBox(height: 32),

              // Compact Versions Section
              _buildSection(
                title: 'Compact Versions',
                description: 'Compact 56dp height versions for dense lists',
                children: [
                  CompactMedicineListCard(
                    medicine: _createSampleMedicine(
                        'Doliprane', MedicineStatus.normal),
                    onTap: () => _showCardTapped(context, 'Compact Medicine'),
                  ),
                  CompactFamilyMemberListCard(
                    member: _createSampleFamilyMember(
                        'Emma', 'Fille', 'female', 12),
                    onTap: () =>
                        _showCardTapped(context, 'Compact Family Member'),
                  ),
                  CompactLocationListCard(
                    location: _createSampleLocation(
                        'Salle de bain', 'bathtub', 'Armoire de toilette'),
                    medicineCount: 6,
                    onTap: () => _showCardTapped(context, 'Compact Location'),
                  ),
                ],
              ),

              const SizedBox(height: 40),

              // Design specifications
              _buildSpecifications(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String description,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.navy,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          description,
          style: const TextStyle(
            fontSize: 14,
            color: AppColors.grey600,
          ),
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildSpecifications() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.grey50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Design Specifications',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.navy,
            ),
          ),
          const SizedBox(height: 12),
          _buildSpecItem('Card Height', '72dp (56dp for compact)'),
          _buildSpecItem('Border Radius', '16dp'),
          _buildSpecItem('Padding', '16dp horizontal, 12dp vertical'),
          _buildSpecItem('Vertical Spacing', '12dp between cards'),
          _buildSpecItem('Primary Icons', '20dp size'),
          _buildSpecItem('Secondary Icons', '16dp size'),
          _buildSpecItem(
              'Status Badges', '8dp vertical, 12dp horizontal padding'),
          _buildSpecItem('Badge Radius', '12dp (pill-shaped)'),
        ],
      ),
    );
  }

  Widget _buildSpecItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.grey700,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.grey900,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Medicine _createSampleMedicine(String name, MedicineStatus status) {
    return Medicine(
      id: 'sample-${name.toLowerCase().replaceAll(' ', '-')}',
      householdId: 'sample-household',
      customName: name,
      isCustom: true,
      dosage: '500mg',
      form: 'Comprimé',
      expiration: DateTime.now().add(const Duration(days: 30)),
      quantity: status == MedicineStatus.lowStock ? 2 : 10,
      locationName: 'Pharmacie',
      familyMemberName: 'Marie',
      createdAt: DateTime.now().subtract(const Duration(days: 7)),
    );
  }

  FamilyMember _createSampleFamilyMember(
      String name, String relationship, String gender, int age) {
    return FamilyMember(
      id: 'sample-${name.toLowerCase().replaceAll(' ', '-')}',
      householdId: 'sample-household',
      name: name,
      relationship: relationship,
      gender: gender,
      dateOfBirth: DateTime.now().subtract(Duration(days: age * 365)),
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
    );
  }

  Location _createSampleLocation(String name, String icon, String description) {
    return Location(
      id: 'sample-${name.toLowerCase().replaceAll(' ', '-')}',
      householdId: 'sample-household',
      name: name,
      description: description,
      icon: icon,
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
    );
  }

  void _showCardTapped(BuildContext context, String cardType) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$cardType card tapped!'),
        duration: const Duration(seconds: 2),
        backgroundColor: AppColors.teal,
      ),
    );
  }
}

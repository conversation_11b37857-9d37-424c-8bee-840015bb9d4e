import 'package:dartz/dartz.dart';
import 'dart:io';
import '../entities/user.dart';
import '../../core/error/failures.dart';

abstract class ProfileRepository {
  /// Update user profile name
  Future<Either<Failure, User>> updateProfileName(String name);

  /// Update user email
  Future<Either<Failure, void>> updateEmail(String email);

  /// Upload user avatar image
  Future<Either<Failure, String>> uploadAvatar(File imageFile);

  /// Remove user avatar
  Future<Either<Failure, void>> removeAvatar();

  /// Get user avatar URL
  Future<Either<Failure, String?>> getAvatarUrl();

  /// Change user password
  Future<Either<Failure, void>> changePassword(String currentPassword, String newPassword);

  /// Delete user account
  Future<Either<Failure, void>> deleteAccount(String password);

  /// Update language preference
  Future<Either<Failure, void>> updateLanguagePreference(String language);

  /// Get language preference
  Future<Either<Failure, String>> getLanguagePreference();

  /// Update theme preference
  Future<Either<Failure, void>> updateThemePreference(bool isDarkMode);

  /// Get theme preference
  Future<Either<Failure, bool>> getThemePreference();
}

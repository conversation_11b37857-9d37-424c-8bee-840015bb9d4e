import 'package:equatable/equatable.dart';
import '../../../domain/entities/user.dart';

/// Authentication states matching web app AuthContext behavior
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

/// Initial state when app starts
class AuthInitial extends AuthState {}

/// Loading state during authentication operations
class AuthLoading extends AuthState {}

/// User is authenticated with profile and household data
class AuthAuthenticated extends AuthState {
  final User user;
  final String? householdId;
  final String? householdName;

  const AuthAuthenticated({
    required this.user,
    this.householdId,
    this.householdName,
  });

  @override
  List<Object?> get props => [user, householdId, householdName];
}

/// User is not authenticated
class AuthUnauthenticated extends AuthState {}

/// User needs to complete onboarding (create household)
class AuthOnboardingRequired extends AuthState {
  final User user;

  const AuthOnboardingRequired({required this.user});

  @override
  List<Object?> get props => [user];
}

/// Authentication error occurred
class AuthError extends AuthState {
  final String message;

  const AuthError({required this.message});

  @override
  List<Object?> get props => [message];
}

/// Sign up was successful
class AuthSignUpSuccess extends AuthState {
  final String message;

  const AuthSignUpSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

/// Profile refresh completed
class AuthProfileRefreshed extends AuthState {
  final User user;
  final String? householdId;
  final String? householdName;

  const AuthProfileRefreshed({
    required this.user,
    this.householdId,
    this.householdName,
  });

  @override
  List<Object?> get props => [user, householdId, householdName];
}

import 'package:dartz/dartz.dart';
import '../error/failures.dart';

/// Base class for all use cases in the application
abstract class UseCase<Type, Params> {
  Future<Either<Failure, Type>> call(Params params);
}

/// Use case with no parameters
abstract class NoParamsUseCase<Type> {
  Future<Either<Failure, Type>> call();
}

/// Class to represent no parameters
class NoParams {
  const NoParams();
}

/// Class to represent pagination parameters
class PaginationParams {
  final int page;
  final int limit;
  final String? search;
  final Map<String, dynamic>? filters;

  const PaginationParams({
    this.page = 1,
    this.limit = 20,
    this.search,
    this.filters,
  });

  Map<String, dynamic> toMap() {
    return {
      'page': page,
      'limit': limit,
      if (search != null) 'search': search,
      if (filters != null) ...filters!,
    };
  }
}

/// Class to represent ID parameters
class IdParams {
  final String id;

  const IdParams({required this.id});
}

/// Class to represent household ID parameters
class HouseholdParams {
  final String householdId;

  const HouseholdParams({required this.householdId});
}

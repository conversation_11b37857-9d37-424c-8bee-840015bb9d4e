import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/reminder.dart';
import '../../../domain/entities/medicine.dart';
import '../../bloc/reminder/reminder_bloc.dart';
import '../../bloc/reminder/reminder_event.dart';
import '../../bloc/reminder/reminder_state.dart';
import '../../bloc/medicine/medicine_bloc.dart';
import '../../../core/utils/supabase_utils.dart';

class ReminderDebugPage extends StatefulWidget {
  const ReminderDebugPage({super.key});

  @override
  State<ReminderDebugPage> createState() => _ReminderDebugPageState();
}

class _ReminderDebugPageState extends State<ReminderDebugPage> {
  List<DoseHistory> _doseHistory = [];
  String _lastAction = '';
  DateTime? _lastActionTime;

  @override
  void initState() {
    super.initState();
    // Load medicines and reminders
    final householdId = SupabaseUtils.getHouseholdId(context);
    if (householdId != null) {
      context
          .read<MedicineBloc>()
          .add(MedicineLoadRequested(householdId: householdId));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      body: Column(
        children: [
          // Header with teal background
          Container(
            padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => context.pop(),
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Debug Rappels',
                    style: AppTextStyles.headlineMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content area
          Expanded(
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
              ),
              child: BlocBuilder<MedicineBloc, MedicineState>(
                builder: (context, medicinesState) {
                  if (medicinesState is MedicineLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (medicinesState is MedicineError) {
                    return Center(
                      child: Text(
                        'Erreur: ${medicinesState.message}',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.error,
                        ),
                      ),
                    );
                  }

                  if (medicinesState is MedicineLoaded) {
                    return SingleChildScrollView(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildActionStatusCard(),
                          const SizedBox(height: 24),
                          _buildMedicinesWithReminders(
                              medicinesState.medicines),
                          const SizedBox(height: 24),
                          _buildDoseHistorySection(),
                        ],
                      ),
                    );
                  }

                  return const Center(
                    child: Text('Aucun médicament trouvé'),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionStatusCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Statut des Actions',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.navy,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (_lastAction.isNotEmpty) ...[
              Text(
                'Dernière action: $_lastAction',
                style: AppTextStyles.bodyMedium,
              ),
              if (_lastActionTime != null) ...[
                const SizedBox(height: 4),
                Text(
                  'Heure: ${_lastActionTime!.toLocal().toString().substring(0, 19)}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.grey600,
                  ),
                ),
              ],
            ] else
              Text(
                'Aucune action effectuée',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.grey600,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildMedicinesWithReminders(List<Medicine> medicines) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Médicaments avec Rappels',
          style: AppTextStyles.titleMedium.copyWith(
            color: AppColors.navy,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        if (medicines.isEmpty)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'Aucun médicament trouvé',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.grey600,
                ),
              ),
            ),
          )
        else
          ...medicines
              .take(5)
              .map((medicine) => _buildMedicineDebugCard(medicine)),
      ],
    );
  }

  Widget _buildMedicineDebugCard(Medicine medicine) {
    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              medicine.name,
              style: AppTextStyles.titleSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            if (medicine.dosage != null) ...[
              const SizedBox(height: 4),
              Text(
                medicine.dosage!,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.grey600,
                ),
              ),
            ],
            const SizedBox(height: 12),

            // Action buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildActionButton(
                  'Pris',
                  AppColors.success,
                  Icons.check,
                  () => _performAction('TAKEN', medicine),
                ),
                _buildActionButton(
                  'Ignoré',
                  AppColors.error,
                  Icons.close,
                  () => _performAction('SKIPPED', medicine),
                ),
                _buildActionButton(
                  'Reporter',
                  AppColors.warning,
                  Icons.snooze,
                  () => _performAction('SNOOZED', medicine),
                ),
                _buildActionButton(
                  'Pause',
                  AppColors.grey600,
                  Icons.pause,
                  () => _pauseReminder(medicine),
                ),
                _buildActionButton(
                  'Archiver',
                  AppColors.grey800,
                  Icons.archive,
                  () => _archiveReminder(medicine),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    String label,
    Color color,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        textStyle: AppTextStyles.bodySmall.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildDoseHistorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Historique des Doses',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.navy,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton.icon(
              onPressed: _refreshDoseHistory,
              icon: const Icon(Icons.refresh, size: 16),
              label: const Text('Actualiser'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_doseHistory.isEmpty)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'Aucun historique de dose',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.grey600,
                ),
              ),
            ),
          )
        else
          ..._doseHistory.take(10).map((dose) => _buildDoseHistoryItem(dose)),
      ],
    );
  }

  Widget _buildDoseHistoryItem(DoseHistory dose) {
    Color statusColor;
    IconData statusIcon;

    switch (dose.status) {
      case 'TAKEN':
        statusColor = AppColors.success;
        statusIcon = Icons.check_circle;
        break;
      case 'SKIPPED':
        statusColor = AppColors.error;
        statusIcon = Icons.cancel;
        break;
      case 'SNOOZED':
        statusColor = AppColors.warning;
        statusIcon = Icons.snooze;
        break;
      default:
        statusColor = AppColors.grey600;
        statusIcon = Icons.help;
    }

    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(statusIcon, color: statusColor),
        title: Text(
          dose.status,
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          'Programmé: ${dose.scheduledAt.toLocal().toString().substring(0, 16)}\n'
          'Action: ${dose.actionAt?.toLocal().toString().substring(0, 16) ?? 'N/A'}',
          style: AppTextStyles.bodySmall,
        ),
        trailing: Text(
          'ID: ${dose.userMedicineId.substring(0, 8)}...',
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.grey600,
          ),
        ),
      ),
    );
  }

  void _performAction(String action, Medicine medicine) {
    // Get current user ID for RLS policy compliance
    final userId = SupabaseUtils.getUserId(context);
    if (userId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Erreur: Utilisateur non authentifié'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final doseHistory = DoseHistory(
      userId: userId, // Required for RLS policies
      userMedicineId: medicine.id,
      reminderId: null, // For debug purposes, we don't need a specific reminder
      scheduledAt: DateTime.now(),
      actionAt: DateTime.now(),
      status: action,
    );

    context.read<ReminderBloc>().add(AddDoseHistory(doseHistory));

    setState(() {
      _lastAction = action;
      _lastActionTime = DateTime.now();
      _doseHistory.insert(0, doseHistory);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text('Action "$action" effectuée pour ${medicine.name}'),
        backgroundColor: _getActionColor(action),
      ),
    );
  }

  void _pauseReminder(Medicine medicine) {
    // Find the active reminder for this medicine
    final reminderState = context.read<ReminderBloc>().state;
    if (reminderState is RemindersLoaded) {
      final activeReminder = reminderState.reminders
          .where((r) => r.userMedicineId == medicine.id && r.isCurrentlyActive)
          .firstOrNull;

      if (activeReminder?.id != null) {
        // Pause the reminder using BLoC
        context.read<ReminderBloc>().add(PauseReminder(activeReminder!.id!));

        setState(() {
          _lastAction = 'PAUSE';
          _lastActionTime = DateTime.now();
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Aucun rappel actif trouvé pour ${medicine.name}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _archiveReminder(Medicine medicine) {
    // Find the active reminder for this medicine
    final reminderState = context.read<ReminderBloc>().state;
    if (reminderState is RemindersLoaded) {
      final activeReminder = reminderState.reminders
          .where((r) => r.userMedicineId == medicine.id && r.isCurrentlyActive)
          .firstOrNull;

      if (activeReminder?.id != null) {
        // Archive the reminder using BLoC
        context.read<ReminderBloc>().add(ArchiveReminder(activeReminder!.id!));

        setState(() {
          _lastAction = 'ARCHIVE';
          _lastActionTime = DateTime.now();
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Aucun rappel actif trouvé pour ${medicine.name}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _refreshDoseHistory() {
    // For debug purposes, just show a message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Historique actualisé'),
      ),
    );
  }

  Color _getActionColor(String action) {
    switch (action) {
      case 'TAKEN':
        return AppColors.success;
      case 'SKIPPED':
        return AppColors.error;
      case 'SNOOZED':
        return AppColors.warning;
      default:
        return AppColors.grey600;
    }
  }
}

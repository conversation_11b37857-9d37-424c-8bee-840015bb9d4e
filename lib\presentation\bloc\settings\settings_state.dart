import 'package:equatable/equatable.dart';
import '../../../domain/entities/settings.dart';

/// Base class for settings states
abstract class SettingsState extends Equatable {
  const SettingsState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class SettingsInitial extends SettingsState {
  const SettingsInitial();
}

/// Loading state
class SettingsLoading extends SettingsState {
  const SettingsLoading();
}

/// Loaded state with settings data
class SettingsLoaded extends SettingsState {
  final Settings settings;
  final bool isBiometricAvailable;

  const SettingsLoaded({
    required this.settings,
    this.isBiometricAvailable = false,
  });

  @override
  List<Object?> get props => [settings, isBiometricAvailable];

  /// Copy with method for state updates
  SettingsLoaded copyWith({
    Settings? settings,
    bool? isBiometricAvailable,
  }) {
    return SettingsLoaded(
      settings: settings ?? this.settings,
      isBiometricAvailable: isBiometricAvailable ?? this.isBiometricAvailable,
    );
  }
}

/// Error state
class SettingsError extends SettingsState {
  final String message;
  final Settings? settings;

  const SettingsError({
    required this.message,
    this.settings,
  });

  @override
  List<Object?> get props => [message, settings];
}

/// Success state for operations like export/import
class SettingsOperationSuccess extends SettingsState {
  final String message;
  final Settings settings;
  final String? data; // For export operations

  const SettingsOperationSuccess({
    required this.message,
    required this.settings,
    this.data,
  });

  @override
  List<Object?> get props => [message, settings, data];
}

/// State for updating specific settings sections
class SettingsUpdating extends SettingsState {
  final Settings settings;
  final String section; // 'notifications', 'app', 'security', 'personalization'

  const SettingsUpdating({
    required this.settings,
    required this.section,
  });

  @override
  List<Object?> get props => [settings, section];
}

/// State for validation results
class SettingsValidationResult extends SettingsState {
  final bool isValid;
  final String? message;
  final Settings settings;

  const SettingsValidationResult({
    required this.isValid,
    this.message,
    required this.settings,
  });

  @override
  List<Object?> get props => [isValid, message, settings];
}

/// State for biometric availability check
class BiometricAvailabilityChecked extends SettingsState {
  final bool isAvailable;
  final Settings settings;

  const BiometricAvailabilityChecked({
    required this.isAvailable,
    required this.settings,
  });

  @override
  List<Object?> get props => [isAvailable, settings];
}

/// State for PIN validation
class PinValidationResult extends SettingsState {
  final bool isValid;
  final Settings settings;

  const PinValidationResult({
    required this.isValid,
    required this.settings,
  });

  @override
  List<Object?> get props => [isValid, settings];
}

/// State for settings reset
class SettingsResetSuccess extends SettingsState {
  final Settings settings;

  const SettingsResetSuccess({required this.settings});

  @override
  List<Object?> get props => [settings];
}

/// State for export success
class SettingsExportSuccess extends SettingsState {
  final String jsonData;
  final Settings settings;

  const SettingsExportSuccess({
    required this.jsonData,
    required this.settings,
  });

  @override
  List<Object?> get props => [jsonData, settings];
}

/// State for import success
class SettingsImportSuccess extends SettingsState {
  final Settings settings;

  const SettingsImportSuccess({required this.settings});

  @override
  List<Object?> get props => [settings];
}

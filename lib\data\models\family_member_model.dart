import '../../domain/entities/family_member.dart';

/// Data model for FamilyMember entity
class FamilyMemberModel extends FamilyMember {
  const FamilyMemberModel({
    required super.id,
    required super.householdId,
    required super.name,
    super.relationship,
    super.dateOfBirth,
    super.gender,
    super.notes,
    super.avatarUrl,
    required super.createdAt,
    super.updatedAt,
  });

  /// Create from JSON
  factory FamilyMemberModel.fromJson(Map<String, dynamic> json) {
    try {
      // Handle actual database fields
      final id = json['id'] as String? ?? '';
      final safeCreatedAt = json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : DateTime.now();

      // Use 'relation' field primarily (where actual data is stored), fallback to 'role' for backward compatibility
      // Also handle cases where the data might already be in French
      final relationValue = json['relation'] as String?;
      final roleValue = json['role'] as String?;

      String? relationship;
      if (relationValue != null && relationValue.isNotEmpty) {
        // Try to map from English to French, but if it's already French, keep it
        final mapped = FamilyMemberModel._mapEnglishToFrench(relationValue);
        relationship = mapped;
      } else if (roleValue != null && roleValue.isNotEmpty) {
        // Fallback to role field
        final mapped = FamilyMemberModel._mapEnglishToFrench(roleValue);
        relationship = mapped;
      } else {
        // Default if both are null/empty
        relationship = 'autre';
      }

      return FamilyMemberModel(
        id: id,
        householdId: json['household_id'] as String? ?? '',
        name: json['name'] as String? ?? '',
        relationship: relationship,
        dateOfBirth: json['birth_date'] != null
            ? DateTime.parse(json['birth_date'] as String)
            : null,
        gender: null, // Not in database schema
        notes: null, // Not in database schema
        avatarUrl: json['avatar_url'] as String?, // Database has this field
        createdAt: safeCreatedAt,
        updatedAt: null, // Not in database schema
      );
    } catch (e) {
      throw Exception(
          'Failed to parse FamilyMember from JSON: $e. JSON: $json');
    }
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    // Only include fields that actually exist in the database schema
    return {
      'household_id': householdId,
      'name': name,
      'avatar_url': avatarUrl, // Database has 'avatar_url' field
      'birth_date':
          dateOfBirth?.toIso8601String(), // Database has 'birth_date' field
      'relation': FamilyMemberModel._mapRelationshipToEnglish(
          relationship), // Map French to English for database
      // 'role' field remains null for future use
    };
  }

  /// Map French relationship terms to English for database storage
  static String? _mapRelationshipToEnglish(String? frenchRelation) {
    if (frenchRelation == null) return null;

    switch (frenchRelation.toLowerCase()) {
      case 'moi':
        return 'self';
      case 'parent':
        return 'parent';
      case 'père':
        return 'father';
      case 'mère':
        return 'mother';
      case 'enfant':
        return 'child';
      case 'fils':
        return 'son';
      case 'fille':
        return 'daughter';
      case 'conjoint':
        return 'spouse';
      case 'époux':
        return 'husband';
      case 'épouse':
        return 'wife';
      case 'frère':
        return 'brother';
      case 'sœur':
        return 'sister';
      case 'grand-parent':
        return 'grandparent';
      case 'grand-père':
        return 'grandfather';
      case 'grand-mère':
        return 'grandmother';
      case 'petit-enfant':
        return 'grandchild';
      case 'petit-fils':
        return 'grandson';
      case 'petite-fille':
        return 'granddaughter';
      case 'autre':
        return 'other';
      default:
        return frenchRelation; // Return as-is if no mapping found
    }
  }

  /// Map English relationship terms from database back to French for UI
  static String? _mapEnglishToFrench(String? relation) {
    if (relation == null || relation.isEmpty) {
      return 'autre'; // Default to 'autre' if no relation is set
    }

    final lowerRelation = relation.toLowerCase();

    // Check if it's already in French (for backward compatibility)
    if (_isFrenchRelationship(lowerRelation)) {
      return lowerRelation;
    }

    // Map from English to French
    switch (lowerRelation) {
      case 'self':
        return 'moi';
      case 'parent':
        return 'parent';
      case 'father':
        return 'père';
      case 'mother':
        return 'mère';
      case 'child':
        return 'enfant';
      case 'son':
        return 'fils';
      case 'daughter':
        return 'fille';
      case 'spouse':
        return 'conjoint';
      case 'husband':
        return 'époux';
      case 'wife':
        return 'épouse';
      case 'brother':
        return 'frère';
      case 'sister':
        return 'sœur';
      case 'grandparent':
        return 'grand-parent';
      case 'grandfather':
        return 'grand-père';
      case 'grandmother':
        return 'grand-mère';
      case 'grandchild':
        return 'petit-enfant';
      case 'grandson':
        return 'petit-fils';
      case 'granddaughter':
        return 'petite-fille';
      case 'other':
        return 'autre';
      default:
        return relation; // Return as-is if no mapping found
    }
  }

  /// Check if a relationship term is already in French
  static bool _isFrenchRelationship(String relation) {
    const frenchRelationships = {
      'moi',
      'parent',
      'père',
      'mère',
      'enfant',
      'fils',
      'fille',
      'conjoint',
      'époux',
      'épouse',
      'frère',
      'sœur',
      'grand-parent',
      'grand-père',
      'grand-mère',
      'petit-enfant',
      'petit-fils',
      'petite-fille',
      'autre'
    };
    return frenchRelationships.contains(relation.toLowerCase());
  }

  /// Create from entity
  factory FamilyMemberModel.fromEntity(FamilyMember entity) {
    return FamilyMemberModel(
      id: entity.id,
      householdId: entity.householdId,
      name: entity.name,
      relationship: entity.relationship,
      dateOfBirth: entity.dateOfBirth,
      gender: entity.gender,
      notes: entity.notes,
      avatarUrl: entity.avatarUrl,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  /// Convert to entity
  FamilyMember toEntity() {
    return FamilyMember(
      id: id,
      householdId: householdId,
      name: name,
      relationship: relationship,
      dateOfBirth: dateOfBirth,
      gender: gender,
      notes: notes,
      avatarUrl: avatarUrl,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  /// Copy with method
  @override
  FamilyMemberModel copyWith({
    String? id,
    String? householdId,
    String? name,
    String? relationship,
    DateTime? dateOfBirth,
    String? gender,
    String? notes,
    String? avatarUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FamilyMemberModel(
      id: id ?? this.id,
      householdId: householdId ?? this.householdId,
      name: name ?? this.name,
      relationship: relationship ?? this.relationship,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      notes: notes ?? this.notes,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

import '../entities/family_member.dart';

/// Repository interface for family member management
abstract class FamilyMemberRepository {
  /// Get all family members for a household
  /// 
  /// [householdId] - Household ID
  /// 
  /// Returns a Stream of family member lists that updates in real-time
  Stream<List<FamilyMember>> getHouseholdMembers(String householdId);
  
  /// Get a family member by ID
  /// 
  /// [memberId] - Family member ID
  /// 
  /// Returns a Future with the FamilyMember entity if found
  Future<FamilyMember?> getFamilyMemberById(String memberId);
  
  /// Create a new family member
  /// 
  /// [member] - FamilyMember entity to create
  /// 
  /// Returns a Future with the created family member
  Future<FamilyMember> createFamilyMember(FamilyMember member);
  
  /// Update an existing family member
  /// 
  /// [member] - FamilyMember entity with updated information
  /// 
  /// Returns a Future with the updated family member
  Future<FamilyMember> updateFamilyMember(FamilyMember member);
  
  /// Delete a family member
  /// 
  /// [memberId] - Family member ID to delete
  /// 
  /// Returns a Future that completes when the family member is deleted
  Future<void> deleteFamilyMember(String memberId);
}

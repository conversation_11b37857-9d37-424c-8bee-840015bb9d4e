import 'package:dartz/dartz.dart';
import '../../repositories/profile_repository.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';

class DeleteAccountParams {
  final String password;

  DeleteAccountParams({required this.password});
}

class DeleteAccountUseCase implements UseCase<void, DeleteAccountParams> {
  final ProfileRepository repository;

  DeleteAccountUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(DeleteAccountParams params) async {
    return await repository.deleteAccount(params.password);
  }
}

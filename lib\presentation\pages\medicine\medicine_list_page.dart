import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/supabase_utils.dart';
import '../../../domain/entities/medicine.dart';

import '../../bloc/medicine/medicine_bloc.dart';

import '../../widgets/medicine/enhanced_medicine_list_card.dart';

class MedicineListPage extends StatefulWidget {
  const MedicineListPage({super.key});

  @override
  State<MedicineListPage> createState() => _MedicineListPageState();
}

class _MedicineListPageState extends State<MedicineListPage> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _loadMedicines();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadMedicines() {
    final householdId = SupabaseUtils.getHouseholdId(context);
    if (householdId != null) {
      context
          .read<MedicineBloc>()
          .add(MedicineLoadRequested(householdId: householdId));
    } else {
      // Handle case where household ID is not available
      debugPrint('Cannot load medicines: household ID not available');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      body: Column(
        children: [
          // Header with teal background
          Container(
            padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => context.pop(),
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Mes Médicaments',
                    style: AppTextStyles.headlineMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content container with white background
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Search and filter section
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Search bar
                        TextField(
                          controller: _searchController,
                          decoration: InputDecoration(
                            hintText: 'Rechercher un médicament...',
                            prefixIcon: const Icon(Icons.search),
                            suffixIcon: _searchQuery.isNotEmpty
                                ? IconButton(
                                    icon: const Icon(Icons.clear),
                                    onPressed: () {
                                      _searchController.clear();
                                      setState(() {
                                        _searchQuery = '';
                                      });
                                    },
                                  )
                                : null,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: AppColors.grey300),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: AppColors.grey300),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide:
                                  BorderSide(color: AppColors.teal, width: 2),
                            ),
                          ),
                          onChanged: (value) {
                            setState(() {
                              _searchQuery = value;
                            });
                          },
                        ),
                        const SizedBox(height: 12),

                        // Filter chips
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: [
                              _buildFilterChip('all', 'Tous', Icons.medication),
                              const SizedBox(width: 8),
                              _buildFilterChip(
                                  'expiring', 'Expire bientôt', Icons.warning),
                              const SizedBox(width: 8),
                              _buildFilterChip(
                                  'expired', 'Expirés', Icons.error),
                              const SizedBox(width: 8),
                              _buildFilterChip('low_stock', 'Stock faible',
                                  Icons.inventory_2),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Medicine list
                  Expanded(
                    child: BlocBuilder<MedicineBloc, MedicineState>(
                      builder: (context, state) {
                        if (state is MedicineLoading) {
                          return const Center(
                              child: CircularProgressIndicator());
                        }

                        if (state is MedicineError) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  size: 64,
                                  color: AppColors.error,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Erreur lors du chargement',
                                  style: AppTextStyles.titleMedium,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  state.message,
                                  style: AppTextStyles.bodyMedium.copyWith(
                                    color: AppColors.grey600,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: _loadMedicines,
                                  child: const Text('Réessayer'),
                                ),
                              ],
                            ),
                          );
                        }

                        if (state is MedicineLoaded) {
                          final filteredMedicines =
                              _filterMedicines(state.medicines);

                          if (filteredMedicines.isEmpty) {
                            return _buildEmptyState();
                          }

                          return RefreshIndicator(
                            onRefresh: () async => _loadMedicines(),
                            child: ListView.builder(
                              padding: const EdgeInsets.all(16),
                              itemCount: filteredMedicines.length,
                              itemBuilder: (context, index) {
                                final medicine = filteredMedicines[index];
                                return EnhancedMedicineListCard(
                                  medicine: medicine,
                                  onTap: () => _showMedicineDetails(medicine),
                                  onEdit: () => _editMedicine(medicine),
                                  onDelete: () => _deleteMedicine(medicine),
                                );
                              },
                            ),
                          );
                        }

                        return _buildEmptyState();
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label, IconData icon) {
    final isSelected = _selectedFilter == value;
    return FilterChip(
      selected: isSelected,
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: isSelected ? Colors.white : AppColors.grey600,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: isSelected ? Colors.white : AppColors.grey600,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
      onSelected: (selected) {
        setState(() {
          _selectedFilter = value;
        });
      },
      backgroundColor: AppColors.grey100,
      selectedColor: AppColors.teal,
      checkmarkColor: Colors.white,
      side: BorderSide(
        color: isSelected ? AppColors.teal : AppColors.grey300,
        width: isSelected ? 2 : 1,
      ),
    );
  }

  List<Medicine> _filterMedicines(List<Medicine> medicines) {
    var filtered = medicines;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((medicine) {
        final searchLower = _searchQuery.toLowerCase();
        return medicine.name.toLowerCase().contains(searchLower) ||
            (medicine.dosage?.toLowerCase().contains(searchLower) ?? false) ||
            (medicine.form?.toLowerCase().contains(searchLower) ?? false);
      }).toList();
    }

    // Apply category filter
    switch (_selectedFilter) {
      case 'expiring':
        final thirtyDaysFromNow = DateTime.now().add(const Duration(days: 30));
        filtered = filtered.where((medicine) {
          return medicine.expiration != null &&
              medicine.expiration!.isAfter(DateTime.now()) &&
              medicine.expiration!.isBefore(thirtyDaysFromNow);
        }).toList();
        break;
      case 'expired':
        filtered = filtered.where((medicine) {
          return medicine.expiration != null &&
              medicine.expiration!.isBefore(DateTime.now());
        }).toList();
        break;
      case 'low_stock':
        filtered = filtered.where((medicine) {
          return medicine.quantity <= medicine.lowStockThreshold;
        }).toList();
        break;
    }

    return filtered;
  }

  Widget _buildEmptyState() {
    String title;
    String subtitle;
    IconData icon;

    switch (_selectedFilter) {
      case 'expiring':
        title = 'Aucun médicament expire bientôt';
        subtitle = 'Tous vos médicaments ont une date d\'expiration éloignée';
        icon = Icons.check_circle_outline;
        break;
      case 'expired':
        title = 'Aucun médicament expiré';
        subtitle = 'Parfait ! Tous vos médicaments sont encore valides';
        icon = Icons.check_circle_outline;
        break;
      case 'low_stock':
        title = 'Stock suffisant';
        subtitle = 'Tous vos médicaments ont un stock suffisant';
        icon = Icons.check_circle_outline;
        break;
      default:
        title = 'Aucun médicament';
        subtitle = 'Commencez par ajouter vos premiers médicaments';
        icon = Icons.medication_outlined;
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: AppColors.grey400,
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: AppTextStyles.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey600,
              ),
              textAlign: TextAlign.center,
            ),
            if (_selectedFilter == 'all') ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () => context.push('/add-medicine'),
                icon: const Icon(Icons.add),
                label: const Text('Ajouter un médicament'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.teal,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showMedicineDetails(Medicine medicine) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _MedicineDetailsSheet(medicine: medicine),
    );
  }

  void _editMedicine(Medicine medicine) {
    context.push('/edit-medicine/${medicine.id}');
  }

  void _deleteMedicine(Medicine medicine) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer le médicament'),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer "${medicine.name}" ?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              final householdId = SupabaseUtils.getHouseholdId(context);
              if (householdId != null) {
                context.read<MedicineBloc>().add(MedicineDeleteRequested(
                      medicineId: medicine.id,
                      householdId: householdId,
                    ));
              } else {
                // Handle case where household ID is not available
                debugPrint(
                    'Cannot delete medicine: household ID not available');
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  /// Gradient matching the white-blue background style
  LinearGradient _getHeaderGradient() {
    return const LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        Color(0xFFE3F0FF), // Light pastel blue
        Color(0xFFFFFFFF), // White bottom blend
      ],
    );
  }

  /// Integrated header card with title (no back button for main medicine list page)
  Widget _buildIntegratedHeaderCard() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Text(
        'Mes médicaments',
        style: AppTextStyles.titleLarge.copyWith(
          color: AppColors.navy,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

class _MedicineDetailsSheet extends StatelessWidget {
  final Medicine medicine;

  const _MedicineDetailsSheet({required this.medicine});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.grey300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  medicine.name,
                  style: AppTextStyles.titleLarge,
                ),
                if (medicine.dosage != null || medicine.form != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    [medicine.dosage, medicine.form]
                        .where((e) => e != null && e.isNotEmpty)
                        .join(' - '),
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                ],
                const SizedBox(height: 24),

                // Details grid
                _buildDetailRow('Quantité', '${medicine.quantity}'),
                if (medicine.expiration != null)
                  _buildDetailRow(
                    'Expiration',
                    DateFormat('dd/MM/yyyy').format(medicine.expiration!),
                  ),
                if (medicine.locationName != null)
                  _buildDetailRow('Emplacement', medicine.locationName!),
                if (medicine.familyMemberName != null)
                  _buildDetailRow('Membre', medicine.familyMemberName!),
                if (medicine.tags.isNotEmpty)
                  _buildTagsRow('Étiquettes', medicine.tags),
                if (medicine.notes != null && medicine.notes!.isNotEmpty)
                  _buildDetailRow('Notes', medicine.notes!),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.grey700,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTagsRow(String label, List<String> tags) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.grey700,
              ),
            ),
          ),
          Expanded(
            child: Wrap(
              spacing: 6,
              runSpacing: 6,
              children: tags.map((tag) {
                return Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.teal.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                        color: AppColors.teal.withValues(alpha: 0.3)),
                  ),
                  child: Text(
                    tag,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.teal,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}

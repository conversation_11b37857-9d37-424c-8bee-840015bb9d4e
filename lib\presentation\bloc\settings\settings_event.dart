import 'package:equatable/equatable.dart';
import '../../../domain/entities/settings.dart';

/// Base class for settings events
abstract class SettingsEvent extends Equatable {
  const SettingsEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load user settings
class SettingsLoadRequested extends SettingsEvent {
  final String userId;

  const SettingsLoadRequested({required this.userId});

  @override
  List<Object?> get props => [userId];
}

/// Event to update user settings
class SettingsUpdateRequested extends SettingsEvent {
  final Settings settings;

  const SettingsUpdateRequested({required this.settings});

  @override
  List<Object?> get props => [settings];
}

/// Event to reset settings to defaults
class SettingsResetRequested extends SettingsEvent {
  final String userId;
  final String householdId;

  const SettingsResetRequested({
    required this.userId,
    required this.householdId,
  });

  @override
  List<Object?> get props => [userId, householdId];
}

/// Event to export settings
class SettingsExportRequested extends SettingsEvent {
  final String userId;

  const SettingsExportRequested({required this.userId});

  @override
  List<Object?> get props => [userId];
}

/// Event to import settings
class SettingsImportRequested extends SettingsEvent {
  final String userId;
  final String jsonData;

  const SettingsImportRequested({
    required this.userId,
    required this.jsonData,
  });

  @override
  List<Object?> get props => [userId, jsonData];
}

/// Event to update notification settings
class NotificationSettingsUpdateRequested extends SettingsEvent {
  final String userId;
  final NotificationSettings settings;

  const NotificationSettingsUpdateRequested({
    required this.userId,
    required this.settings,
  });

  @override
  List<Object?> get props => [userId, settings];
}

/// Event to update app settings
class AppSettingsUpdateRequested extends SettingsEvent {
  final String userId;
  final AppSettings settings;

  const AppSettingsUpdateRequested({
    required this.userId,
    required this.settings,
  });

  @override
  List<Object?> get props => [userId, settings];
}

/// Event to update security settings
class SecuritySettingsUpdateRequested extends SettingsEvent {
  final String userId;
  final SecuritySettings settings;

  const SecuritySettingsUpdateRequested({
    required this.userId,
    required this.settings,
  });

  @override
  List<Object?> get props => [userId, settings];
}

/// Event to update personalization settings
class PersonalizationSettingsUpdateRequested extends SettingsEvent {
  final String userId;
  final PersonalizationSettings settings;

  const PersonalizationSettingsUpdateRequested({
    required this.userId,
    required this.settings,
  });

  @override
  List<Object?> get props => [userId, settings];
}

/// Event to check biometric availability
class BiometricAvailabilityCheckRequested extends SettingsEvent {
  const BiometricAvailabilityCheckRequested();
}

/// Event to validate PIN code
class PinCodeValidationRequested extends SettingsEvent {
  final String pinCode;

  const PinCodeValidationRequested({required this.pinCode});

  @override
  List<Object?> get props => [pinCode];
}

/// Event to update expiry warning threshold
class ExpiryWarningThresholdUpdateRequested extends SettingsEvent {
  final String userId;
  final int days;

  const ExpiryWarningThresholdUpdateRequested({
    required this.userId,
    required this.days,
  });

  @override
  List<Object?> get props => [userId, days];
}

/// Event to toggle a specific setting
class SettingToggleRequested extends SettingsEvent {
  final String settingKey;
  final bool value;

  const SettingToggleRequested({
    required this.settingKey,
    required this.value,
  });

  @override
  List<Object?> get props => [settingKey, value];
}

/// Event to update language preference
class LanguageUpdateRequested extends SettingsEvent {
  final String userId;
  final String language;

  const LanguageUpdateRequested({
    required this.userId,
    required this.language,
  });

  @override
  List<Object?> get props => [userId, language];
}

/// Event to update theme preference
class ThemeUpdateRequested extends SettingsEvent {
  final String userId;
  final bool isDarkMode;

  const ThemeUpdateRequested({
    required this.userId,
    required this.isDarkMode,
  });

  @override
  List<Object?> get props => [userId, isDarkMode];
}

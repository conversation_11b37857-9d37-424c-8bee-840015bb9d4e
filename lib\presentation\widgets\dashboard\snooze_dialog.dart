import 'package:flutter/material.dart';
import '../../../core/services/enhanced_reminder_notification_service.dart';
import '../../../core/theme/app_colors.dart';

/// Dialog for selecting snooze duration for medicine reminders
class SnoozeDialog extends StatelessWidget {
  final String medicineName;
  final Function(Duration) onSnoozeSelected;

  const SnoozeDialog({
    super.key,
    required this.medicineName,
    required this.onSnoozeSelected,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          Icon(
            Icons.snooze,
            color: AppColors.warning,
            size: 24,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Reporter le rappel',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Choisissez la durée pour reporter le rappel de :',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 8),
          Text(
            medicineName,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.primary,
                ),
          ),
          const SizedBox(height: 16),
          ...EnhancedReminderNotificationService.availableSnoozeDurations
              .asMap()
              .entries
              .map((entry) {
            final index = entry.key;
            final duration = entry.value;
            final label =
                EnhancedReminderNotificationService.snoozeDurationLabels[index];

            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.of(context).pop();
                    onSnoozeSelected(duration);
                  },
                  icon: Icon(
                    _getIconForDuration(duration),
                    size: 20,
                  ),
                  label: Text(
                    label,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.warning.withValues(alpha: 0.1),
                    foregroundColor: AppColors.warning,
                    elevation: 0,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                      side: BorderSide(
                        color: AppColors.warning.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'Annuler',
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  IconData _getIconForDuration(Duration duration) {
    if (duration.inMinutes <= 5) {
      return Icons.timer;
    } else if (duration.inMinutes <= 15) {
      return Icons.timer_10;
    } else if (duration.inMinutes <= 30) {
      return Icons.timer_3;
    } else {
      return Icons.schedule;
    }
  }
}

/// Helper function to show snooze dialog
Future<void> showSnoozeDialog({
  required BuildContext context,
  required String medicineName,
  required Function(Duration) onSnoozeSelected,
}) async {
  return showDialog<void>(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext context) {
      return SnoozeDialog(
        medicineName: medicineName,
        onSnoozeSelected: onSnoozeSelected,
      );
    },
  );
}

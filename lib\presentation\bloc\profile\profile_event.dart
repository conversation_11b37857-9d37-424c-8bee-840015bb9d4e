import 'package:equatable/equatable.dart';
import 'dart:io';

/// Profile events for managing user profile operations
abstract class Profile<PERSON>vent extends Equatable {
  const ProfileEvent();

  @override
  List<Object?> get props => [];
}

/// Load user profile data
class ProfileLoadRequested extends ProfileEvent {}

/// Update user name
class ProfileNameUpdateRequested extends ProfileEvent {
  final String name;

  const ProfileNameUpdateRequested({required this.name});

  @override
  List<Object?> get props => [name];
}

/// Update user email
class ProfileEmailUpdateRequested extends ProfileEvent {
  final String email;

  const ProfileEmailUpdateRequested({required this.email});

  @override
  List<Object?> get props => [email];
}

/// Update user avatar
class ProfileAvatarUpdateRequested extends ProfileEvent {
  final File imageFile;

  const ProfileAvatarUpdateRequested({required this.imageFile});

  @override
  List<Object?> get props => [imageFile];
}

/// Remove user avatar
class ProfileAvatarRemoveRequested extends ProfileEvent {}

/// Change user password
class ProfilePasswordChangeRequested extends ProfileEvent {
  final String currentPassword;
  final String newPassword;

  const ProfilePasswordChangeRequested({
    required this.currentPassword,
    required this.newPassword,
  });

  @override
  List<Object?> get props => [currentPassword, newPassword];
}

/// Update language preference
class ProfileLanguageUpdateRequested extends ProfileEvent {
  final String language;

  const ProfileLanguageUpdateRequested({required this.language});

  @override
  List<Object?> get props => [language];
}

/// Update theme preference
class ProfileThemeUpdateRequested extends ProfileEvent {
  final bool isDarkMode;

  const ProfileThemeUpdateRequested({required this.isDarkMode});

  @override
  List<Object?> get props => [isDarkMode];
}

/// Delete user account
class ProfileAccountDeleteRequested extends ProfileEvent {
  final String password;

  const ProfileAccountDeleteRequested({required this.password});

  @override
  List<Object?> get props => [password];
}

/// Reset profile state
class ProfileResetRequested extends ProfileEvent {}

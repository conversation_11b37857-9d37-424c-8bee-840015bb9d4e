import 'package:dartz/dartz.dart';
import '../../repositories/profile_repository.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';

class UpdateEmailParams {
  final String email;

  UpdateEmailParams({required this.email});
}

class UpdateEmailUseCase implements UseCase<void, UpdateEmailParams> {
  final ProfileRepository repository;

  UpdateEmailUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(UpdateEmailParams params) async {
    return await repository.updateEmail(params.email);
  }
}

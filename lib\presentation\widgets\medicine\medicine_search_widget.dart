import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/debug_logger.dart';
import '../../../domain/entities/tunisia_medicine.dart';
import '../../bloc/add_medicine/add_medicine_bloc.dart';
import '../../bloc/add_medicine/add_medicine_event.dart';
import '../../bloc/add_medicine/add_medicine_state.dart';

/// Widget for searching and selecting medicines from tunisia_medicines table
class MedicineSearchWidget extends StatefulWidget {
  const MedicineSearchWidget({super.key});

  @override
  State<MedicineSearchWidget> createState() => _MedicineSearchWidgetState();
}

class _MedicineSearchWidgetState extends State<MedicineSearchWidget> {
  final _searchController = TextEditingController();
  final _focusNode = FocusNode();
  Timer? _debounceTimer;

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AddMedicineBloc, AddMedicineState>(
      builder: (context, state) {
        if (state is! AddMedicineFormState) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search input field
            TextFormField(
              controller: _searchController,
              focusNode: _focusNode,
              decoration: InputDecoration(
                labelText: 'Rechercher un médicament',
                hintText: 'Tapez le nom du médicament...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: state.isSearching
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: Padding(
                          padding: EdgeInsets.all(12),
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      )
                    : _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              context
                                  .read<AddMedicineBloc>()
                                  .add(const SearchCleared());
                            },
                          )
                        : null,
              ),
              onChanged: (value) {
                // Cancel previous timer
                _debounceTimer?.cancel();

                if (value.trim().isEmpty) {
                  context.read<AddMedicineBloc>().add(const SearchCleared());
                  return;
                }

                // Set up debounce timer
                _debounceTimer = Timer(const Duration(milliseconds: 300), () {
                  if (mounted) {
                    context
                        .read<AddMedicineBloc>()
                        .add(MedicinesSearched(query: value));
                  }
                });
              },
              validator: (value) {
                if (!state.isCustomMode && state.selectedMedicine == null) {
                  return 'Veuillez sélectionner un médicament ou passer en mode personnalisé';
                }
                return null;
              },
            ),

            const SizedBox(height: 8),

            // Search results
            if (state.searchResults.isNotEmpty) ...[
              const SizedBox(height: 8),
              Container(
                constraints: const BoxConstraints(maxHeight: 300),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: AppColors.grey300),
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ListView.separated(
                  shrinkWrap: true,
                  itemCount: state.searchResults.length,
                  separatorBuilder: (context, index) =>
                      const Divider(height: 1),
                  itemBuilder: (context, index) {
                    final medicine = state.searchResults[index];
                    return _buildSearchResultItem(context, medicine);
                  },
                ),
              ),
            ] else if (state.isSearching) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: AppColors.grey300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Recherche en cours...',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.grey600,
                      ),
                    ),
                  ],
                ),
              ),
            ] else if (_searchController.text.isNotEmpty &&
                state.searchResults.isEmpty &&
                !state.isSearching &&
                state.selectedMedicine == null &&
                state.selectedMedicineName == null) ...[
              // Debug: Log the condition check
              Builder(builder: (context) {
                DebugLogger.logUI(
                    'CustomMedicineButton', 'Button condition check',
                    data: {
                      'searchText': _searchController.text,
                      'searchTextEmpty': _searchController.text.isEmpty,
                      'searchResultsCount': state.searchResults.length,
                      'isSearching': state.isSearching,
                      'shouldShowButton': _searchController.text.isNotEmpty &&
                          state.searchResults.isEmpty &&
                          !state.isSearching,
                    });
                return const SizedBox.shrink();
              }),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.grey50,
                  border: Border.all(color: AppColors.grey300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.search_off,
                          color: AppColors.grey500,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Aucun médicament trouvé',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.grey600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    // Dedicated "Add Custom Medicine" button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          final searchText = _searchController.text;
                          final bloc = context.read<AddMedicineBloc>();

                          DebugLogger.logUI('CustomMedicineButton',
                              'Switching to custom mode',
                              data: {
                                'searchText': searchText,
                                'searchTextLength': searchText.length,
                              });

                          // Clear search first
                          _searchController.clear();
                          bloc.add(const SearchCleared());

                          // Switch to custom mode with customName directly in the event
                          bloc.add(ModeToggled(
                            isCustomMode: true,
                            customName:
                                searchText.isNotEmpty ? searchText : null,
                          ));

                          DebugLogger.logUI('CustomMedicineButton',
                              'Mode toggled with custom name',
                              data: {
                                'customName': searchText,
                                'isCustomMode': true,
                              });
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.teal,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        icon: const Icon(Icons.add_circle_outline, size: 20),
                        label: Text(
                          'Ajouter un médicament personnalisé',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // Medicine name selected confirmation
            if (state.selectedMedicineName != null &&
                state.availableDosageForms.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildMedicineNameConfirmation(
                  context, state.selectedMedicineName!),
            ],

            // Selected medicine display
            if (state.selectedMedicine != null) ...[
              const SizedBox(height: 16),
              _buildSelectedMedicineCard(context, state.selectedMedicine!),
            ],

            // Show custom mode status when active
            if (state.isCustomMode) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.teal.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border:
                      Border.all(color: AppColors.teal.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      color: AppColors.teal,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Mode personnalisé activé - Remplissez les détails ci-dessous',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.teal,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  Widget _buildSearchResultItem(
      BuildContext context, TunisiaMedicine medicine) {
    return ListTile(
      title: Text(
        medicine.nom,
        style: AppTextStyles.titleSmall,
      ),
      subtitle: medicine.laboratoire != null
          ? Text(
              'Laboratoire: ${medicine.laboratoire}',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.grey600,
              ),
            )
          : null,
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        _searchController.text = medicine.nom;
        _focusNode.unfocus();
        context
            .read<AddMedicineBloc>()
            .add(MedicineNameSelected(medicineName: medicine.nom));
      },
    );
  }

  Widget _buildMedicineNameConfirmation(
      BuildContext context, String medicineName) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: AppColors.success,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Médicament sélectionné de la base de données Tunisienne',
                    style: AppTextStyles.titleSmall.copyWith(
                      color: AppColors.success,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () {
                    _searchController.clear();
                    context.read<AddMedicineBloc>().add(const SearchCleared());
                  },
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              medicineName,
              style: AppTextStyles.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Veuillez sélectionner le dosage et la forme dans les champs ci-dessous.',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.grey600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedMedicineCard(
      BuildContext context, TunisiaMedicine medicine) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: AppColors.success,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Médicament sélectionné',
                  style: AppTextStyles.titleSmall.copyWith(
                    color: AppColors.success,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () {
                    _searchController.clear();
                    context
                        .read<AddMedicineBloc>()
                        .add(const ModeToggled(isCustomMode: false));
                  },
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              medicine.nom,
              style: AppTextStyles.titleMedium,
            ),
            if (medicine.dosage != null || medicine.forme != null) ...[
              const SizedBox(height: 4),
              Text(
                [medicine.dosage, medicine.forme]
                    .where((e) => e != null && e.isNotEmpty)
                    .join(' - '),
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.grey600,
                ),
              ),
            ],
            if (medicine.laboratoire != null) ...[
              const SizedBox(height: 4),
              Text(
                'Laboratoire: ${medicine.laboratoire}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.grey600,
                ),
              ),
            ],
            if (medicine.dci != null) ...[
              const SizedBox(height: 4),
              Text(
                'DCI: ${medicine.dci}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.grey600,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

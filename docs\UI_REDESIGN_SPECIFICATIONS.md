# MedyTrack Mobile UI Redesign Specifications

## Overview
Complete UI redesign specifications based on the provided mockup to transform MedyTrack into a modern, user-friendly medical management application.

## Design System Foundation

### Colors (Existing MedyTrack Palette)
- **Primary Teal**: `#0DCDB7` (AppColors.teal)
- **Navy**: `#2D4A8E` (AppColors.navy)
- **White**: `#FFFFFF`
- **Light Gray**: `#F8F9FA`
- **Dark Gray**: `#6C757D`

### Typography
- **Primary Font**: System default (maintaining current AppTextStyles)
- **Heading Sizes**: 24px, 20px, 18px, 16px
- **Body Text**: 14px, 12px
- **Font Weights**: Regular (400), Medium (500), Bold (700)

## Screen Specifications

### 1. Dashboard Screen (Main Landing)

#### Header Component
```
┌─────────────────────────────────────┐
│ [Logo] MEDYTRACK        [☰]        │
│                                     │
│ Gestion de vos médicaments          │
│                                     │
│ Lorem ipsum dolor sit amet,         │
│ consectetur adipiscing elit...      │
│                                     │
│           [Commencer]               │
│                                     │
│     [Medical Illustration]          │
└─────────────────────────────────────┘
```

**Specifications:**
- Full-screen card with rounded corners (24px)
- Gradient background: Light blue to white
- Logo: MedyTrack branding (top-left)
- Hamburger menu: 3-line icon (top-right)
- Title: "Gestion de vos médicaments" (24px, bold)
- Description: Body text (14px, gray)
- CTA Button: Teal background, white text, rounded (12px)
- Illustration: Medical-themed vector graphic

### 2. Medicine Categories Screen

#### Header Bar
```
┌─────────────────────────────────────┐
│ [🏠] [⚙️]              [🔍] [☰]    │
└─────────────────────────────────────┘
```

#### Categories Grid
```
┌─────────────────────────────────────┐
│ Catégories                          │
│                                     │
│ ┌─────────┐  ┌─────────┐           │
│ │   💊    │  │   🫁    │           │
│ │ Pilules │  │Respirat.│           │
│ └─────────┘  └─────────┘           │
│                                     │
│ ┌─────────┐  ┌─────────┐           │
│ │   💉    │  │   ❤️    │           │
│ │Injection│  │ Cardio  │           │
│ └─────────┘  └─────────┘           │
└─────────────────────────────────────┘
```

#### Medicine List
```
┌─────────────────────────────────────┐
│ Mes Médicaments                     │
│                                     │
│ ┌─ [👨‍⚕️] Dr. Medicine Name ────┐   │
│ │   Dosage • Hospital Info      │   │
│ │   Additional details          [✓]│
│ └───────────────────────────────────┘
│                                     │
│ ┌─ [👩‍⚕️] Dr. Medicine Name ────┐   │
│ │   Dosage • Hospital Info      │   │
│ │   Additional details          [→]│
│ └───────────────────────────────────┘
└─────────────────────────────────────┘
```

**Specifications:**
- Header icons: 24px, navy color
- Category cards: 2x2 grid, white background, 16px rounded corners
- Card shadows: subtle elevation (2dp)
- Icons: 32px, centered in cards
- Medicine cards: Full width, 16px padding, white background
- Profile images: 48px circular avatars
- Action buttons: 32px, teal or navy color

### 3. Calendar & Scheduling Screen

#### Profile Header
```
┌─────────────────────────────────────┐
│ [👤] Dr. Lorem Ipsum          [☰]  │
│      Lorem Ipsum • Hospital         │
│      Lorem ipsum                    │
└─────────────────────────────────────┘
```

#### Calendar Component
```
┌─────────────────────────────────────┐
│ Book your Date                      │
│                                     │
│ < MONTH 2020 >                      │
│                                     │
│ M  T  W  T  F  S  S                │
│                                     │
│ 26 27 28 29 30 31  1               │
│  2  3  4  5  6  7  8               │
│  9 10 11 12 13 14 15               │
│ 16 17 18 19 20 21 22               │
│ 23 24 25 26 27 28 29               │
│ 30 31                              │
│                                     │
│           [CONFIRM]                 │
└─────────────────────────────────────┘
```

#### Time Selection
```
┌─────────────────────────────────────┐
│ Your Date                           │
│                                     │
│ 00 Month                            │
│                                     │
│ Morning                             │
│ 08:00  08:00  08:00                │
│                                     │
│ Afternoon                           │
│ 08:00  08:00  08:00                │
└─────────────────────────────────────┘
```

**Specifications:**
- Profile section: 80px height, white background
- Avatar: 56px circular image
- Calendar: 7-column grid layout
- Date cells: 40x40px, rounded corners when selected
- Selected dates: Teal background (#0DCDB7)
- Time slots: Chip-style buttons, 80px width
- Selected times: Teal background, white text

## Component Library Requirements

### 1. Header Components
- `ModernAppHeader`: Logo + hamburger menu
- `ProfileHeader`: Avatar + user info + menu
- `SimpleHeader`: Back button + title + action

### 2. Card Components
- `CategoryCard`: Icon + label, grid layout
- `MedicineCard`: Avatar + info + action button
- `InfoCard`: General purpose information display

### 3. Navigation Components
- `HamburgerMenu`: Slide-out navigation drawer
- `BottomNavigation`: Tab-based navigation
- `IconButton`: Consistent icon button styling

### 4. Form Components
- `CalendarPicker`: Month view with date selection
- `TimeSlotPicker`: Time selection chips
- `ModernButton`: Primary and secondary button styles
- `SearchBar`: Header-integrated search

### 5. Layout Components
- `ScreenContainer`: Consistent screen padding and structure
- `SectionHeader`: Consistent section titles
- `GridLayout`: Responsive grid for categories

## Implementation Priority

1. **Phase 1**: Header components and basic layout structure
2. **Phase 2**: Card components and grid layouts
3. **Phase 3**: Navigation and menu systems
4. **Phase 4**: Calendar and time selection components
5. **Phase 5**: Form components and interactions

## Technical Requirements

- Maintain BLoC architecture pattern
- Preserve internationalization support (FR, EN, AR)
- Ensure Material Design 3 compliance
- Responsive design for different screen sizes
- Accessibility support (semantic labels, contrast ratios)
- Smooth animations and transitions

## Screen Mapping: Current → New Design

### Dashboard Transformation
**Current**: `DashboardPage` with statistics grid and medicine sections
**New**: Welcome screen with categories and medicine management access

**Changes Required**:
- Replace statistics cards with category grid
- Transform medicine sections into specialist-style cards
- Add prominent welcome/onboarding section
- Implement new header with hamburger menu

### Medicine Management Transformation
**Current**: `MedicineListPage` and `MyMedicinesPage` with simple lists
**New**: Category-based navigation with specialist-style medicine cards

**Changes Required**:
- Add category selection grid (Pills, Respiratory, Injection, Cardio)
- Transform medicine list items into doctor/specialist-style cards
- Add search functionality in header
- Implement filtering by category

### Calendar Integration (New Feature)
**Current**: Basic reminder system
**New**: Full calendar interface for scheduling and reminders

**New Components Needed**:
- Monthly calendar view with date selection
- Time slot selection interface
- Appointment/reminder booking flow
- Integration with existing reminder system

## Implementation Roadmap

### Week 1: Foundation
- [ ] Create new component library structure
- [ ] Implement base header components
- [ ] Set up new layout containers

### Week 2: Dashboard Redesign
- [ ] Build category grid component
- [ ] Transform dashboard layout
- [ ] Implement navigation flow

### Week 3: Medicine Management
- [ ] Redesign medicine list screens
- [ ] Add category filtering
- [ ] Update medicine cards design

### Week 4: Calendar & Polish
- [ ] Build calendar components
- [ ] Add time selection interface
- [ ] Final testing and refinements

## Next Steps

1. Create new component library based on specifications
2. Implement dashboard screen redesign
3. Update medicine management screens
4. Add calendar and scheduling interfaces
5. Test across all supported languages and screen sizes

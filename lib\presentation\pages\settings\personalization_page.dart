import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/family_member.dart';
import '../../../domain/entities/location.dart';
import '../../../domain/entities/tag.dart';
import '../../bloc/personalization/personalization_bloc.dart';
import '../../bloc/personalization/personalization_event.dart';
import '../../bloc/personalization/personalization_state.dart';
import '../../bloc/settings/settings_bloc.dart';
import '../../bloc/settings/settings_event.dart';
import '../../bloc/settings/settings_state.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_state.dart' as auth_state;
import '../../widgets/settings/settings_section.dart';
import '../../widgets/settings/settings_tile.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../../l10n/generated/app_localizations.dart';

class PersonalizationPage extends StatefulWidget {
  const PersonalizationPage({super.key});

  @override
  State<PersonalizationPage> createState() => _PersonalizationPageState();
}

class _PersonalizationPageState extends State<PersonalizationPage> {
  final _searchController = TextEditingController();
  final _thresholdController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // Load both personalization and settings data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authState = context.read<AuthBloc>().state;
      if (authState is auth_state.AuthAuthenticated) {
        // Load personalization data
        final householdId = authState.user.householdId ??
            authState.user.id; // Use userId as fallback
        context.read<PersonalizationBloc>().add(
              PersonalizationLoadRequested(
                userId: authState.user.id,
                householdId: householdId,
              ),
            );

        // Load settings data for dark mode and other app settings
        context.read<SettingsBloc>().add(
              SettingsLoadRequested(userId: authState.user.id),
            );
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _thresholdController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      body: BlocConsumer<PersonalizationBloc, PersonalizationState>(
        listener: (context, state) {
          if (state is PersonalizationError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          } else if (state is PersonalizationOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.success,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is PersonalizationLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is! PersonalizationLoaded) {
            return Center(
                child: Text(AppLocalizations.of(context).loadingError));
          }

          // Update threshold controller
          if (_thresholdController.text.isEmpty) {
            _thresholdController.text =
                state.settings.minimumExpiryThreshold.toString();
          }

          return Stack(
            children: [
              Column(
                children: [
                  // Header with teal background
                  Container(
                    padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
                    child: Row(
                      children: [
                        GestureDetector(
                          onTap: () => context.pop(),
                          child: Container(
                            width: 48,
                            height: 48,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(
                              Icons.arrow_back,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Text(
                            'Personnalisation',
                            style: AppTextStyles.headlineMedium.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Content container with white background
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(24),
                          topRight: Radius.circular(24),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 8,
                            offset: const Offset(0, -4),
                          ),
                        ],
                      ),
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildPreferencesSection(state),
                            const SizedBox(height: 24),
                            _buildFamilyMembersSection(state),
                            const SizedBox(height: 24),
                            _buildLocationsSection(state),
                            const SizedBox(height: 24),
                            _buildTagsSection(state),
                            const SizedBox(height: 32),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),

              // Loading overlay for operations
              if (state is PersonalizationSettingsUpdating ||
                  state is FamilyMemberOperating ||
                  state is LocationOperating ||
                  state is TagOperating)
                LoadingOverlay(
                  message: _getLoadingMessage(state),
                ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildPreferencesSection(PersonalizationLoaded state) {
    final l10n = AppLocalizations.of(context);

    return SettingsSection(
      title: l10n.preferences,
      children: [
        SettingsTile(
          icon: Icons.schedule_outlined,
          title: 'Seuil d\'expiration minimum',
          subtitle: '${state.settings.minimumExpiryThreshold} jours',
          onTap: () => _showThresholdDialog(context, state),
        ),
        SettingsTile(
          icon: Icons.location_on_outlined,
          title: 'Emplacement par défaut',
          subtitle: state.defaultLocationEntity?.displayName ?? 'Aucun',
          onTap: () => _showLocationSelectionDialog(context, state),
        ),
        SettingsTile(
          icon: Icons.person_outline,
          title: 'Membre de famille par défaut',
          subtitle: state.defaultFamilyMemberEntity?.name ?? 'Aucun',
          onTap: () => _showFamilyMemberSelectionDialog(context, state),
        ),
        SettingsTile(
          icon: Icons.visibility_outlined,
          title: 'Afficher les médicaments expirés',
          subtitle:
              state.settings.showExpiredMedicines ? 'Activé' : 'Désactivé',
          trailing: Switch(
            value: state.settings.showExpiredMedicines,
            onChanged: (value) {
              context.read<PersonalizationBloc>().add(
                    ShowExpiredMedicinesToggleRequested(show: value),
                  );
            },
            activeColor: AppColors.teal,
          ),
        ),
        SettingsTile(
          icon: Icons.group_work_outlined,
          title: 'Grouper par emplacement',
          subtitle: state.settings.groupByLocation ? 'Activé' : 'Désactivé',
          trailing: Switch(
            value: state.settings.groupByLocation,
            onChanged: (value) {
              context.read<PersonalizationBloc>().add(
                    GroupByLocationToggleRequested(groupByLocation: value),
                  );
            },
            activeColor: AppColors.teal,
          ),
        ),
        // Dark mode toggle - moved from language settings
        BlocBuilder<SettingsBloc, SettingsState>(
          builder: (context, settingsState) {
            if (settingsState is SettingsLoaded) {
              return SettingsTile(
                icon: Icons.dark_mode_outlined,
                title: 'Mode sombre',
                subtitle: settingsState.settings.app.isDarkMode
                    ? 'Activé'
                    : 'Désactivé',
                trailing: Switch(
                  value: settingsState.settings.app.isDarkMode,
                  onChanged: (value) {
                    context.read<SettingsBloc>().add(
                          ThemeUpdateRequested(
                            userId: settingsState.settings.userId,
                            isDarkMode: value,
                          ),
                        );
                  },
                  activeColor: AppColors.teal,
                ),
              );
            }
            return const SizedBox.shrink();
          },
        ),
        // Date format selection - moved from language settings
        BlocBuilder<SettingsBloc, SettingsState>(
          builder: (context, settingsState) {
            if (settingsState is SettingsLoaded) {
              return SettingsTile(
                icon: Icons.date_range_outlined,
                title: 'Format de date',
                subtitle:
                    _getDateFormatDisplay(settingsState.settings.app.language),
                onTap: () => _showDateFormatDialog(context, settingsState),
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }

  Widget _buildFamilyMembersSection(PersonalizationLoaded state) {
    final l10n = AppLocalizations.of(context);

    return SettingsSection(
      title: l10n.familyMembers,
      children: [
        // Add new member tile
        SettingsTile(
          icon: Icons.person_add_outlined,
          title: 'Ajouter un membre',
          subtitle: 'Créer un nouveau membre de famille',
          onTap: () => _showAddFamilyMemberDialog(context),
          iconColor: AppColors.teal,
        ),

        // Existing members
        ...state.familyMembers.map((member) => SettingsTile(
              icon: Icons.person_outline,
              title: member.name,
              subtitle: member.relationship != null
                  ? RelationshipTypes.values.firstWhere(
                      (r) => r['value'] == member.relationship,
                      orElse: () => {'label': member.relationship!},
                    )['label']!
                  : 'Aucune relation',
              onTap: () => _showEditFamilyMemberDialog(context, member),
              trailing: PopupMenuButton<String>(
                onSelected: (value) {
                  if (value == 'edit') {
                    _showEditFamilyMemberDialog(context, member);
                  } else if (value == 'delete') {
                    _showDeleteConfirmationDialog(
                      context,
                      'Supprimer le membre',
                      'Êtes-vous sûr de vouloir supprimer ${member.name} ?',
                      () => context.read<PersonalizationBloc>().add(
                            FamilyMemberDeleteRequested(memberId: member.id),
                          ),
                    );
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 20),
                        SizedBox(width: 8),
                        Text('Modifier'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 20, color: AppColors.error),
                        SizedBox(width: 8),
                        Text('Supprimer',
                            style: TextStyle(color: AppColors.error)),
                      ],
                    ),
                  ),
                ],
              ),
            )),
      ],
    );
  }

  Widget _buildLocationsSection(PersonalizationLoaded state) {
    return SettingsSection(
      title: 'Emplacements de stockage',
      children: [
        // Add new location tile
        SettingsTile(
          icon: Icons.add_location_outlined,
          title: 'Ajouter un emplacement',
          subtitle: 'Créer un nouvel emplacement de stockage',
          onTap: () => _showAddLocationDialog(context),
          iconColor: AppColors.teal,
        ),

        // Existing locations
        ...state.locations.map((location) => SettingsTile(
              icon: Icons.location_on_outlined,
              title: location.displayName,
              subtitle: location.description ?? 'Aucune description',
              onTap: () => _showEditLocationDialog(context, location),
              trailing: PopupMenuButton<String>(
                onSelected: (value) {
                  if (value == 'edit') {
                    _showEditLocationDialog(context, location);
                  } else if (value == 'delete') {
                    _showDeleteConfirmationDialog(
                      context,
                      'Supprimer l\'emplacement',
                      'Êtes-vous sûr de vouloir supprimer ${location.displayName} ?',
                      () => context.read<PersonalizationBloc>().add(
                            LocationDeleteRequested(locationId: location.id),
                          ),
                    );
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 20),
                        SizedBox(width: 8),
                        Text('Modifier'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 20, color: AppColors.error),
                        SizedBox(width: 8),
                        Text('Supprimer',
                            style: TextStyle(color: AppColors.error)),
                      ],
                    ),
                  ),
                ],
              ),
            )),
      ],
    );
  }

  Widget _buildTagsSection(PersonalizationLoaded state) {
    return SettingsSection(
      title: 'Étiquettes',
      children: [
        // Add new tag tile
        SettingsTile(
          icon: Icons.label_outline,
          title: 'Ajouter une étiquette',
          subtitle: 'Créer une nouvelle étiquette',
          onTap: () => _showAddTagDialog(context),
          iconColor: AppColors.teal,
        ),

        // Therapeutic tags
        if (state.therapeuticTags.isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              'Étiquettes thérapeutiques',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.grey600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ...state.therapeuticTags.map((tag) => _buildTagTile(tag)),
        ],

        // Usage tags
        if (state.usageTags.isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              'Étiquettes d\'usage',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.grey600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ...state.usageTags.map((tag) => _buildTagTile(tag)),
        ],
      ],
    );
  }

  Widget _buildTagTile(Tag tag) {
    return SettingsTile(
      icon: Icons.label_outline,
      title: tag.name,
      subtitle: tag.category == 'therapeutic' ? 'Thérapeutique' : 'Usage',
      onTap: () => _showEditTagDialog(context, tag),
      trailing: PopupMenuButton<String>(
        onSelected: (value) {
          if (value == 'edit') {
            _showEditTagDialog(context, tag);
          } else if (value == 'delete') {
            _showDeleteConfirmationDialog(
              context,
              'Supprimer l\'étiquette',
              'Êtes-vous sûr de vouloir supprimer "${tag.name}" ?',
              () => context.read<PersonalizationBloc>().add(
                    TagDeleteRequested(tagId: tag.id),
                  ),
            );
          }
        },
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'edit',
            child: Row(
              children: [
                Icon(Icons.edit, size: 20),
                SizedBox(width: 8),
                Text('Modifier'),
              ],
            ),
          ),
          const PopupMenuItem(
            value: 'delete',
            child: Row(
              children: [
                Icon(Icons.delete, size: 20, color: AppColors.error),
                SizedBox(width: 8),
                Text('Supprimer', style: TextStyle(color: AppColors.error)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showThresholdDialog(BuildContext context, PersonalizationLoaded state) {
    double currentValue = state.settings.minimumExpiryThreshold.toDouble();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Seuil d\'expiration minimum'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Recevoir des alertes ${currentValue.toInt()} jours avant expiration',
                style: AppTextStyles.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),

              // Enhanced slider with labels
              Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('7 jours',
                          style: AppTextStyles.bodySmall
                              .copyWith(color: AppColors.grey600)),
                      Text('${currentValue.toInt()} jours',
                          style: AppTextStyles.titleMedium
                              .copyWith(color: AppColors.teal)),
                      Text('90 jours',
                          style: AppTextStyles.bodySmall
                              .copyWith(color: AppColors.grey600)),
                    ],
                  ),
                  const SizedBox(height: 8),
                  SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: AppColors.teal,
                      inactiveTrackColor: AppColors.teal.withValues(alpha: 0.3),
                      thumbColor: AppColors.teal,
                      overlayColor: AppColors.teal.withValues(alpha: 0.2),
                      valueIndicatorColor: AppColors.teal,
                      valueIndicatorTextStyle:
                          const TextStyle(color: Colors.white),
                    ),
                    child: Slider(
                      value: currentValue,
                      min: 7,
                      max: 90,
                      divisions: 83,
                      label: '${currentValue.toInt()} jours',
                      onChanged: (value) {
                        setState(() {
                          currentValue = value;
                        });
                      },
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.teal.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: AppColors.teal, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Vous recevrez des notifications pour les médicaments expirant dans ${currentValue.toInt()} jours ou moins.',
                        style: AppTextStyles.bodySmall
                            .copyWith(color: AppColors.teal),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                context.read<PersonalizationBloc>().add(
                      MinimumExpiryThresholdUpdateRequested(
                          days: currentValue.toInt()),
                    );
                Navigator.pop(context);
              },
              child: const Text('Enregistrer'),
            ),
          ],
        ),
      ),
    );
  }

  void _showLocationSelectionDialog(
      BuildContext context, PersonalizationLoaded state) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Emplacement par défaut'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Aucun'),
              leading: Radio<String>(
                value: '',
                groupValue: state.settings.defaultLocation,
                onChanged: (value) {
                  context.read<PersonalizationBloc>().add(
                        DefaultLocationUpdateRequested(locationId: ''),
                      );
                  Navigator.pop(context);
                },
              ),
            ),
            ...state.locations.map((location) => ListTile(
                  title: Text(location.displayName),
                  subtitle: location.description != null
                      ? Text(location.description!)
                      : null,
                  leading: Radio<String>(
                    value: location.id,
                    groupValue: state.settings.defaultLocation,
                    onChanged: (value) {
                      context.read<PersonalizationBloc>().add(
                            DefaultLocationUpdateRequested(
                                locationId: location.id),
                          );
                      Navigator.pop(context);
                    },
                  ),
                )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _showFamilyMemberSelectionDialog(
      BuildContext context, PersonalizationLoaded state) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Membre de famille par défaut'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Aucun'),
              leading: Radio<String>(
                value: '',
                groupValue: state.settings.defaultFamilyMember,
                onChanged: (value) {
                  context.read<PersonalizationBloc>().add(
                        DefaultFamilyMemberUpdateRequested(familyMemberId: ''),
                      );
                  Navigator.pop(context);
                },
              ),
            ),
            ...state.familyMembers.map((member) => ListTile(
                  title: Text(member.name),
                  subtitle: member.relationship != null
                      ? Text(RelationshipTypes.values.firstWhere(
                          (r) => r['value'] == member.relationship,
                          orElse: () => {'label': member.relationship!},
                        )['label']!)
                      : null,
                  leading: Radio<String>(
                    value: member.id,
                    groupValue: state.settings.defaultFamilyMember,
                    onChanged: (value) {
                      context.read<PersonalizationBloc>().add(
                            DefaultFamilyMemberUpdateRequested(
                                familyMemberId: member.id),
                          );
                      Navigator.pop(context);
                    },
                  ),
                )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _showAddFamilyMemberDialog(BuildContext context) {
    final nameController = TextEditingController();
    String selectedRelationship = 'self';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Ajouter un membre'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Nom',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: selectedRelationship,
                decoration: const InputDecoration(
                  labelText: 'Relation',
                  border: OutlineInputBorder(),
                ),
                items: RelationshipTypes.values.map((relationship) {
                  return DropdownMenuItem(
                    value: relationship['value'],
                    child: Text(relationship['label']!),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    selectedRelationship = value!;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                nameController.dispose();
                Navigator.pop(context);
              },
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                if (nameController.text.trim().isNotEmpty) {
                  final member = FamilyMember(
                    id: '', // Will be generated by the backend
                    householdId: '', // Will be set by the bloc
                    name: nameController.text.trim(),
                    relationship: selectedRelationship,
                    createdAt: DateTime.now(),
                  );

                  context.read<PersonalizationBloc>().add(
                        FamilyMemberCreateRequested(member: member),
                      );
                  nameController.dispose();
                  Navigator.pop(context);
                }
              },
              child: const Text('Ajouter'),
            ),
          ],
        ),
      ),
    );
  }

  void _showEditFamilyMemberDialog(BuildContext context, FamilyMember member) {
    final nameController = TextEditingController(text: member.name);
    String selectedRelationship = member.relationship ?? 'self';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Modifier le membre'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Nom',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: selectedRelationship,
                decoration: const InputDecoration(
                  labelText: 'Relation',
                  border: OutlineInputBorder(),
                ),
                items: RelationshipTypes.values.map((relationship) {
                  return DropdownMenuItem(
                    value: relationship['value'],
                    child: Text(relationship['label']!),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    selectedRelationship = value!;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                nameController.dispose();
                Navigator.pop(context);
              },
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                if (nameController.text.trim().isNotEmpty) {
                  final updatedMember = member.copyWith(
                    name: nameController.text.trim(),
                    relationship: selectedRelationship,
                  );

                  context.read<PersonalizationBloc>().add(
                        FamilyMemberUpdateRequested(member: updatedMember),
                      );
                  nameController.dispose();
                  Navigator.pop(context);
                }
              },
              child: const Text('Enregistrer'),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddLocationDialog(BuildContext context) {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    String selectedIcon = 'location_on'; // Default icon

    // Predefined icon options for locations
    final iconOptions = [
      {'icon': Icons.location_on, 'name': 'location_on', 'label': 'Général'},
      {
        'icon': Icons.medical_services,
        'name': 'medical_services',
        'label': 'Pharmacie'
      },
      {'icon': Icons.home, 'name': 'home', 'label': 'Maison'},
      {'icon': Icons.kitchen, 'name': 'kitchen', 'label': 'Cuisine'},
      {'icon': Icons.bed, 'name': 'bed', 'label': 'Chambre'},
      {'icon': Icons.bathroom, 'name': 'bathroom', 'label': 'Salle de bain'},
      {'icon': Icons.work, 'name': 'work', 'label': 'Bureau'},
      {
        'icon': Icons.local_hospital,
        'name': 'local_hospital',
        'label': 'Hôpital'
      },
      {'icon': Icons.school, 'name': 'school', 'label': 'École'},
      {'icon': Icons.car_rental, 'name': 'car_rental', 'label': 'Voiture'},
    ];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Ajouter un emplacement'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Nom',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description (optionnel)',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),

                // Icon picker section
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Icône',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.grey300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: iconOptions.map((iconData) {
                          final isSelected = selectedIcon == iconData['name'];
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                selectedIcon = iconData['name'] as String;
                              });
                            },
                            child: Tooltip(
                              message: iconData['label'] as String,
                              child: Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: isSelected
                                      ? AppColors.teal.withValues(alpha: 0.1)
                                      : Colors.transparent,
                                  borderRadius: BorderRadius.circular(8),
                                  border: isSelected
                                      ? Border.all(
                                          color: AppColors.teal, width: 2)
                                      : Border.all(
                                          color: AppColors.grey300, width: 1),
                                ),
                                child: Icon(
                                  iconData['icon'] as IconData,
                                  color: isSelected
                                      ? AppColors.teal
                                      : AppColors.grey600,
                                  size: 24,
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                nameController.dispose();
                descriptionController.dispose();
                Navigator.pop(context);
              },
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                if (nameController.text.trim().isNotEmpty) {
                  final location = Location(
                    id: '', // Will be generated by the backend
                    householdId: '', // Will be set by the bloc
                    name: nameController.text.trim(),
                    description: descriptionController.text.trim().isEmpty
                        ? null
                        : descriptionController.text.trim(),
                    icon: selectedIcon,
                    createdAt: DateTime.now(),
                  );

                  context.read<PersonalizationBloc>().add(
                        LocationCreateRequested(location: location),
                      );
                  nameController.dispose();
                  descriptionController.dispose();
                  Navigator.pop(context);
                }
              },
              child: const Text('Ajouter'),
            ),
          ],
        ),
      ),
    );
  }

  void _showEditLocationDialog(BuildContext context, Location location) {
    final nameController = TextEditingController(text: location.name);
    final descriptionController =
        TextEditingController(text: location.description ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Modifier l\'emplacement'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Nom',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (optionnel)',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              nameController.dispose();
              descriptionController.dispose();
              Navigator.pop(context);
            },
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.trim().isNotEmpty) {
                final updatedLocation = Location(
                  id: location.id,
                  householdId: location.householdId,
                  name: nameController.text.trim(),
                  description: descriptionController.text.trim().isEmpty
                      ? null
                      : descriptionController.text.trim(),
                  icon: location.icon,
                  createdAt: location.createdAt,
                  updatedAt: DateTime.now(),
                );

                context.read<PersonalizationBloc>().add(
                      LocationUpdateRequested(location: updatedLocation),
                    );
                nameController.dispose();
                descriptionController.dispose();
                Navigator.pop(context);
              }
            },
            child: const Text('Enregistrer'),
          ),
        ],
      ),
    );
  }

  void _showAddTagDialog(BuildContext context) {
    final nameController = TextEditingController();
    String selectedCategory = 'therapeutic';
    String selectedColor = '#14B8A6'; // Default teal color

    // Predefined color options
    final colorOptions = [
      '#14B8A6', // Teal
      '#EF4444', // Red
      '#3B82F6', // Blue
      '#10B981', // Green
      '#F59E0B', // Yellow
      '#8B5CF6', // Purple
      '#F97316', // Orange
      '#EC4899', // Pink
      '#6B7280', // Gray
      '#1F2937', // Dark Gray
    ];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Ajouter une étiquette'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Nom',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: selectedCategory,
                  decoration: const InputDecoration(
                    labelText: 'Catégorie',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: 'therapeutic',
                      child: Text('Thérapeutique'),
                    ),
                    DropdownMenuItem(
                      value: 'usage',
                      child: Text('Usage'),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedCategory = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // Color picker section
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Couleur',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.grey300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: colorOptions.map((color) {
                          final isSelected = selectedColor == color;
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                selectedColor = color;
                              });
                            },
                            child: Container(
                              width: 32,
                              height: 32,
                              decoration: BoxDecoration(
                                color: Color(
                                    int.parse(color.substring(1), radix: 16) +
                                        0xFF000000),
                                shape: BoxShape.circle,
                                border: isSelected
                                    ? Border.all(
                                        color: AppColors.navy, width: 3)
                                    : Border.all(
                                        color: AppColors.grey300, width: 1),
                              ),
                              child: isSelected
                                  ? const Icon(Icons.check,
                                      color: Colors.white, size: 16)
                                  : null,
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                nameController.dispose();
                Navigator.pop(context);
              },
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                if (nameController.text.trim().isNotEmpty) {
                  final tag = Tag(
                    id: '', // Will be generated by the backend
                    householdId: '', // Will be set by the bloc
                    name: nameController.text.trim(),
                    category: selectedCategory,
                    color: selectedColor,
                    createdAt: DateTime.now(),
                  );

                  context.read<PersonalizationBloc>().add(
                        TagCreateRequested(tag: tag),
                      );
                  nameController.dispose();
                  Navigator.pop(context);
                }
              },
              child: const Text('Ajouter'),
            ),
          ],
        ),
      ),
    );
  }

  void _showEditTagDialog(BuildContext context, Tag tag) {
    final nameController = TextEditingController(text: tag.name);
    String selectedCategory = tag.category;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Modifier l\'étiquette'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Nom',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: selectedCategory,
                decoration: const InputDecoration(
                  labelText: 'Catégorie',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(
                    value: 'therapeutic',
                    child: Text('Thérapeutique'),
                  ),
                  DropdownMenuItem(
                    value: 'usage',
                    child: Text('Usage'),
                  ),
                ],
                onChanged: (value) {
                  setState(() {
                    selectedCategory = value!;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                nameController.dispose();
                Navigator.pop(context);
              },
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                if (nameController.text.trim().isNotEmpty) {
                  final updatedTag = Tag(
                    id: tag.id,
                    householdId: tag.householdId,
                    name: nameController.text.trim(),
                    category: selectedCategory,
                    color: tag.color,
                    createdAt: tag.createdAt,
                    updatedAt: DateTime.now(),
                  );

                  context.read<PersonalizationBloc>().add(
                        TagUpdateRequested(tag: updatedTag),
                      );
                  nameController.dispose();
                  Navigator.pop(context);
                }
              },
              child: const Text('Enregistrer'),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmationDialog(
    BuildContext context,
    String title,
    String content,
    VoidCallback onConfirm,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              onConfirm();
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  String _getLoadingMessage(PersonalizationState state) {
    if (state is PersonalizationSettingsUpdating) {
      switch (state.section) {
        case 'threshold':
          return 'Mise à jour du seuil...';
        case 'location':
          return 'Mise à jour de l\'emplacement...';
        case 'member':
          return 'Mise à jour du membre...';
        case 'categories':
          return 'Mise à jour des catégories...';
        default:
          return 'Mise à jour des préférences...';
      }
    } else if (state is FamilyMemberOperating) {
      switch (state.operation) {
        case 'create':
          return 'Création du membre...';
        case 'update':
          return 'Modification du membre...';
        case 'delete':
          return 'Suppression du membre...';
        default:
          return 'Opération en cours...';
      }
    } else if (state is LocationOperating) {
      switch (state.operation) {
        case 'create':
          return 'Création de l\'emplacement...';
        case 'update':
          return 'Modification de l\'emplacement...';
        case 'delete':
          return 'Suppression de l\'emplacement...';
        default:
          return 'Opération en cours...';
      }
    } else if (state is TagOperating) {
      switch (state.operation) {
        case 'create':
          return 'Création de l\'étiquette...';
        case 'update':
          return 'Modification de l\'étiquette...';
        case 'delete':
          return 'Suppression de l\'étiquette...';
        default:
          return 'Opération en cours...';
      }
    }
    return 'Chargement...';
  }

  /// Gradient matching the white-blue background style
  LinearGradient _getHeaderGradient() {
    return const LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        Color(0xFFE3F0FF), // Light pastel blue
        Color(0xFFFFFFFF), // White bottom blend
      ],
    );
  }

  /// Integrated header card with title, subtitle, and back arrow
  Widget _buildIntegratedHeaderCard() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Back arrow
          GestureDetector(
            onTap: () => context.pop(),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.navy.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.arrow_back,
                color: AppColors.navy,
                size: 24,
              ),
            ),
          ),

          // Title and subtitle
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Personnalisation',
                    style: AppTextStyles.titleLarge.copyWith(
                      color: AppColors.navy,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Gérer vos préférences et données',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Action button (settings icon)
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.teal.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.tune,
              color: AppColors.teal,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  /// Get display text for current date format
  String _getDateFormatDisplay(String language) {
    switch (language) {
      case 'fr':
        return 'DD/MM/YYYY (Français)';
      case 'en':
        return 'MM/DD/YYYY (English)';
      case 'ar':
        return 'DD/MM/YYYY (العربية)';
      default:
        return 'DD/MM/YYYY';
    }
  }

  /// Show date format selection dialog
  void _showDateFormatDialog(BuildContext context, SettingsLoaded state) {
    final formats = [
      {'code': 'dd/MM/yyyy', 'name': 'DD/MM/YYYY', 'example': '25/12/2024'},
      {'code': 'MM/dd/yyyy', 'name': 'MM/DD/YYYY', 'example': '12/25/2024'},
      {'code': 'yyyy-MM-dd', 'name': 'YYYY-MM-DD', 'example': '2024-12-25'},
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Format de date'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: formats.map((format) {
            return ListTile(
              title: Text(format['name']!),
              subtitle: Text('Exemple: ${format['example']}'),
              onTap: () {
                // For now, just close the dialog
                // In a real implementation, you'd update the date format setting
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content:
                        Text('Format de date changé vers ${format['name']}'),
                    backgroundColor: AppColors.teal,
                  ),
                );
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
        ],
      ),
    );
  }
}

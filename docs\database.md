# Database Schema Documentation

MedyTrack Mobile uses Supabase (PostgreSQL) as its backend database. This document outlines the database schema, relationships, and key considerations.

## 📊 Database Overview

The database is designed to support multi-household medicine management with the following core principles:

- **Multi-tenancy**: Each household operates independently
- **Real-time updates**: Changes sync across all devices instantly
- **Data integrity**: Foreign key constraints and validation rules
- **Security**: Row Level Security (RLS) policies protect user data
- **Scalability**: Optimized indexes and efficient queries

## 🗃️ Core Tables

### Users Table

Stores user authentication and profile information.

```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  display_name TEXT,
  avatar_url TEXT,
  phone TEXT,
  date_of_birth DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Key Features:**
- Integrates with Supa<PERSON> Auth
- Stores additional profile information
- Supports avatar uploads to Supabase Storage

### Households Table

Manages family/household groupings for shared medicine management.

```sql
CREATE TABLE households (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  created_by UUID REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Key Features:**
- One household can have multiple users
- Creator has admin privileges
- Soft delete support for data retention

### Household Members Table

Junction table for user-household relationships.

```sql
CREATE TABLE household_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  household_id UUID REFERENCES households(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  role TEXT DEFAULT 'member' CHECK (role IN ('admin', 'member')),
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(household_id, user_id)
);
```

**Roles:**
- `admin`: Can manage household settings and members
- `member`: Can view and manage medicines

### Medicines Table

Core table storing medicine information and details.

```sql
CREATE TABLE medicines (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  household_id UUID REFERENCES households(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  dosage TEXT,
  form TEXT, -- tablet, capsule, liquid, etc.
  manufacturer TEXT,
  barcode TEXT,
  image_url TEXT,
  quantity INTEGER DEFAULT 0,
  unit TEXT DEFAULT 'pieces',
  expiry_date DATE,
  purchase_date DATE,
  price DECIMAL(10,2),
  location_id UUID REFERENCES medicine_locations(id),
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Key Features:**
- Household-scoped data isolation
- Support for barcode scanning
- Image storage integration
- Flexible quantity and unit system
- Price tracking for cost analysis

### Medicine Locations Table

Manages storage locations within households.

```sql
CREATE TABLE medicine_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  household_id UUID REFERENCES households(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  icon TEXT DEFAULT 'location_on',
  color TEXT DEFAULT '#14B8A6',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(household_id, name)
);
```

**Default Locations:**
- Kitchen
- Bathroom
- Bedroom
- Living Room
- First Aid Kit
- Refrigerator

### Medicine Tags Table

Categorization system for medicines.

```sql
CREATE TABLE medicine_tags (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  household_id UUID REFERENCES households(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  color TEXT DEFAULT '#6B7280',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(household_id, name)
);
```

### Medicine Tag Assignments Table

Junction table for medicine-tag relationships.

```sql
CREATE TABLE medicine_tag_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  medicine_id UUID REFERENCES medicines(id) ON DELETE CASCADE,
  tag_id UUID REFERENCES medicine_tags(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(medicine_id, tag_id)
);
```

### Notifications Table

Manages alerts and reminders for users.

```sql
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  household_id UUID REFERENCES households(id) ON DELETE CASCADE,
  medicine_id UUID REFERENCES medicines(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN ('expiry_warning', 'low_stock', 'expired')),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  scheduled_for TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Notification Types:**
- `expiry_warning`: Medicine expiring soon
- `low_stock`: Quantity below threshold
- `expired`: Medicine has expired

### Reminders Table (v0.4.0)

Manages medicine reminder scheduling with flexible frequency options.

```sql
CREATE TABLE reminders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_medicine_id UUID NOT NULL REFERENCES user_medicines(id) ON DELETE CASCADE,
  times TEXT[] NOT NULL,
  frequency_type TEXT NOT NULL CHECK (frequency_type IN ('daily', 'weekly', 'hourlyInterval')),
  interval_hours INTEGER,
  days_of_week INTEGER[],
  start_date DATE NOT NULL,
  end_date DATE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Frequency Types:**
- `daily`: Take medicine every day at specified times
- `weekly`: Take medicine on specific days of the week (stored in days_of_week array)
- `hourlyInterval`: Take medicine every X hours (interval_hours field)

**Key Features:**
- Multiple times per day support via TEXT[] array
- Flexible scheduling with different frequency types
- Start and end date support for temporary medications
- Active/inactive status for pausing reminders

### Dose History Table (v0.4.0)

Tracks medication adherence and dose-taking history.

```sql
CREATE TABLE dose_history (
  id SERIAL PRIMARY KEY,
  user_medicine_id UUID NOT NULL REFERENCES user_medicines(id) ON DELETE CASCADE,
  reminder_id UUID REFERENCES reminders(id) ON DELETE SET NULL,
  scheduled_time TIMESTAMP WITH TIME ZONE NOT NULL,
  action_time TIMESTAMP WITH TIME ZONE,
  status TEXT NOT NULL CHECK (status IN ('TAKEN', 'SKIPPED', 'SNOOZED')),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Status Types:**
- `TAKEN`: User confirmed taking the medicine
- `SKIPPED`: User explicitly skipped the dose
- `SNOOZED`: User postponed the reminder

**Key Features:**
- Links to both user_medicine and reminder for full traceability
- Tracks both scheduled time and actual action time
- Optional notes for additional context
- Supports adherence reporting and analytics

## 🔐 Security Policies

### Row Level Security (RLS)

All tables implement RLS policies to ensure data isolation:

```sql
-- Users can only access their own data
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

-- Household members can access household data
CREATE POLICY "Household members can view medicines" ON medicines
  FOR SELECT USING (
    household_id IN (
      SELECT household_id FROM household_members 
      WHERE user_id = auth.uid()
    )
  );
```

### API Security

- JWT tokens for authentication
- Rate limiting on API endpoints
- Input validation and sanitization
- Encrypted data transmission (HTTPS)

## 📈 Performance Optimizations

### Indexes

```sql
-- Frequently queried columns
CREATE INDEX idx_medicines_household_id ON medicines(household_id);
CREATE INDEX idx_medicines_expiry_date ON medicines(expiry_date);
CREATE INDEX idx_medicines_name_search ON medicines USING gin(to_tsvector('english', name));
CREATE INDEX idx_notifications_user_unread ON notifications(user_id, is_read);

-- Reminder system indexes (v0.4.0)
CREATE INDEX idx_reminders_user_medicine_active ON reminders(user_medicine_id, is_active);
CREATE INDEX idx_reminders_frequency_type ON reminders(frequency_type);
CREATE INDEX idx_dose_history_user_medicine ON dose_history(user_medicine_id);
CREATE INDEX idx_dose_history_scheduled_time ON dose_history(scheduled_time);
CREATE INDEX idx_dose_history_status ON dose_history(status);
```

### Views

```sql
-- Dashboard statistics view
CREATE VIEW dashboard_stats AS
SELECT 
  household_id,
  COUNT(*) as total_medicines,
  COUNT(*) FILTER (WHERE expiry_date < CURRENT_DATE) as expired_count,
  COUNT(*) FILTER (WHERE expiry_date BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '30 days') as expiring_soon_count,
  COUNT(*) FILTER (WHERE quantity <= 5) as low_stock_count
FROM medicines
GROUP BY household_id;
```

## 🔄 Data Relationships

```
Users ──┐
        ├── Household_Members ──── Households
        │                            │
        └── Notifications            │
                                     │
Medicine_Tags ──┐                    │
                ├── Medicine_Tag_Assignments
                │                    │
Medicine_Locations                   │
        │                           │
        └── User_Medicines ─────────┘
                │
                ├── Reminders ──┐
                │               ├── Dose_History
                │               └── (Notification scheduling)
                │
                └── Notifications
```

## 🚀 Migration Scripts

### Initial Setup

```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create tables in dependency order
\i 01_users.sql
\i 02_households.sql
\i 03_household_members.sql
\i 04_medicine_locations.sql
\i 05_medicine_tags.sql
\i 06_medicines.sql
\i 07_medicine_tag_assignments.sql
\i 08_notifications.sql

-- Reminder system (v0.4.0)
\i 09_reminders.sql
\i 10_dose_history.sql

-- Create indexes and views
\i 11_indexes.sql
\i 12_views.sql

-- Set up RLS policies
\i 11_security_policies.sql

-- Insert default data
\i 12_default_data.sql
```

### Version Updates

Migration scripts are versioned and applied sequentially:

- `v1.0.0_initial_schema.sql`
- `v1.1.0_add_barcode_support.sql`
- `v1.2.0_notification_system.sql`
- `v1.3.0_reminder_system.sql` (v0.4.0 - Comprehensive reminder and dose tracking)

## 📊 Analytics Queries

### Common Dashboard Queries

```sql
-- Get household medicine statistics
SELECT 
  COUNT(*) as total,
  COUNT(*) FILTER (WHERE expiry_date < CURRENT_DATE) as expired,
  COUNT(*) FILTER (WHERE expiry_date BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '30 days') as expiring_soon,
  COUNT(*) FILTER (WHERE quantity <= 5) as low_stock
FROM medicines 
WHERE household_id = $1;

-- Get recently added medicines
SELECT * FROM medicines 
WHERE household_id = $1 
ORDER BY created_at DESC 
LIMIT 5;

-- Search medicines with full-text search
SELECT * FROM medicines
WHERE household_id = $1
AND to_tsvector('english', name || ' ' || COALESCE(description, '')) @@ plainto_tsquery('english', $2)
ORDER BY ts_rank(to_tsvector('english', name), plainto_tsquery('english', $2)) DESC;
```

### Reminder System Analytics (v0.4.0)

```sql
-- Get today's scheduled reminders
SELECT
  um.custom_name,
  r.times,
  r.frequency_type,
  CASE
    WHEN dh.status IS NOT NULL THEN dh.status
    ELSE 'PENDING'
  END as status
FROM reminders r
JOIN user_medicines um ON r.user_medicine_id = um.id
LEFT JOIN dose_history dh ON r.id = dh.reminder_id
  AND DATE(dh.scheduled_time) = CURRENT_DATE
WHERE r.is_active = true
  AND um.user_id = auth.uid()
ORDER BY r.times[1];

-- Calculate adherence rate for a user
SELECT
  um.custom_name,
  COUNT(*) FILTER (WHERE dh.status = 'TAKEN') * 100.0 / COUNT(*) as adherence_rate,
  COUNT(*) as total_doses,
  COUNT(*) FILTER (WHERE dh.status = 'TAKEN') as taken_doses,
  COUNT(*) FILTER (WHERE dh.status = 'SKIPPED') as skipped_doses
FROM dose_history dh
JOIN user_medicines um ON dh.user_medicine_id = um.id
WHERE um.user_id = auth.uid()
  AND dh.scheduled_time >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY um.id, um.custom_name
ORDER BY adherence_rate DESC;

-- Get reminder statistics for dashboard
SELECT
  COUNT(DISTINCT r.id) as total_active_reminders,
  COUNT(DISTINCT CASE WHEN r.frequency_type = 'daily' THEN r.id END) as daily_reminders,
  COUNT(DISTINCT CASE WHEN r.frequency_type = 'weekly' THEN r.id END) as weekly_reminders,
  COUNT(DISTINCT CASE WHEN r.frequency_type = 'hourlyInterval' THEN r.id END) as interval_reminders
FROM reminders r
JOIN user_medicines um ON r.user_medicine_id = um.id
WHERE r.is_active = true
  AND um.user_id = auth.uid();
```

## 🔧 Maintenance

### Regular Tasks

- **Cleanup expired notifications**: Remove old read notifications
- **Update statistics**: Refresh materialized views if used
- **Archive old data**: Move deleted records to archive tables
- **Monitor performance**: Check slow queries and optimize indexes

### Backup Strategy

- **Daily backups**: Automated Supabase backups
- **Point-in-time recovery**: Available for last 7 days
- **Export functionality**: User data export for GDPR compliance

## 🔗 Related Documentation

- [Architecture Overview](architecture.md)
- [API Documentation](api.md)
- [Deployment Guide](deployment.md)
- [Contributing Guidelines](../CONTRIBUTING.md)

---

For more technical details, see the [Supabase Documentation](https://supabase.com/docs) or contact the development team.

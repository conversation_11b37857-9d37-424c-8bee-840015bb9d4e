import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/medicine.dart';
import '../common/base_list_card.dart';
import '../common/status_badge.dart';

/// Enhanced medicine list card following the new design specifications
/// Features:
/// - 72dp fixed height
/// - 16dp border radius
/// - Status badges with proper styling
/// - Consistent typography and icon sizing
/// - Material 3 design principles
class EnhancedMedicineListCard extends StatelessWidget {
  final Medicine medicine;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool isSelected;
  final bool isSelectionMode;

  const EnhancedMedicineListCard({
    super.key,
    required this.medicine,
    this.onTap,
    this.onLongPress,
    this.onEdit,
    this.onDelete,
    this.isSelected = false,
    this.isSelectionMode = false,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedBaseListCard(
      leading: _buildLeading(),
      title: _buildTitle(),
      subtitle: _buildSubtitle(),
      trailing: _buildTrailing(),
      actions: _buildActions(),
      onTap: onTap,
      onLongPress: onLongPress,
      isSelected: isSelected,
      showBorder: isSelectionMode,
      backgroundColor: _getStatusBackgroundColor(),
      borderColor: _getStatusColor(),
    );
  }

  Widget _buildLeading() {
    return ListCardLeading.icon(
      icon: Icons.medication,
      color: _getStatusColor(),
      size: 48.0,
      iconSize: 20.0, // 20dp for primary icons
    );
  }

  Widget _buildTitle() {
    return Text(
      medicine.displayName,
      style: AppTextStyles.titleMedium.copyWith(
        fontSize: AppTextStyles.titleMedium.fontSize! - 2, // Reduced by 2pt
        fontWeight: FontWeight.w600,
        color: AppColors.navy,
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildSubtitle() {
    final List<String> subtitleParts = [];

    // Add dosage in black color (2pt smaller than title)
    if (medicine.dosage != null && medicine.dosage!.isNotEmpty) {
      subtitleParts.add(medicine.dosage!);
    }

    // Add expiration date without icon
    if (medicine.expiration != null) {
      subtitleParts
          .add('Exp: ${DateFormat('dd/MM/yyyy').format(medicine.expiration!)}');
    }

    // Add location without icon
    if (medicine.locationName != null && medicine.locationName!.isNotEmpty) {
      subtitleParts.add(medicine.locationName!);
    }

    // Add quantity
    subtitleParts.add('Qté: ${medicine.quantity}');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Dosage on separate line in black
        if (medicine.dosage != null && medicine.dosage!.isNotEmpty) ...[
          Text(
            medicine.dosage!,
            style: AppTextStyles.bodyMedium.copyWith(
              fontSize: AppTextStyles.titleMedium.fontSize! -
                  3, // Reduced by 1pt more (total -3pt)
              color: Colors.black,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 2),
        ],
        // Simplified info line
        Text(
          subtitleParts
              .skip(medicine.dosage != null && medicine.dosage!.isNotEmpty
                  ? 1
                  : 0)
              .join(' • '),
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.grey600,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildTrailing() {
    // Status badge positioned at bottom-right
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        MedicineStatusBadge.fromStatus(medicine.status),
      ],
    );
  }

  List<Widget>? _buildActions() {
    if (onEdit == null && onDelete == null) return null;

    return [
      ListCardTrailing.moreOptions(
        items: [
          if (onEdit != null)
            PopupMenuItem<String>(
              value: 'edit',
              child: Row(
                children: [
                  Icon(
                    Icons.edit_outlined,
                    size: 18,
                    color: AppColors.grey600,
                  ),
                  const SizedBox(width: 8),
                  const Text('Modifier'),
                ],
              ),
            ),
          if (onDelete != null)
            PopupMenuItem<String>(
              value: 'delete',
              child: Row(
                children: [
                  Icon(
                    Icons.delete_outline,
                    size: 18,
                    color: AppColors.error,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Supprimer',
                    style: TextStyle(color: AppColors.error),
                  ),
                ],
              ),
            ),
        ],
        onSelected: (value) {
          switch (value) {
            case 'edit':
              onEdit?.call();
              break;
            case 'delete':
              onDelete?.call();
              break;
          }
        },
      ),
    ];
  }

  Color _getStatusColor() {
    switch (medicine.status) {
      case MedicineStatus.expired:
        return AppColors.expired;
      case MedicineStatus.expiringSoon:
        return AppColors.expiringSoon;
      case MedicineStatus.lowStock:
        return AppColors.lowStock;
      case MedicineStatus.outOfStock:
        return AppColors.error;
      case MedicineStatus.normal:
        return AppColors.adequate;
    }
  }

  Color _getStatusBackgroundColor() {
    // Use medicine status color with very light opacity for background
    final statusColor = _getStatusColor();
    return statusColor.withValues(alpha: 0.05);
  }
}

/// Compact version of the enhanced medicine card for dense lists
class CompactMedicineListCard extends StatelessWidget {
  final Medicine medicine;
  final VoidCallback? onTap;
  final bool isSelected;

  const CompactMedicineListCard({
    super.key,
    required this.medicine,
    this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return BaseListCard(
      height: 56.0, // Smaller height for compact version
      onTap: onTap,
      isSelected: isSelected,
      backgroundColor: _getStatusColor().withValues(alpha: 0.05),
      borderColor: _getStatusColor(),
      child: Row(
        children: [
          // Compact leading icon
          ListCardLeading.icon(
            icon: Icons.medication,
            color: _getStatusColor(),
            size: 40.0,
            iconSize: 18.0,
            borderRadius: 6.0,
          ),

          const SizedBox(width: 12.0),

          // Main content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  medicine.displayName,
                  style: AppTextStyles.titleSmall.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (medicine.dosage != null) ...[
                  const SizedBox(height: 2.0),
                  Text(
                    medicine.dosage!,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.grey600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),

          // Compact status badge
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 8.0,
              vertical: 4.0,
            ),
            decoration: BoxDecoration(
              color: _getStatusColor().withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Text(
              _getStatusText(),
              style: AppTextStyles.labelSmall.copyWith(
                color: _getStatusColor(),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (medicine.status) {
      case MedicineStatus.expired:
        return AppColors.expired;
      case MedicineStatus.expiringSoon:
        return AppColors.expiringSoon;
      case MedicineStatus.lowStock:
        return AppColors.lowStock;
      case MedicineStatus.outOfStock:
        return AppColors.error;
      case MedicineStatus.normal:
        return AppColors.adequate;
    }
  }

  String _getStatusText() {
    switch (medicine.status) {
      case MedicineStatus.expired:
        return 'Expiré';
      case MedicineStatus.expiringSoon:
        return 'Expire';
      case MedicineStatus.lowStock:
        return 'Faible';
      case MedicineStatus.outOfStock:
        return 'Rupture';
      case MedicineStatus.normal:
        return 'OK';
    }
  }
}

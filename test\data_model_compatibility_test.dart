import 'package:flutter_test/flutter_test.dart';
import 'package:medytrack_mobile_v2/data/models/family_member_model.dart';
import 'package:medytrack_mobile_v2/data/models/location_model.dart';

void main() {
  group('Data Model Compatibility Tests', () {
    group('FamilyMemberModel', () {
      test('should handle data with relation field in English', () {
        final json = {
          'id': '123',
          'household_id': '456',
          'name': 'Test Member',
          'relation': 'father',
          'birth_date': '1980-01-01',
          'created_at': '2023-01-01T00:00:00Z',
        };

        final model = FamilyMemberModel.fromJson(json);
        expect(model.relationship, equals('père'));
      });

      test('should handle data with relation field in French (backward compatibility)', () {
        final json = {
          'id': '123',
          'household_id': '456',
          'name': 'Test Member',
          'relation': 'père',
          'birth_date': '1980-01-01',
          'created_at': '2023-01-01T00:00:00Z',
        };

        final model = FamilyMemberModel.fromJson(json);
        expect(model.relationship, equals('père'));
      });

      test('should handle data with role field fallback', () {
        final json = {
          'id': '123',
          'household_id': '456',
          'name': 'Test Member',
          'role': 'mother',
          'birth_date': '1980-01-01',
          'created_at': '2023-01-01T00:00:00Z',
        };

        final model = FamilyMemberModel.fromJson(json);
        expect(model.relationship, equals('mère'));
      });

      test('should handle data with null relationship fields', () {
        final json = {
          'id': '123',
          'household_id': '456',
          'name': 'Test Member',
          'birth_date': '1980-01-01',
          'created_at': '2023-01-01T00:00:00Z',
        };

        final model = FamilyMemberModel.fromJson(json);
        expect(model.relationship, equals('autre'));
      });

      test('should handle data with empty relationship fields', () {
        final json = {
          'id': '123',
          'household_id': '456',
          'name': 'Test Member',
          'relation': '',
          'role': '',
          'birth_date': '1980-01-01',
          'created_at': '2023-01-01T00:00:00Z',
        };

        final model = FamilyMemberModel.fromJson(json);
        expect(model.relationship, equals('autre'));
      });
    });

    group('LocationModel', () {
      test('should handle basic location data', () {
        final json = {
          'id': '123',
          'household_id': '456',
          'name': 'Test Location',
          'description': 'Test Description',
          'created_at': '2023-01-01T00:00:00Z',
        };

        final model = LocationModel.fromJson(json);
        expect(model.name, equals('Test Location'));
        expect(model.description, equals('Test Description'));
        expect(model.icon, equals('home')); // Default icon
      });

      test('should handle location data with null description', () {
        final json = {
          'id': '123',
          'household_id': '456',
          'name': 'Test Location',
          'created_at': '2023-01-01T00:00:00Z',
        };

        final model = LocationModel.fromJson(json);
        expect(model.name, equals('Test Location'));
        expect(model.description, isNull);
        expect(model.icon, equals('home'));
      });
    });
  });
}

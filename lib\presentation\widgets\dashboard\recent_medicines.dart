import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/supabase_utils.dart';
import '../../../domain/entities/medicine.dart';
import '../../bloc/medicine/medicine_bloc.dart';

class RecentMedicines extends StatefulWidget {
  const RecentMedicines({super.key});

  @override
  State<RecentMedicines> createState() => _RecentMedicinesState();
}

class _RecentMedicinesState extends State<RecentMedicines> {
  @override
  void initState() {
    super.initState();
    _loadRecentMedicines();
  }

  void _loadRecentMedicines() {
    final householdId = SupabaseUtils.getHouseholdId(context);
    if (householdId != null) {
      context
          .read<MedicineBloc>()
          .add(MedicineLoadRequested(householdId: householdId));
    } else {
      // Handle case where household ID is not available
      debugPrint('Cannot load recent medicines: household ID not available');
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MedicineBloc, MedicineState>(
      builder: (context, state) {
        if (state is MedicineLoading) {
          return Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.grey200),
            ),
            child: Center(
              child: CircularProgressIndicator(
                color: AppColors.teal,
              ),
            ),
          );
        }

        if (state is MedicineError) {
          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.grey200),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.error_outline,
                  size: 48,
                  color: AppColors.error,
                ),
                const SizedBox(height: 12),
                Text(
                  'Erreur de chargement',
                  style: AppTextStyles.titleMedium.copyWith(
                    color: AppColors.grey600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  state.message,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.grey500,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadRecentMedicines,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.teal,
                    foregroundColor: AppColors.white,
                  ),
                  child: const Text('Réessayer'),
                ),
              ],
            ),
          );
        }

        if (state is MedicineLoaded) {
          // Get recent medicines (last 30 days, sorted by most recent first)
          final now = DateTime.now();
          final thirtyDaysAgo = now.subtract(const Duration(days: 30));

          final recentMedicines = state.medicines.where((medicine) {
            return medicine.createdAt.isAfter(thirtyDaysAgo);
          }).toList()
            ..sort((a, b) =>
                b.createdAt.compareTo(a.createdAt)) // Most recent first
            ..take(5);

          if (recentMedicines.isEmpty) {
            return Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.grey200),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.medication,
                    size: 48,
                    color: AppColors.grey400,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Aucun médicament récent',
                    style: AppTextStyles.titleMedium.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Les médicaments récemment ajoutés apparaîtront ici',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.grey500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return Container(
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.grey200),
            ),
            child: Column(
              children: [
                ...recentMedicines.asMap().entries.map((entry) {
                  final index = entry.key;
                  final medicine = entry.value;
                  return Column(
                    children: [
                      _RecentMedicineItem(
                        medicine: medicine,
                        onTap: () => context.push('/medicines/${medicine.id}'),
                      ),
                      if (index < recentMedicines.length - 1)
                        Divider(
                          height: 1,
                          color: AppColors.grey200,
                        ),
                    ],
                  );
                }),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => context.push('/medicines'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.teal,
                        foregroundColor: AppColors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text('Voir tous les médicaments'),
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }
}

class _RecentMedicineItem extends StatelessWidget {
  final Medicine medicine;
  final VoidCallback onTap;

  const _RecentMedicineItem({
    required this.medicine,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: onTap,
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: _getStatusColor().withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.medication,
          color: _getStatusColor(),
          size: 20,
        ),
      ),
      title: Text(
        medicine.displayName,
        style: AppTextStyles.titleSmall,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (medicine.dosage != null)
            Text(
              medicine.dosage!,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.grey600,
              ),
            ),
          Row(
            children: [
              Text(
                'Qté: ${medicine.quantity}',
                style: AppTextStyles.labelSmall.copyWith(
                  color: AppColors.grey500,
                ),
              ),
              if (medicine.locationName != null) ...[
                const SizedBox(width: 8),
                Icon(
                  Icons.location_on,
                  size: 12,
                  color: AppColors.grey500,
                ),
                const SizedBox(width: 2),
                Expanded(
                  child: Text(
                    medicine.locationName!,
                    style: AppTextStyles.labelSmall.copyWith(
                      color: AppColors.grey500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: AppColors.grey400,
      ),
    );
  }

  Color _getStatusColor() {
    switch (medicine.status) {
      case MedicineStatus.expired:
        return AppColors.error;
      case MedicineStatus.expiringSoon:
        return AppColors.warning;
      case MedicineStatus.lowStock:
        return AppColors.warning;
      case MedicineStatus.outOfStock:
        return AppColors.error;
      case MedicineStatus.normal:
        return AppColors.success;
    }
  }
}

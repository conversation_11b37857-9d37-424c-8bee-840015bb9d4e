import '../../domain/entities/dashboard_stats.dart';

class DashboardStatsModel extends DashboardStats {
  const DashboardStatsModel({
    super.total,
    super.expired,
    super.expiringSoon,
    super.lowStock,
    super.locationCount,
    super.totalLocations,
    super.tagCount,
    super.totalTags,
    super.locationUsagePercentage,
    super.tagUsagePercentage,
  });

  factory DashboardStatsModel.fromJson(Map<String, dynamic> json) {
    return DashboardStatsModel(
      total: json['total'] as int? ?? 0,
      expired: json['expired'] as int? ?? 0,
      expiringSoon: json['expiring_soon'] as int? ?? 0,
      lowStock: json['low_stock'] as int? ?? 0,
      locationCount: json['location_count'] as int? ?? 0,
      totalLocations: json['total_locations'] as int? ?? 0,
      tagCount: json['tag_count'] as int? ?? 0,
      totalTags: json['total_tags'] as int? ?? 7,
      locationUsagePercentage: json['location_usage_percentage'] as int? ?? 0,
      tagUsagePercentage: json['tag_usage_percentage'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'expired': expired,
      'expiring_soon': expiringSoon,
      'low_stock': lowStock,
      'location_count': locationCount,
      'total_locations': totalLocations,
      'tag_count': tagCount,
      'total_tags': totalTags,
      'location_usage_percentage': locationUsagePercentage,
      'tag_usage_percentage': tagUsagePercentage,
    };
  }

  factory DashboardStatsModel.fromEntity(DashboardStats stats) {
    return DashboardStatsModel(
      total: stats.total,
      expired: stats.expired,
      expiringSoon: stats.expiringSoon,
      lowStock: stats.lowStock,
      locationCount: stats.locationCount,
      totalLocations: stats.totalLocations,
      tagCount: stats.tagCount,
      totalTags: stats.totalTags,
      locationUsagePercentage: stats.locationUsagePercentage,
      tagUsagePercentage: stats.tagUsagePercentage,
    );
  }
}

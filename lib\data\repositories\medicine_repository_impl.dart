import 'package:dartz/dartz.dart';

import '../../domain/entities/medicine.dart';
import '../../domain/repositories/medicine_repository.dart';
import '../../core/error/failures.dart';
import '../../core/network/network_info.dart';
import '../datasources/medicine_remote_data_source.dart';
import '../models/medicine_model.dart';

class MedicineRepositoryImpl implements MedicineRepository {
  final MedicineRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  MedicineRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<Medicine>>> getMedicines(
      String householdId) async {
    if (await networkInfo.isConnected) {
      try {
        final medicines = await remoteDataSource.getMedicines(householdId);
        return Right(medicines);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Medicine>> getMedicineById(String medicineId) async {
    if (await networkInfo.isConnected) {
      try {
        final medicine = await remoteDataSource.getMedicineById(medicineId);
        return Right(medicine);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Medicine>> addMedicine(Medicine medicine) async {
    if (await networkInfo.isConnected) {
      try {
        final medicineModel = MedicineModel.fromEntity(medicine);
        final result = await remoteDataSource.addMedicine(medicineModel);
        return Right(result);
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteMedicine(String medicineId) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.deleteMedicine(medicineId);
        return const Right(null);
      } catch (e) {
        return Left(
            ServerFailure('Failed to delete medicine: ${e.toString()}'));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Medicine>> updateMedicine(Medicine medicine) async {
    if (await networkInfo.isConnected) {
      try {
        final medicineModel = MedicineModel.fromEntity(medicine);
        final result = await remoteDataSource.updateMedicine(medicineModel);
        return Right(result.toEntity());
      } catch (e) {
        return Left(
            ServerFailure('Failed to update medicine: ${e.toString()}'));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<Medicine>>> searchMedicines(
      String householdId, String query) async {
    if (await networkInfo.isConnected) {
      try {
        final medicines =
            await remoteDataSource.searchMedicines(householdId, query);
        return Right(medicines.map((model) => model.toEntity()).toList());
      } catch (e) {
        return Left(
            ServerFailure('Failed to search medicines: ${e.toString()}'));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<Medicine>>> getRecentMedicines(String householdId,
      {int limit = 5}) async {
    if (await networkInfo.isConnected) {
      try {
        final medicines = await remoteDataSource.getRecentMedicines(householdId,
            limit: limit);
        return Right(medicines.map((model) => model.toEntity()).toList());
      } catch (e) {
        return Left(
            ServerFailure('Failed to get recent medicines: ${e.toString()}'));
      }
    } else {
      return const Left(NetworkFailure('No internet connection'));
    }
  }
}

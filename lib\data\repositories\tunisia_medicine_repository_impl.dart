import 'package:injectable/injectable.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../domain/entities/tunisia_medicine.dart';
import '../../domain/repositories/tunisia_medicine_repository.dart';
import '../models/tunisia_medicine_model.dart';

@Injectable(as: TunisiaMedicineRepository)
class TunisiaMedicineRepositoryImpl implements TunisiaMedicineRepository {
  final SupabaseClient _supabaseClient;

  TunisiaMedicineRepositoryImpl(this._supabaseClient);

  @override
  Future<List<TunisiaMedicine>> searchMedicines(String query,
      {int limit = 10}) async {
    try {
      // Use ILIKE for case-insensitive search across multiple fields
      final response = await _supabaseClient
          .from('tunisia_medicines')
          .select()
          .or('nom.ilike.%$query%,dci.ilike.%$query%,laboratoire.ilike.%$query%')
          .order('nom')
          .limit(limit);

      final List<dynamic> data = response as List<dynamic>;

      return data
          .map((json) =>
              TunisiaMedicineModel.fromJson(json as Map<String, dynamic>))
          .map((model) => model.toEntity())
          .toList();
    } catch (e) {
      throw Exception(
          'Erreur lors de la recherche de médicaments: ${e.toString()}');
    }
  }

  @override
  Future<TunisiaMedicine?> getMedicineById(String id) async {
    try {
      final response = await _supabaseClient
          .from('tunisia_medicines')
          .select()
          .eq('id', id)
          .maybeSingle();

      if (response == null) {
        return null;
      }

      final model = TunisiaMedicineModel.fromJson(response);
      return model.toEntity();
    } catch (e) {
      throw Exception(
          'Erreur lors de la récupération du médicament: ${e.toString()}');
    }
  }
}

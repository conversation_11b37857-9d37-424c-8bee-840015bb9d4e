// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appTitle => 'MedyTrack';

  @override
  String get dashboard => 'Tableau de bord';

  @override
  String get medicines => 'Médicaments';

  @override
  String get myMedicines => 'Mes médicaments';

  @override
  String get alerts => 'Alertes';

  @override
  String get reminders => 'Rappels';

  @override
  String get settings => 'Paramètres';

  @override
  String get profile => 'Profil';

  @override
  String get locations => 'Emplacements';

  @override
  String get family => 'Famille';

  @override
  String get notificationSettings => 'Paramètres de notification';

  @override
  String get languageSettings => 'Paramètres de langue';

  @override
  String get profileSecurity => 'Profil et sécurité';

  @override
  String get personalization => 'Personnalisation';

  @override
  String get dataManagement => 'Gestion des données';

  @override
  String get expiryAlerts => 'Alertes d\'expiration';

  @override
  String get lowStockAlerts => 'Alertes de stock faible';

  @override
  String get medicationReminders => 'Rappels de médicaments';

  @override
  String get pushNotifications => 'Notifications push';

  @override
  String get emailNotifications => 'Notifications par email';

  @override
  String get enabled => 'Activé';

  @override
  String get disabled => 'Désactivé';

  @override
  String get activated => 'Activées';

  @override
  String get deactivated => 'Désactivées';

  @override
  String get language => 'Langue';

  @override
  String get french => 'Français';

  @override
  String get english => 'Anglais';

  @override
  String get arabic => 'Arabe';

  @override
  String get darkMode => 'Mode sombre';

  @override
  String get dateFormat => 'Format de date';

  @override
  String get autoSync => 'Synchronisation automatique';

  @override
  String get testNotifications => 'Tester les notifications';

  @override
  String get sendTestNotification => 'Envoyer une notification de test';

  @override
  String get test => 'Test';

  @override
  String get testNotificationSent => 'Notification de test envoyée!';

  @override
  String get error => 'Erreur';

  @override
  String get expiryThreshold => 'Seuil d\'expiration';

  @override
  String get lowStockThreshold => 'Seuil de stock faible';

  @override
  String daysBeforeExpiry(int days) {
    return '$days jours avant expiration';
  }

  @override
  String unitsRemaining(int units) {
    return '$units unités ou moins';
  }

  @override
  String get save => 'Enregistrer';

  @override
  String get cancel => 'Annuler';

  @override
  String get ok => 'OK';

  @override
  String get addReminder => 'Ajouter un rappel';

  @override
  String get editReminder => 'Modifier le rappel';

  @override
  String get deleteReminder => 'Supprimer le rappel';

  @override
  String get reminderSettings => 'Paramètres des rappels';

  @override
  String get activeReminders => 'Rappels actifs';

  @override
  String get medicinesWithoutReminders => 'Médicaments sans rappels';

  @override
  String get noActiveReminders => 'Aucun rappel actif';

  @override
  String get noMedicinesWithoutReminders =>
      'Tous les médicaments ont des rappels';

  @override
  String get reminderTime => 'Heure du rappel';

  @override
  String get reminderFrequency => 'Fréquence';

  @override
  String get daily => 'Quotidien';

  @override
  String get weekly => 'Hebdomadaire';

  @override
  String get hourlyInterval => 'Intervalle horaire';

  @override
  String get selectDays => 'Sélectionner les jours';

  @override
  String get intervalHours => 'Intervalle (heures)';

  @override
  String get startDate => 'Date de début';

  @override
  String get endDate => 'Date de fin';

  @override
  String get isActive => 'Actif';

  @override
  String get reminderAdded => 'Rappel ajouté avec succès';

  @override
  String get reminderUpdated => 'Rappel mis à jour avec succès';

  @override
  String get reminderDeleted => 'Rappel supprimé avec succès';

  @override
  String get medicineReminderTitle => '💊 Rappel de médicament';

  @override
  String timeToTakeMedicine(String medicineName) {
    return 'Il est temps de prendre $medicineName';
  }

  @override
  String everyXHours(int hours) {
    return 'toutes les $hours heures';
  }

  @override
  String get loadingError => 'Erreur de chargement';

  @override
  String get featureComingSoon =>
      'Cette fonctionnalité sera disponible dans une prochaine version.';

  @override
  String get preferences => 'Préférences';

  @override
  String get familyMembers => 'Membres de la famille';

  @override
  String get storageLocations => 'Emplacements de stockage';

  @override
  String get tags => 'Étiquettes';

  @override
  String get dangerZone => 'Zone de danger';

  @override
  String get security => 'Sécurité';

  @override
  String get minimumExpiryThreshold => 'Seuil d\'expiration minimum';

  @override
  String get defaultLocation => 'Emplacement par défaut';

  @override
  String get defaultFamilyMember => 'Membre de famille par défaut';

  @override
  String get showExpiredMedicines => 'Afficher les médicaments expirés';

  @override
  String get groupByLocation => 'Grouper par emplacement';

  @override
  String get addMember => 'Ajouter un membre';

  @override
  String get editMember => 'Modifier le membre';

  @override
  String get addLocation => 'Ajouter un emplacement';

  @override
  String get editLocation => 'Modifier l\'emplacement';

  @override
  String get addTag => 'Ajouter une étiquette';

  @override
  String get editTag => 'Modifier l\'étiquette';

  @override
  String get name => 'Nom';

  @override
  String get description => 'Description';

  @override
  String get relationship => 'Relation';

  @override
  String get category => 'Catégorie';

  @override
  String get color => 'Couleur';

  @override
  String get none => 'Aucun';

  @override
  String get days => 'jours';

  @override
  String get units => 'unités';

  @override
  String get changePassword => 'Changer le mot de passe';

  @override
  String get currentPassword => 'Mot de passe actuel';

  @override
  String get newPassword => 'Nouveau mot de passe';

  @override
  String get confirmPassword => 'Confirmer le mot de passe';

  @override
  String get updatePassword => 'Mettre à jour le mot de passe';

  @override
  String get deleteAccount => 'Supprimer le compte';

  @override
  String get accountDeletion => 'Suppression du compte';

  @override
  String get personalInformation => 'Informations personnelles';

  @override
  String get email => 'Email';

  @override
  String get updateProfile => 'Mettre à jour le profil';

  @override
  String get avatar => 'Avatar';

  @override
  String get uploadPhoto => 'Télécharger une photo';

  @override
  String get removePhoto => 'Supprimer la photo';
}

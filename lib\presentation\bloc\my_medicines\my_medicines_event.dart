import 'package:equatable/equatable.dart';

/// Base class for MyMedicines events
abstract class MyMedicinesEvent extends Equatable {
  const MyMedicinesEvent();

  @override
  List<Object?> get props => [];
}

/// Initialize medicines for a household
class MyMedicinesInitialized extends MyMedicinesEvent {
  final String householdId;

  const MyMedicinesInitialized({required this.householdId});

  @override
  List<Object?> get props => [householdId];
}

/// Refresh medicines
class MyMedicinesRefreshed extends MyMedicinesEvent {
  const MyMedicinesRefreshed();
}

/// Search medicines
class MyMedicinesSearched extends MyMedicinesEvent {
  final String query;

  const MyMedicinesSearched({required this.query});

  @override
  List<Object?> get props => [query];
}

/// Clear search
class MyMedicinesSearchCleared extends MyMedicinesEvent {
  const MyMedicinesSearchCleared();
}

/// Filter medicines
class MyMedicinesFiltered extends MyMedicinesEvent {
  final MedicineFilter filter;

  const MyMedicinesFiltered({required this.filter});

  @override
  List<Object?> get props => [filter];
}

/// Sort medicines
class MyMedicinesSorted extends MyMedicinesEvent {
  final MedicineSortOption sortOption;

  const MyMedicinesSorted({required this.sortOption});

  @override
  List<Object?> get props => [sortOption];
}

/// Update medicine quantity
class MedicineQuantityUpdated extends MyMedicinesEvent {
  final String medicineId;
  final int newQuantity;

  const MedicineQuantityUpdated({
    required this.medicineId,
    required this.newQuantity,
  });

  @override
  List<Object?> get props => [medicineId, newQuantity];
}

/// Delete medicine
class MedicineDeleted extends MyMedicinesEvent {
  final String medicineId;

  const MedicineDeleted({required this.medicineId});

  @override
  List<Object?> get props => [medicineId];
}

/// Select medicine for bulk operations
class MedicineSelected extends MyMedicinesEvent {
  final String medicineId;
  final bool isSelected;

  const MedicineSelected({
    required this.medicineId,
    required this.isSelected,
  });

  @override
  List<Object?> get props => [medicineId, isSelected];
}

/// Select all medicines
class AllMedicinesSelected extends MyMedicinesEvent {
  final bool selectAll;

  const AllMedicinesSelected({required this.selectAll});

  @override
  List<Object?> get props => [selectAll];
}

/// Clear selection
class MedicineSelectionCleared extends MyMedicinesEvent {
  const MedicineSelectionCleared();
}

/// Bulk delete selected medicines
class SelectedMedicinesDeleted extends MyMedicinesEvent {
  const SelectedMedicinesDeleted();
}

/// Toggle view mode (list/grid)
class ViewModeToggled extends MyMedicinesEvent {
  final MedicineViewMode viewMode;

  const ViewModeToggled({required this.viewMode});

  @override
  List<Object?> get props => [viewMode];
}

/// Filter options for medicines
enum MedicineFilter {
  all,
  expired,
  expiringSoon,
  lowStock,
  outOfStock,
  custom,
  prescription,
  byLocation,
  byFamilyMember,
}

/// Sort options for medicines
enum MedicineSortOption {
  nameAsc,
  nameDesc,
  expirationAsc,
  expirationDesc,
  quantityAsc,
  quantityDesc,
  createdAtAsc,
  createdAtDesc,
  locationAsc,
  locationDesc,
}

/// View mode options
enum MedicineViewMode {
  list,
  grid,
}

/// Extension for filter display names
extension MedicineFilterExtension on MedicineFilter {
  String get displayName {
    switch (this) {
      case MedicineFilter.all:
        return 'Tous';
      case MedicineFilter.expired:
        return 'Expirés';
      case MedicineFilter.expiringSoon:
        return 'Expire bientôt';
      case MedicineFilter.lowStock:
        return 'Stock faible';
      case MedicineFilter.outOfStock:
        return 'Rupture de stock';
      case MedicineFilter.custom:
        return 'Personnalisés';
      case MedicineFilter.prescription:
        return 'Prescription';
      case MedicineFilter.byLocation:
        return 'Par lieu';
      case MedicineFilter.byFamilyMember:
        return 'Par membre';
    }
  }

  String get icon {
    switch (this) {
      case MedicineFilter.all:
        return 'medication';
      case MedicineFilter.expired:
        return 'error';
      case MedicineFilter.expiringSoon:
        return 'warning';
      case MedicineFilter.lowStock:
        return 'inventory_2';
      case MedicineFilter.outOfStock:
        return 'remove_circle';
      case MedicineFilter.custom:
        return 'edit';
      case MedicineFilter.prescription:
        return 'local_pharmacy';
      case MedicineFilter.byLocation:
        return 'location_on';
      case MedicineFilter.byFamilyMember:
        return 'people';
    }
  }
}

/// Extension for sort option display names
extension MedicineSortOptionExtension on MedicineSortOption {
  String get displayName {
    switch (this) {
      case MedicineSortOption.nameAsc:
        return 'Nom (A-Z)';
      case MedicineSortOption.nameDesc:
        return 'Nom (Z-A)';
      case MedicineSortOption.expirationAsc:
        return 'Expiration (plus proche)';
      case MedicineSortOption.expirationDesc:
        return 'Expiration (plus loin)';
      case MedicineSortOption.quantityAsc:
        return 'Quantité (croissant)';
      case MedicineSortOption.quantityDesc:
        return 'Quantité (décroissant)';
      case MedicineSortOption.createdAtAsc:
        return 'Ajouté (plus ancien)';
      case MedicineSortOption.createdAtDesc:
        return 'Ajouté (plus récent)';
      case MedicineSortOption.locationAsc:
        return 'Emplacement (A-Z)';
      case MedicineSortOption.locationDesc:
        return 'Emplacement (Z-A)';
    }
  }

  String get icon {
    switch (this) {
      case MedicineSortOption.nameAsc:
      case MedicineSortOption.nameDesc:
        return 'sort_by_alpha';
      case MedicineSortOption.expirationAsc:
      case MedicineSortOption.expirationDesc:
        return 'schedule';
      case MedicineSortOption.quantityAsc:
      case MedicineSortOption.quantityDesc:
        return 'numbers';
      case MedicineSortOption.createdAtAsc:
      case MedicineSortOption.createdAtDesc:
        return 'access_time';
      case MedicineSortOption.locationAsc:
      case MedicineSortOption.locationDesc:
        return 'location_on';
    }
  }

  bool get isAscending {
    switch (this) {
      case MedicineSortOption.nameAsc:
      case MedicineSortOption.expirationAsc:
      case MedicineSortOption.quantityAsc:
      case MedicineSortOption.createdAtAsc:
      case MedicineSortOption.locationAsc:
        return true;
      case MedicineSortOption.nameDesc:
      case MedicineSortOption.expirationDesc:
      case MedicineSortOption.quantityDesc:
      case MedicineSortOption.createdAtDesc:
      case MedicineSortOption.locationDesc:
        return false;
    }
  }
}

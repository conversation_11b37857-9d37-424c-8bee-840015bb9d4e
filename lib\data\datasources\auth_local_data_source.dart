import '../models/user_model.dart';
import '../../core/storage/local_storage.dart';
import '../../core/storage/secure_storage.dart';
import '../../core/config/app_config.dart';

abstract class AuthLocalDataSource {
  Future<UserModel?> getCachedUser();
  Future<void> cacheUser(UserModel user);
  Future<void> clearCachedUser();
  Future<String?> getToken();
  Future<void> cacheToken(String token);
  Future<void> clearToken();
}

class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final LocalStorage localStorage;
  final SecureStorage secureStorage;

  AuthLocalDataSourceImpl({
    required this.localStorage,
    required this.secureStorage,
  });

  @override
  Future<UserModel?> getCachedUser() async {
    try {
      final userMap = await localStorage.getObject(AppConfig.userProfileKey);
      if (userMap == null) return null;
      
      return UserModel.fromJson(userMap);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> cacheUser(UserModel user) async {
    await localStorage.setObject(AppConfig.userProfileKey, user.toJson());
  }

  @override
  Future<void> clearCachedUser() async {
    await localStorage.remove(AppConfig.userProfileKey);
  }

  @override
  Future<String?> getToken() async {
    return await secureStorage.getString(AppConfig.userTokenKey);
  }

  @override
  Future<void> cacheToken(String token) async {
    await secureStorage.setString(AppConfig.userTokenKey, token);
  }

  @override
  Future<void> clearToken() async {
    await secureStorage.remove(AppConfig.userTokenKey);
  }
}

import 'package:dartz/dartz.dart';
import '../../entities/user.dart';
import '../../repositories/profile_repository.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';

class UpdateProfileNameParams {
  final String name;

  UpdateProfileNameParams({required this.name});
}

class UpdateProfileNameUseCase implements UseCase<User, UpdateProfileNameParams> {
  final ProfileRepository repository;

  UpdateProfileNameUseCase(this.repository);

  @override
  Future<Either<Failure, User>> call(UpdateProfileNameParams params) async {
    return await repository.updateProfileName(params.name);
  }
}

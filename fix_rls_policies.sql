-- Fix RLS policies for reminders table
-- This script should be run in Supabase SQL Editor

-- First, check if the reminders table exists and has RLS enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'reminders';

-- Check existing policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'reminders';

-- Drop existing policies if they exist (to recreate them)
DROP POLICY IF EXISTS "Users can view their own reminders" ON public.reminders;
DROP POLICY IF EXISTS "Users can insert their own reminders" ON public.reminders;
DROP POLICY IF EXISTS "Users can update their own reminders" ON public.reminders;
DROP POLICY IF EXISTS "Users can delete their own reminders" ON public.reminders;

-- Ensure RLS is enabled
ALTER TABLE public.reminders ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for reminders table
CREATE POLICY "Users can view their own reminders" ON public.reminders
    FOR SELECT USING (
        user_medicine_id IN (
            SELECT um.id FROM public.user_medicines um
            WHERE um.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own reminders" ON public.reminders
    FOR INSERT WITH CHECK (
        user_medicine_id IN (
            SELECT um.id FROM public.user_medicines um
            WHERE um.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own reminders" ON public.reminders
    FOR UPDATE USING (
        user_medicine_id IN (
            SELECT um.id FROM public.user_medicines um
            WHERE um.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own reminders" ON public.reminders
    FOR DELETE USING (
        user_medicine_id IN (
            SELECT um.id FROM public.user_medicines um
            WHERE um.user_id = auth.uid()
        )
    );

-- Grant necessary permissions
GRANT ALL ON public.reminders TO authenticated;

-- Test query to verify the policy works
-- This should return user_medicines for the authenticated user
SELECT um.id, um.user_id, um.custom_name
FROM public.user_medicines um
WHERE um.user_id = auth.uid()
LIMIT 5;

-- Verify policies are created
SELECT schemaname, tablename, policyname, permissive, roles, cmd 
FROM pg_policies 
WHERE tablename = 'reminders';

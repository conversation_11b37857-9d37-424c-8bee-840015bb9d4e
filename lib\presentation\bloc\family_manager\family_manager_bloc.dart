import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../../domain/entities/family_member.dart';
import '../../../domain/repositories/family_member_repository.dart';
import 'family_manager_event.dart';
import 'family_manager_state.dart';

@injectable
class FamilyManagerBloc extends Bloc<FamilyManagerEvent, FamilyManagerState> {
  final FamilyMemberRepository _familyMemberRepository;
  StreamSubscription<List<FamilyMember>>? _membersSubscription;
  Timer? _searchDebounceTimer;

  FamilyManagerBloc(this._familyMemberRepository)
      : super(const FamilyManagerInitial()) {
    on<FamilyManagerInitialized>(_onFamilyManagerInitialized);
    on<FamilyMembersRefreshed>(_onFamilyMembersRefreshed);
    on<FamilyMemberCreated>(_onFamilyMemberCreated);
    on<FamilyMemberUpdated>(_onFamilyMemberUpdated);
    on<FamilyMemberDeleted>(_onFamilyMemberDeleted);
    on<FamilyMembersSearched>(_onFamilyMembersSearched);
    on<FamilyMemberSearchCleared>(_onFamilyMemberSearchCleared);
    on<FamilyMembersFiltered>(_onFamilyMembersFiltered);
    on<FamilyMembersSorted>(_onFamilyMembersSorted);
    on<FamilyMemberSelected>(_onFamilyMemberSelected);
    on<FamilyMemberSelectionCleared>(_onFamilyMemberSelectionCleared);
    on<FamilyMemberFormValidated>(_onFamilyMemberFormValidated);
    on<FamilyMemberFormReset>(_onFamilyMemberFormReset);
  }

  @override
  Future<void> close() {
    _membersSubscription?.cancel();
    _searchDebounceTimer?.cancel();
    return super.close();
  }

  Future<void> _onFamilyManagerInitialized(
    FamilyManagerInitialized event,
    Emitter<FamilyManagerState> emit,
  ) async {
    emit(const FamilyManagerLoading());

    try {
      // Cancel previous subscription
      await _membersSubscription?.cancel();

      // Use emit.forEach to properly handle stream emissions
      await emit.forEach<List<FamilyMember>>(
        _familyMemberRepository.getHouseholdMembers(event.householdId),
        onData: (members) {
          final filteredMembers = _applyFiltersAndSort(
            members,
            '',
            null,
            FamilyMemberSortOption.nameAsc,
          );

          return FamilyManagerLoaded(
            householdId: event.householdId,
            members: members,
            filteredMembers: filteredMembers,
          );
        },
        onError: (error, stackTrace) => FamilyManagerError(
          message: 'Erreur lors du chargement des membres: ${error.toString()}',
        ),
      );
    } catch (e) {
      if (!emit.isDone) {
        emit(FamilyManagerError(
          message: 'Erreur lors de l\'initialisation: ${e.toString()}',
        ));
      }
    }
  }

  Future<void> _onFamilyMembersRefreshed(
    FamilyMembersRefreshed event,
    Emitter<FamilyManagerState> emit,
  ) async {
    final currentState = state;
    if (currentState is FamilyManagerLoaded) {
      emit(const FamilyManagerLoading());
      // The stream subscription will automatically emit new data
    }
  }

  Future<void> _onFamilyMemberCreated(
    FamilyMemberCreated event,
    Emitter<FamilyManagerState> emit,
  ) async {
    try {
      await _familyMemberRepository.createFamilyMember(event.member);
      emit(const FamilyManagerOperationSuccess(
        message: 'Membre de la famille ajouté avec succès',
        operationType: FamilyManagerOperationType.memberCreated,
      ));
      // The stream subscription will automatically refresh the list
    } catch (e) {
      emit(FamilyManagerError(
        message: 'Erreur lors de l\'ajout: ${e.toString()}',
      ));
    }
  }

  Future<void> _onFamilyMemberUpdated(
    FamilyMemberUpdated event,
    Emitter<FamilyManagerState> emit,
  ) async {
    try {
      await _familyMemberRepository.updateFamilyMember(event.member);
      emit(const FamilyManagerOperationSuccess(
        message: 'Membre de la famille mis à jour avec succès',
        operationType: FamilyManagerOperationType.memberUpdated,
      ));
      // The stream subscription will automatically refresh the list
    } catch (e) {
      emit(FamilyManagerError(
        message: 'Erreur lors de la mise à jour: ${e.toString()}',
      ));
    }
  }

  Future<void> _onFamilyMemberDeleted(
    FamilyMemberDeleted event,
    Emitter<FamilyManagerState> emit,
  ) async {
    try {
      await _familyMemberRepository.deleteFamilyMember(event.memberId);
      emit(const FamilyManagerOperationSuccess(
        message: 'Membre de la famille supprimé avec succès',
        operationType: FamilyManagerOperationType.memberDeleted,
      ));
    } catch (e) {
      emit(FamilyManagerError(
        message: 'Erreur lors de la suppression: ${e.toString()}',
      ));
    }
  }

  Future<void> _onFamilyMembersSearched(
    FamilyMembersSearched event,
    Emitter<FamilyManagerState> emit,
  ) async {
    final currentState = state;
    if (currentState is FamilyManagerLoaded) {
      // Cancel previous timer
      _searchDebounceTimer?.cancel();

      // Set searching state immediately
      emit(currentState.copyWith(isSearching: true));

      // Debounce search with 300ms delay
      _searchDebounceTimer = Timer(const Duration(milliseconds: 300), () {
        if (!isClosed) {
          final filteredMembers = _applyFiltersAndSort(
            currentState.members,
            event.query,
            currentState.relationshipFilter,
            currentState.currentSort,
          );

          emit(currentState.copyWith(
            searchQuery: event.query,
            filteredMembers: filteredMembers,
            isSearching: false,
          ));
        }
      });
    }
  }

  Future<void> _onFamilyMemberSearchCleared(
    FamilyMemberSearchCleared event,
    Emitter<FamilyManagerState> emit,
  ) async {
    final currentState = state;
    if (currentState is FamilyManagerLoaded) {
      _searchDebounceTimer?.cancel();

      final filteredMembers = _applyFiltersAndSort(
        currentState.members,
        '',
        currentState.relationshipFilter,
        currentState.currentSort,
      );

      emit(currentState.copyWith(
        searchQuery: '',
        filteredMembers: filteredMembers,
        isSearching: false,
      ));
    }
  }

  Future<void> _onFamilyMembersFiltered(
    FamilyMembersFiltered event,
    Emitter<FamilyManagerState> emit,
  ) async {
    final currentState = state;
    if (currentState is FamilyManagerLoaded) {
      final filteredMembers = _applyFiltersAndSort(
        currentState.members,
        currentState.searchQuery,
        event.relationship,
        currentState.currentSort,
      );

      emit(currentState.copyWith(
        relationshipFilter: event.relationship,
        clearRelationshipFilter: event.relationship == null,
        filteredMembers: filteredMembers,
      ));
    }
  }

  Future<void> _onFamilyMembersSorted(
    FamilyMembersSorted event,
    Emitter<FamilyManagerState> emit,
  ) async {
    final currentState = state;
    if (currentState is FamilyManagerLoaded) {
      final filteredMembers = _applyFiltersAndSort(
        currentState.members,
        currentState.searchQuery,
        currentState.relationshipFilter,
        event.sortOption,
      );

      emit(currentState.copyWith(
        currentSort: event.sortOption,
        filteredMembers: filteredMembers,
      ));
    }
  }

  Future<void> _onFamilyMemberSelected(
    FamilyMemberSelected event,
    Emitter<FamilyManagerState> emit,
  ) async {
    final currentState = state;
    if (currentState is FamilyManagerLoaded) {
      emit(currentState.copyWith(selectedMember: event.member));
    }
  }

  Future<void> _onFamilyMemberSelectionCleared(
    FamilyMemberSelectionCleared event,
    Emitter<FamilyManagerState> emit,
  ) async {
    final currentState = state;
    if (currentState is FamilyManagerLoaded) {
      emit(currentState.copyWith(clearSelectedMember: true));
    }
  }

  Future<void> _onFamilyMemberFormValidated(
    FamilyMemberFormValidated event,
    Emitter<FamilyManagerState> emit,
  ) async {
    final errors = <String, String?>{};

    // Validate name
    if (event.name.trim().isEmpty) {
      errors['name'] = 'Le nom est requis';
    } else if (event.name.trim().length < 2) {
      errors['name'] = 'Le nom doit contenir au moins 2 caractères';
    } else if (event.name.trim().length > 50) {
      errors['name'] = 'Le nom ne peut pas dépasser 50 caractères';
    }

    // Validate date of birth (optional)
    if (event.dateOfBirth != null) {
      final now = DateTime.now();
      if (event.dateOfBirth!.isAfter(now)) {
        errors['dateOfBirth'] =
            'La date de naissance ne peut pas être dans le futur';
      }

      final age = now.difference(event.dateOfBirth!).inDays ~/ 365;
      if (age > 150) {
        errors['dateOfBirth'] = 'Âge non valide (plus de 150 ans)';
      }
    }

    // Validate notes (optional)
    if (event.notes != null && event.notes!.length > 500) {
      errors['notes'] = 'Les notes ne peuvent pas dépasser 500 caractères';
    }

    final isValid = errors.values.every((error) => error == null);

    emit(FamilyMemberFormValidation(
      errors: errors,
      isValid: isValid,
    ));
  }

  Future<void> _onFamilyMemberFormReset(
    FamilyMemberFormReset event,
    Emitter<FamilyManagerState> emit,
  ) async {
    emit(const FamilyMemberFormValidation(
      errors: {},
      isValid: false,
    ));
  }

  /// Apply filters and sorting to family members list
  List<FamilyMember> _applyFiltersAndSort(
    List<FamilyMember> members,
    String searchQuery,
    String? relationshipFilter,
    FamilyMemberSortOption sortOption,
  ) {
    var filtered = List<FamilyMember>.from(members);

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      filtered = filtered.where((member) {
        return member.displayName.toLowerCase().contains(query) ||
            (member.notes?.toLowerCase().contains(query) ?? false) ||
            member.relationshipDisplayName.toLowerCase().contains(query);
      }).toList();
    }

    // Apply relationship filter
    if (relationshipFilter != null) {
      filtered = filtered.where((member) {
        return member.relationship == relationshipFilter;
      }).toList();
    }

    // Apply sorting
    filtered = _applySorting(filtered, sortOption);

    return filtered;
  }

  /// Apply sorting to family members
  List<FamilyMember> _applySorting(
      List<FamilyMember> members, FamilyMemberSortOption sortOption) {
    final sorted = List<FamilyMember>.from(members);

    switch (sortOption) {
      case FamilyMemberSortOption.nameAsc:
        sorted.sort((a, b) => a.displayName.compareTo(b.displayName));
        break;
      case FamilyMemberSortOption.nameDesc:
        sorted.sort((a, b) => b.displayName.compareTo(a.displayName));
        break;
      case FamilyMemberSortOption.ageAsc:
        sorted.sort((a, b) {
          final ageA = a.age ?? -1;
          final ageB = b.age ?? -1;
          return ageA.compareTo(ageB);
        });
        break;
      case FamilyMemberSortOption.ageDesc:
        sorted.sort((a, b) {
          final ageA = a.age ?? -1;
          final ageB = b.age ?? -1;
          return ageB.compareTo(ageA);
        });
        break;
      case FamilyMemberSortOption.relationshipAsc:
        sorted.sort((a, b) =>
            a.relationshipDisplayName.compareTo(b.relationshipDisplayName));
        break;
      case FamilyMemberSortOption.relationshipDesc:
        sorted.sort((a, b) =>
            b.relationshipDisplayName.compareTo(a.relationshipDisplayName));
        break;
      case FamilyMemberSortOption.createdAtAsc:
        sorted.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case FamilyMemberSortOption.createdAtDesc:
        sorted.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
    }

    return sorted;
  }
}

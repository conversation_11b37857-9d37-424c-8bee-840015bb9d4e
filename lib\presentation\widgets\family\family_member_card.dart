import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/family_member.dart';

class FamilyMemberCard extends StatelessWidget {
  final FamilyMember member;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const FamilyMemberCard({
    super.key,
    required this.member,
    this.onTap,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Avatar with gender-based icon
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: _getAvatarColor().withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(28),
                ),
                child: Icon(
                  _getGenderIcon(),
                  color: _getAvatarColor(),
                  size: 28,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Member details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name and age
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            member.displayName,
                            style: AppTextStyles.titleMedium.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (member.age != null) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: _getAgeGroupColor().withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '${member.age} ans',
                              style: AppTextStyles.labelSmall.copyWith(
                                color: _getAgeGroupColor(),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // Relationship
                    Text(
                      member.relationshipDisplayName,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.grey600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Additional info row
                    Row(
                      children: [
                        // Gender indicator
                        if (member.gender != null) ...[
                          Icon(
                            _getGenderIcon(),
                            size: 14,
                            color: AppColors.grey500,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _getGenderDisplayName(),
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.grey500,
                            ),
                          ),
                        ],
                        
                        // Age group indicator
                        if (member.age != null) ...[
                          if (member.gender != null) ...[
                            Text(
                              ' • ',
                              style: AppTextStyles.bodySmall.copyWith(
                                color: AppColors.grey500,
                              ),
                            ),
                          ],
                          Icon(
                            _getAgeGroupIcon(),
                            size: 14,
                            color: AppColors.grey500,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _getAgeGroupDisplayName(),
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.grey500,
                            ),
                          ),
                        ],
                        
                        const Spacer(),
                        
                        // Creation date
                        Icon(
                          Icons.access_time,
                          size: 14,
                          color: AppColors.grey500,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _formatDate(member.createdAt),
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.grey500,
                          ),
                        ),
                      ],
                    ),
                    
                    // Notes (if any)
                    if (member.notes != null && member.notes!.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.grey100,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.note,
                              size: 14,
                              color: AppColors.grey600,
                            ),
                            const SizedBox(width: 6),
                            Expanded(
                              child: Text(
                                member.notes!,
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: AppColors.grey600,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              
              // Action menu
              if (onEdit != null || onDelete != null)
                PopupMenuButton<String>(
                  icon: Icon(
                    Icons.more_vert,
                    color: AppColors.grey600,
                    size: 20,
                  ),
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        onEdit?.call();
                        break;
                      case 'delete':
                        onDelete?.call();
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    if (onEdit != null)
                      PopupMenuItem<String>(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(
                              Icons.edit_outlined,
                              size: 18,
                              color: AppColors.grey600,
                            ),
                            const SizedBox(width: 8),
                            const Text('Modifier'),
                          ],
                        ),
                      ),
                    if (onDelete != null)
                      PopupMenuItem<String>(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(
                              Icons.delete_outline,
                              size: 18,
                              color: AppColors.error,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Supprimer',
                              style: TextStyle(color: AppColors.error),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Get gender-based icon
  IconData _getGenderIcon() {
    switch (member.gender?.toLowerCase()) {
      case 'male':
      case 'homme':
      case 'masculin':
        return Icons.male;
      case 'female':
      case 'femme':
      case 'féminin':
        return Icons.female;
      default:
        return Icons.person;
    }
  }

  /// Get gender display name
  String _getGenderDisplayName() {
    switch (member.gender?.toLowerCase()) {
      case 'male':
      case 'homme':
      case 'masculin':
        return 'Homme';
      case 'female':
      case 'femme':
      case 'féminin':
        return 'Femme';
      default:
        return 'Non spécifié';
    }
  }

  /// Get avatar color based on gender
  Color _getAvatarColor() {
    switch (member.gender?.toLowerCase()) {
      case 'male':
      case 'homme':
      case 'masculin':
        return AppColors.blue;
      case 'female':
      case 'femme':
      case 'féminin':
        return Colors.pink;
      default:
        return AppColors.teal;
    }
  }

  /// Get age group icon
  IconData _getAgeGroupIcon() {
    final ageGroup = member.ageGroup;
    switch (ageGroup) {
      case 'infant':
        return Icons.child_care;
      case 'child':
      case 'adolescent':
        return Icons.child_friendly;
      case 'adult':
        return Icons.person;
      case 'senior':
        return Icons.elderly;
      default:
        return Icons.person;
    }
  }

  /// Get age group display name
  String _getAgeGroupDisplayName() {
    final ageGroup = member.ageGroup;
    switch (ageGroup) {
      case 'infant':
        return 'Bébé';
      case 'child':
        return 'Enfant';
      case 'adolescent':
        return 'Adolescent';
      case 'adult':
        return 'Adulte';
      case 'senior':
        return 'Senior';
      default:
        return 'Adulte';
    }
  }

  /// Get age group color
  Color _getAgeGroupColor() {
    final ageGroup = member.ageGroup;
    switch (ageGroup) {
      case 'infant':
        return Colors.pink;
      case 'child':
        return Colors.orange;
      case 'adolescent':
        return AppColors.purple;
      case 'adult':
        return AppColors.teal;
      case 'senior':
        return Colors.brown;
      default:
        return AppColors.teal;
    }
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Aujourd\'hui';
    } else if (difference.inDays == 1) {
      return 'Hier';
    } else if (difference.inDays < 7) {
      return 'Il y a ${difference.inDays} jours';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return 'Il y a $weeks semaine${weeks > 1 ? 's' : ''}';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return 'Il y a $months mois';
    } else {
      final years = (difference.inDays / 365).floor();
      return 'Il y a $years an${years > 1 ? 's' : ''}';
    }
  }
}

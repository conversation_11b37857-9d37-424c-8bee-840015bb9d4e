import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';

abstract class ProfileRemoteDataSource {
  Future<UserModel> updateProfileName(String name);
  Future<void> updateEmail(String email);
  Future<String> uploadAvatar(File imageFile);
  Future<void> removeAvatar();
  Future<String?> getAvatarUrl();
  Future<void> changePassword(String currentPassword, String newPassword);
  Future<void> deleteAccount(String password);
}

class ProfileRemoteDataSourceImpl implements ProfileRemoteDataSource {
  final SupabaseClient supabaseClient;

  ProfileRemoteDataSourceImpl({required this.supabaseClient});

  @override
  Future<UserModel> updateProfileName(String name) async {
    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        throw Exception('Aucun utilisateur connecté');
      }

      // Update profile name in profiles table
      await supabaseClient
          .from('profiles')
          .update({'name': name})
          .eq('id', user.id);

      // Fetch updated profile
      return await _fetchUserProfile(user.id);
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour du nom: ${e.toString()}');
    }
  }

  @override
  Future<void> updateEmail(String email) async {
    try {
      final response = await supabaseClient.auth.updateUser(
        UserAttributes(email: email),
      );

      if (response.user == null) {
        throw Exception('Erreur lors de la mise à jour de l\'email');
      }
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour de l\'email: ${e.toString()}');
    }
  }

  @override
  Future<String> uploadAvatar(File imageFile) async {
    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        throw Exception('Aucun utilisateur connecté');
      }

      final fileName = '${user.id}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final filePath = 'avatars/$fileName';

      // Upload image to Supabase Storage
      await supabaseClient.storage
          .from('avatars')
          .upload(filePath, imageFile);

      // Get public URL
      final publicUrl = supabaseClient.storage
          .from('avatars')
          .getPublicUrl(filePath);

      // Update profile with avatar URL
      await supabaseClient
          .from('profiles')
          .update({'avatar_url': publicUrl})
          .eq('id', user.id);

      return publicUrl;
    } catch (e) {
      throw Exception('Erreur lors du téléchargement de l\'avatar: ${e.toString()}');
    }
  }

  @override
  Future<void> removeAvatar() async {
    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        throw Exception('Aucun utilisateur connecté');
      }

      // Remove avatar URL from profile
      await supabaseClient
          .from('profiles')
          .update({'avatar_url': null})
          .eq('id', user.id);
    } catch (e) {
      throw Exception('Erreur lors de la suppression de l\'avatar: ${e.toString()}');
    }
  }

  @override
  Future<String?> getAvatarUrl() async {
    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        throw Exception('Aucun utilisateur connecté');
      }

      final response = await supabaseClient
          .from('profiles')
          .select('avatar_url')
          .eq('id', user.id)
          .maybeSingle();

      return response?['avatar_url'];
    } catch (e) {
      throw Exception('Erreur lors de la récupération de l\'avatar: ${e.toString()}');
    }
  }

  @override
  Future<void> changePassword(String currentPassword, String newPassword) async {
    try {
      final response = await supabaseClient.auth.updateUser(
        UserAttributes(password: newPassword),
      );

      if (response.user == null) {
        throw Exception('Erreur lors du changement de mot de passe');
      }
    } catch (e) {
      throw Exception('Erreur lors du changement de mot de passe: ${e.toString()}');
    }
  }

  @override
  Future<void> deleteAccount(String password) async {
    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        throw Exception('Aucun utilisateur connecté');
      }

      // Delete user data from all tables
      await supabaseClient.from('user_medicines').delete().eq('user_id', user.id);
      await supabaseClient.from('profiles').delete().eq('id', user.id);
      await supabaseClient.from('users').delete().eq('id', user.id);

      // Delete auth user
      await supabaseClient.auth.admin.deleteUser(user.id);
    } catch (e) {
      throw Exception('Erreur lors de la suppression du compte: ${e.toString()}');
    }
  }

  Future<UserModel> _fetchUserProfile(String userId) async {
    try {
      // Fetch profile data
      final profileResponse = await supabaseClient
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .maybeSingle();

      // Fetch user data
      final userResponse = await supabaseClient
          .from('users')
          .select('household_id, expiry_warning_days')
          .eq('id', userId)
          .maybeSingle();

      // Fetch household data if available
      String? householdName;
      if (userResponse?['household_id'] != null) {
        final householdResponse = await supabaseClient
            .from('households')
            .select('name')
            .eq('id', userResponse!['household_id'])
            .maybeSingle();
        
        householdName = householdResponse?['name'];
      }

      final user = supabaseClient.auth.currentUser!;

      return UserModel(
        id: userId,
        email: user.email!,
        name: profileResponse?['name'],
        householdId: userResponse?['household_id'],
        householdName: householdName,
        isOnboardingCompleted: profileResponse?['onboarding_completed'] ?? false,
        expiryWarningDays: userResponse?['expiry_warning_days'] ?? 1,
        createdAt: DateTime.tryParse(user.createdAt),
        updatedAt: DateTime.tryParse(user.updatedAt ?? ''),
      );
    } catch (e) {
      throw Exception('Erreur de récupération du profil: ${e.toString()}');
    }
  }
}

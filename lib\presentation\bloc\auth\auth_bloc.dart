import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide AuthState;
import '../../../domain/usecases/auth/get_current_user_usecase.dart';
import '../../../domain/usecases/auth/sign_in_usecase.dart';
import '../../../domain/usecases/auth/sign_up_usecase.dart';
import '../../../domain/usecases/auth/sign_out_usecase.dart';
import '../../../domain/usecases/auth/refresh_profile_usecase.dart';
import '../../../domain/usecases/auth/auth_params.dart';
import 'auth_event.dart';
import 'auth_state.dart' as auth_state;

/// Authentication BLoC matching web app AuthContext behavior
class AuthBloc extends Bloc<AuthEvent, auth_state.AuthState> {
  final GetCurrentUserUseCase getCurrentUserUseCase;
  final SignInUseCase signInUseCase;
  final SignUpUseCase signUpUseCase;
  final SignOutUseCase signOutUseCase;
  final RefreshProfileUseCase refreshProfileUseCase;

  AuthBloc({
    required this.getCurrentUserUseCase,
    required this.signInUseCase,
    required this.signUpUseCase,
    required this.signOutUseCase,
    required this.refreshProfileUseCase,
  }) : super(auth_state.AuthInitial()) {
    on<AuthInitializeRequested>(_onInitializeRequested);
    on<AuthSignInRequested>(_onSignInRequested);
    on<AuthSignUpRequested>(_onSignUpRequested);
    on<AuthSignOutRequested>(_onSignOutRequested);
    on<AuthRefreshProfileRequested>(_onRefreshProfileRequested);
    on<AuthCreateHouseholdRequested>(_onCreateHouseholdRequested);
    on<AuthUpdateHouseholdRequested>(_onUpdateHouseholdRequested);
    on<AuthPasswordResetRequested>(_onPasswordResetRequested);
    on<AuthStateChanged>(_onAuthStateChanged);
  }

  /// Initialize authentication - check for existing session
  Future<void> _onInitializeRequested(
    AuthInitializeRequested event,
    Emitter<auth_state.AuthState> emit,
  ) async {
    emit(auth_state.AuthLoading());

    final result = await getCurrentUserUseCase();

    result.fold(
      (failure) => emit(auth_state.AuthUnauthenticated()),
      (user) {
        if (user.householdId != null) {
          emit(auth_state.AuthAuthenticated(
            user: user,
            householdId: user.householdId,
            householdName: user.householdName,
          ));
        } else {
          emit(auth_state.AuthOnboardingRequired(user: user));
        }
      },
    );
  }

  /// Sign in with email and password
  Future<void> _onSignInRequested(
    AuthSignInRequested event,
    Emitter<auth_state.AuthState> emit,
  ) async {
    emit(auth_state.AuthLoading());

    final result = await signInUseCase(SignInParams(
      email: event.email,
      password: event.password,
    ));

    result.fold(
      (failure) => emit(auth_state.AuthError(message: failure.message)),
      (user) {
        if (user.householdId != null) {
          emit(auth_state.AuthAuthenticated(
            user: user,
            householdId: user.householdId,
            householdName: user.householdName,
          ));
        } else {
          emit(auth_state.AuthOnboardingRequired(user: user));
        }
      },
    );
  }

  /// Sign up with email, password, and name
  Future<void> _onSignUpRequested(
    AuthSignUpRequested event,
    Emitter<auth_state.AuthState> emit,
  ) async {
    emit(auth_state.AuthLoading());

    final result = await signUpUseCase(SignUpParams(
      email: event.email,
      password: event.password,
      name: event.name,
    ));

    result.fold(
      (failure) => emit(auth_state.AuthError(message: failure.message)),
      (_) => emit(const auth_state.AuthSignUpSuccess(
        message: 'Inscription réussie. Un foyer par défaut a été créé.',
      )),
    );
  }

  /// Sign out current user
  Future<void> _onSignOutRequested(
    AuthSignOutRequested event,
    Emitter<auth_state.AuthState> emit,
  ) async {
    emit(auth_state.AuthLoading());

    final result = await signOutUseCase();

    result.fold(
      (failure) => emit(auth_state.AuthError(message: failure.message)),
      (_) => emit(auth_state.AuthUnauthenticated()),
    );
  }

  /// Refresh user profile and household data
  Future<void> _onRefreshProfileRequested(
    AuthRefreshProfileRequested event,
    Emitter<auth_state.AuthState> emit,
  ) async {
    final result = await refreshProfileUseCase();

    result.fold(
      (failure) => emit(auth_state.AuthError(message: failure.message)),
      (user) {
        if (user.householdId != null) {
          emit(auth_state.AuthProfileRefreshed(
            user: user,
            householdId: user.householdId,
            householdName: user.householdName,
          ));
        } else {
          emit(auth_state.AuthOnboardingRequired(user: user));
        }
      },
    );
  }

  /// Create a new household for the user
  Future<void> _onCreateHouseholdRequested(
    AuthCreateHouseholdRequested event,
    Emitter<auth_state.AuthState> emit,
  ) async {
    emit(auth_state.AuthLoading());

    try {
      final supabase = Supabase.instance.client;
      final user = supabase.auth.currentUser;

      if (user == null) {
        emit(const auth_state.AuthError(message: 'Utilisateur non connecté'));
        return;
      }

      // Create household
      final householdResponse = await supabase
          .from('households')
          .insert({
            'name': event.householdName,
            'created_by': user.id,
          })
          .select()
          .single();

      // Update user with household_id
      await supabase
          .from('users')
          .update({'household_id': householdResponse['id']}).eq('id', user.id);

      // Refresh profile to get updated data
      add(AuthRefreshProfileRequested());
    } catch (e) {
      emit(auth_state.AuthError(
          message: 'Erreur lors de la création du foyer: $e'));
    }
  }

  /// Update household name
  Future<void> _onUpdateHouseholdRequested(
    AuthUpdateHouseholdRequested event,
    Emitter<auth_state.AuthState> emit,
  ) async {
    try {
      final supabase = Supabase.instance.client;
      final currentState = state;

      if (currentState is! auth_state.AuthAuthenticated ||
          currentState.householdId == null) {
        emit(const auth_state.AuthError(message: 'Aucun foyer associé'));
        return;
      }

      await supabase.from('households').update(
          {'name': event.householdName}).eq('id', currentState.householdId!);

      // Refresh profile to get updated data
      add(AuthRefreshProfileRequested());
    } catch (e) {
      emit(auth_state.AuthError(
          message: 'Erreur lors de la mise à jour du foyer: $e'));
    }
  }

  /// Reset password for email
  Future<void> _onPasswordResetRequested(
    AuthPasswordResetRequested event,
    Emitter<auth_state.AuthState> emit,
  ) async {
    try {
      final supabase = Supabase.instance.client;

      await supabase.auth.resetPasswordForEmail(
        event.email,
        redirectTo: 'medytrack://auth/callback',
      );

      emit(const auth_state.AuthSignUpSuccess(
        message:
            'Email de réinitialisation envoyé. Vérifiez votre boîte de réception.',
      ));
    } catch (e) {
      emit(
          auth_state.AuthError(message: 'Erreur lors de l\'envoi du lien: $e'));
    }
  }

  /// Handle auth state change from Supabase
  Future<void> _onAuthStateChanged(
    AuthStateChanged event,
    Emitter<auth_state.AuthState> emit,
  ) async {
    if (event.isAuthenticated && event.userId != null) {
      // User is authenticated, refresh profile
      add(AuthRefreshProfileRequested());
    } else {
      // User is not authenticated
      emit(auth_state.AuthUnauthenticated());
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_state.dart' as auth_state;

/// Widget that listens to auth state changes and handles global auth logic
/// Mirrors the web app's auth context provider functionality
class AuthStateListener extends StatelessWidget {
  final Widget child;

  const AuthStateListener({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, auth_state.AuthState>(
      listener: (context, state) {
        // Handle global auth state changes here
        // This mirrors the web app's AuthContext provider logic

        if (state is auth_state.AuthAuthenticated) {
          // User is fully authenticated with complete profile
          _handleAuthenticatedState(context, state);
        } else if (state is auth_state.AuthOnboardingRequired) {
          // User is authenticated but needs onboarding
          _handleOnboardingRequiredState(context, state);
        } else if (state is auth_state.AuthUnauthenticated) {
          // User is not authenticated
          _handleUnauthenticatedState(context, state);
        } else if (state is auth_state.AuthError) {
          // Handle auth errors
          _handleAuthError(context, state);
        }
      },
      child: child,
    );
  }

  void _handleAuthenticatedState(
      BuildContext context, auth_state.AuthAuthenticated state) {
    // User has complete profile and household setup
    // Load dashboard data and other user-specific data
    // TODO: Implement dashboard data loading when DashboardBloc is created
    debugPrint('User authenticated: ${state.user.email}');
  }

  void _handleOnboardingRequiredState(
    BuildContext context,
    auth_state.AuthOnboardingRequired state,
  ) {
    // User needs to complete onboarding
    // Clear any cached data that might be from previous user
    _clearUserData(context);
  }

  void _handleUnauthenticatedState(
    BuildContext context,
    auth_state.AuthUnauthenticated state,
  ) {
    // User is not authenticated
    // Clear all user data and reset app state
    _clearUserData(context);
  }

  void _handleAuthError(BuildContext context, auth_state.AuthError state) {
    // Handle authentication errors
    // Show error message if needed (can be overridden by specific pages)
    debugPrint('Auth Error: ${state.message}');
  }

  void _clearUserData(BuildContext context) {
    // TODO: Clear dashboard data when DashboardBloc is implemented
    // TODO: Clear medicine data when MedicineBloc is implemented
    debugPrint('Clearing user data');
  }
}

/// Extension to add dashboard and medicine events
extension AuthStateListenerEvents on AuthStateListener {
  // These would be defined in the respective BLoC files
}

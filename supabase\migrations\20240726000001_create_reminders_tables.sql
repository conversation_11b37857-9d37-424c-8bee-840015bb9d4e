-- Create reminders table
CREATE TABLE IF NOT EXISTS public.reminders (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_medicine_id UUID NOT NULL REFERENCES public.user_medicines(id) ON DELETE CASCADE,
    times TEXT[] NOT NULL, -- Array of time strings like ["08:00", "14:00", "20:00"]
    frequency_type TEXT NOT NULL CHECK (frequency_type IN ('DAILY', 'WEEKLY', 'HOURLY_INTERVAL')),
    interval_hours INTEGER, -- For hourly intervals (e.g., every 8 hours)
    days_of_week INTEGER[], -- Array of weekday numbers (0=Sunday, 1=Monday, etc.) for weekly reminders
    start_date DATE NOT NULL,
    end_date DATE, -- Optional end date
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create dose_history table
CREATE TABLE IF NOT EXISTS public.dose_history (
    id SERIAL PRIMARY KEY,
    user_medicine_id UUID NOT NULL REFERENCES public.user_medicines(id) ON DELETE CASCADE,
    reminder_id UUID REFERENCES public.reminders(id) ON DELETE SET NULL,
    scheduled_time TIMESTAMP WITH TIME ZONE NOT NULL,
    action_time TIMESTAMP WITH TIME ZONE,
    status TEXT NOT NULL CHECK (status IN ('TAKEN', 'SKIPPED', 'SNOOZED')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_reminders_user_medicine_id ON public.reminders(user_medicine_id);
CREATE INDEX IF NOT EXISTS idx_reminders_is_active ON public.reminders(is_active);
CREATE INDEX IF NOT EXISTS idx_reminders_start_date ON public.reminders(start_date);
CREATE INDEX IF NOT EXISTS idx_reminders_end_date ON public.reminders(end_date);

CREATE INDEX IF NOT EXISTS idx_dose_history_user_medicine_id ON public.dose_history(user_medicine_id);
CREATE INDEX IF NOT EXISTS idx_dose_history_reminder_id ON public.dose_history(reminder_id);
CREATE INDEX IF NOT EXISTS idx_dose_history_scheduled_time ON public.dose_history(scheduled_time);
CREATE INDEX IF NOT EXISTS idx_dose_history_status ON public.dose_history(status);

-- Create updated_at trigger for reminders table
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER handle_reminders_updated_at
    BEFORE UPDATE ON public.reminders
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- Enable Row Level Security (RLS)
ALTER TABLE public.reminders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.dose_history ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for reminders table
CREATE POLICY "Users can view their own reminders" ON public.reminders
    FOR SELECT USING (
        user_medicine_id IN (
            SELECT um.id FROM public.user_medicines um
            WHERE um.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own reminders" ON public.reminders
    FOR INSERT WITH CHECK (
        user_medicine_id IN (
            SELECT um.id FROM public.user_medicines um
            WHERE um.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own reminders" ON public.reminders
    FOR UPDATE USING (
        user_medicine_id IN (
            SELECT um.id FROM public.user_medicines um
            WHERE um.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own reminders" ON public.reminders
    FOR DELETE USING (
        user_medicine_id IN (
            SELECT um.id FROM public.user_medicines um
            WHERE um.user_id = auth.uid()
        )
    );

-- Create RLS policies for dose_history table
CREATE POLICY "Users can view their own dose history" ON public.dose_history
    FOR SELECT USING (
        user_medicine_id IN (
            SELECT um.id FROM public.user_medicines um
            WHERE um.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own dose history" ON public.dose_history
    FOR INSERT WITH CHECK (
        user_medicine_id IN (
            SELECT um.id FROM public.user_medicines um
            WHERE um.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own dose history" ON public.dose_history
    FOR UPDATE USING (
        user_medicine_id IN (
            SELECT um.id FROM public.user_medicines um
            WHERE um.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own dose history" ON public.dose_history
    FOR DELETE USING (
        user_medicine_id IN (
            SELECT um.id FROM public.user_medicines um
            WHERE um.user_id = auth.uid()
        )
    );

-- Grant necessary permissions
GRANT ALL ON public.reminders TO authenticated;
GRANT ALL ON public.dose_history TO authenticated;
GRANT USAGE, SELECT ON SEQUENCE public.dose_history_id_seq TO authenticated;

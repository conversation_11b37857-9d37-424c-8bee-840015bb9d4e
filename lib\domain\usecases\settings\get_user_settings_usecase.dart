import 'package:dartz/dartz.dart';
import '../../entities/settings.dart';
import '../../repositories/settings_repository.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';

/// Use case for getting user settings
class GetUserSettingsUseCase implements UseCase<Settings, String> {
  final SettingsRepository repository;

  GetUserSettingsUseCase(this.repository);

  @override
  Future<Either<Failure, Settings>> call(String userId) async {
    return await repository.getUserSettings(userId);
  }
}

/// Use case for updating user settings
class UpdateUserSettingsUseCase implements UseCase<Settings, UpdateUserSettingsParams> {
  final SettingsRepository repository;

  UpdateUserSettingsUseCase(this.repository);

  @override
  Future<Either<Failure, Settings>> call(UpdateUserSettingsParams params) async {
    return await repository.updateUserSettings(params.settings);
  }
}

/// Parameters for updating user settings
class UpdateUserSettingsParams {
  final Settings settings;

  UpdateUserSettingsParams({required this.settings});
}

/// Use case for getting notification settings
class GetNotificationSettingsUseCase implements UseCase<NotificationSettings, String> {
  final SettingsRepository repository;

  GetNotificationSettingsUseCase(this.repository);

  @override
  Future<Either<Failure, NotificationSettings>> call(String userId) async {
    return await repository.getNotificationSettings(userId);
  }
}

/// Use case for updating notification settings
class UpdateNotificationSettingsUseCase implements UseCase<NotificationSettings, UpdateNotificationSettingsParams> {
  final SettingsRepository repository;

  UpdateNotificationSettingsUseCase(this.repository);

  @override
  Future<Either<Failure, NotificationSettings>> call(UpdateNotificationSettingsParams params) async {
    return await repository.updateNotificationSettings(params.userId, params.settings);
  }
}

/// Parameters for updating notification settings
class UpdateNotificationSettingsParams {
  final String userId;
  final NotificationSettings settings;

  UpdateNotificationSettingsParams({
    required this.userId,
    required this.settings,
  });
}

/// Use case for getting app settings
class GetAppSettingsUseCase implements UseCase<AppSettings, String> {
  final SettingsRepository repository;

  GetAppSettingsUseCase(this.repository);

  @override
  Future<Either<Failure, AppSettings>> call(String userId) async {
    return await repository.getAppSettings(userId);
  }
}

/// Use case for updating app settings
class UpdateAppSettingsUseCase implements UseCase<AppSettings, UpdateAppSettingsParams> {
  final SettingsRepository repository;

  UpdateAppSettingsUseCase(this.repository);

  @override
  Future<Either<Failure, AppSettings>> call(UpdateAppSettingsParams params) async {
    return await repository.updateAppSettings(params.userId, params.settings);
  }
}

/// Parameters for updating app settings
class UpdateAppSettingsParams {
  final String userId;
  final AppSettings settings;

  UpdateAppSettingsParams({
    required this.userId,
    required this.settings,
  });
}

/// Use case for getting security settings
class GetSecuritySettingsUseCase implements UseCase<SecuritySettings, String> {
  final SettingsRepository repository;

  GetSecuritySettingsUseCase(this.repository);

  @override
  Future<Either<Failure, SecuritySettings>> call(String userId) async {
    return await repository.getSecuritySettings(userId);
  }
}

/// Use case for updating security settings
class UpdateSecuritySettingsUseCase implements UseCase<SecuritySettings, UpdateSecuritySettingsParams> {
  final SettingsRepository repository;

  UpdateSecuritySettingsUseCase(this.repository);

  @override
  Future<Either<Failure, SecuritySettings>> call(UpdateSecuritySettingsParams params) async {
    return await repository.updateSecuritySettings(params.userId, params.settings);
  }
}

/// Parameters for updating security settings
class UpdateSecuritySettingsParams {
  final String userId;
  final SecuritySettings settings;

  UpdateSecuritySettingsParams({
    required this.userId,
    required this.settings,
  });
}

/// Use case for getting personalization settings
class GetPersonalizationSettingsUseCase implements UseCase<PersonalizationSettings, String> {
  final SettingsRepository repository;

  GetPersonalizationSettingsUseCase(this.repository);

  @override
  Future<Either<Failure, PersonalizationSettings>> call(String userId) async {
    return await repository.getPersonalizationSettings(userId);
  }
}

/// Use case for updating personalization settings
class UpdatePersonalizationSettingsUseCase implements UseCase<PersonalizationSettings, UpdatePersonalizationSettingsParams> {
  final SettingsRepository repository;

  UpdatePersonalizationSettingsUseCase(this.repository);

  @override
  Future<Either<Failure, PersonalizationSettings>> call(UpdatePersonalizationSettingsParams params) async {
    return await repository.updatePersonalizationSettings(params.userId, params.settings);
  }
}

/// Parameters for updating personalization settings
class UpdatePersonalizationSettingsParams {
  final String userId;
  final PersonalizationSettings settings;

  UpdatePersonalizationSettingsParams({
    required this.userId,
    required this.settings,
  });
}

/// Use case for resetting settings to defaults
class ResetSettingsToDefaultsUseCase implements UseCase<Settings, ResetSettingsParams> {
  final SettingsRepository repository;

  ResetSettingsToDefaultsUseCase(this.repository);

  @override
  Future<Either<Failure, Settings>> call(ResetSettingsParams params) async {
    return await repository.resetToDefaults(params.userId, params.householdId);
  }
}

/// Parameters for resetting settings
class ResetSettingsParams {
  final String userId;
  final String householdId;

  ResetSettingsParams({
    required this.userId,
    required this.householdId,
  });
}

/// Use case for checking biometric availability
class CheckBiometricAvailabilityUseCase implements UseCase<bool, NoParams> {
  final SettingsRepository repository;

  CheckBiometricAvailabilityUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(NoParams params) async {
    return await repository.isBiometricAvailable();
  }
}

/// Use case for validating PIN code
class ValidatePinCodeUseCase implements UseCase<bool, String> {
  final SettingsRepository repository;

  ValidatePinCodeUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(String pinCode) async {
    return await repository.validatePinCode(pinCode);
  }
}

/// Use case for updating expiry warning threshold
class UpdateExpiryWarningThresholdUseCase implements UseCase<void, UpdateExpiryWarningParams> {
  final SettingsRepository repository;

  UpdateExpiryWarningThresholdUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(UpdateExpiryWarningParams params) async {
    return await repository.updateExpiryWarningThreshold(params.userId, params.days);
  }
}

/// Parameters for updating expiry warning threshold
class UpdateExpiryWarningParams {
  final String userId;
  final int days;

  UpdateExpiryWarningParams({
    required this.userId,
    required this.days,
  });
}

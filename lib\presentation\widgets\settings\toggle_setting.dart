import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

/// Enhanced toggle setting widget with loading states and validation
class ToggleSetting extends StatefulWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final bool value;
  final Function(bool) onChanged;
  final bool enabled;
  final bool isLoading;
  final Color? iconColor;
  final String? loadingText;
  final Widget? customTrailing;

  const ToggleSetting({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    required this.value,
    required this.onChanged,
    this.enabled = true,
    this.isLoading = false,
    this.iconColor,
    this.loadingText,
    this.customTrailing,
  });

  @override
  State<ToggleSetting> createState() => _ToggleSettingState();
}

class _ToggleSettingState extends State<ToggleSetting>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 4),
            decoration: BoxDecoration(
              color: widget.enabled ? Colors.white : AppColors.grey50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: widget.value && widget.enabled
                    ? AppColors.teal.withValues(alpha: 0.3)
                    : AppColors.grey200,
                width: 1,
              ),
              boxShadow: widget.enabled
                  ? [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(12),
                onTap: widget.enabled && !widget.isLoading
                    ? () {
                        _animationController.forward().then((_) {
                          _animationController.reverse();
                        });
                        widget.onChanged(!widget.value);
                      }
                    : null,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      // Icon
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: (widget.iconColor ?? AppColors.teal)
                              .withValues(alpha: widget.enabled ? 0.1 : 0.05),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          widget.icon,
                          color: widget.enabled
                              ? (widget.iconColor ?? AppColors.teal)
                              : AppColors.grey400,
                          size: 20,
                        ),
                      ),

                      const SizedBox(width: 16),

                      // Title and subtitle
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.title,
                              style: AppTextStyles.titleSmall.copyWith(
                                color: widget.enabled
                                    ? AppColors.black
                                    : AppColors.grey400,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            if (widget.subtitle != null) ...[
                              const SizedBox(height: 2),
                              Text(
                                widget.isLoading && widget.loadingText != null
                                    ? widget.loadingText!
                                    : widget.subtitle!,
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: widget.enabled
                                      ? AppColors.grey600
                                      : AppColors.grey400,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),

                      const SizedBox(width: 16),

                      // Trailing widget
                      if (widget.customTrailing != null)
                        widget.customTrailing!
                      else if (widget.isLoading)
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: AppColors.teal,
                          ),
                        )
                      else
                        AnimatedSwitcher(
                          duration: const Duration(milliseconds: 200),
                          child: Switch(
                            key: ValueKey(widget.value),
                            value: widget.value,
                            onChanged: widget.enabled ? widget.onChanged : null,
                            activeColor: AppColors.teal,
                            inactiveThumbColor: AppColors.grey400,
                            inactiveTrackColor: AppColors.grey200,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Slider setting widget for numeric values
class SliderSetting extends StatefulWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final double value;
  final double min;
  final double max;
  final int? divisions;
  final Function(double) onChanged;
  final String Function(double)? valueFormatter;
  final bool enabled;
  final bool isLoading;
  final Color? iconColor;

  const SliderSetting({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    required this.value,
    required this.min,
    required this.max,
    this.divisions,
    required this.onChanged,
    this.valueFormatter,
    this.enabled = true,
    this.isLoading = false,
    this.iconColor,
  });

  @override
  State<SliderSetting> createState() => _SliderSettingState();
}

class _SliderSettingState extends State<SliderSetting> {
  late double _currentValue;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.value;
  }

  @override
  void didUpdateWidget(SliderSetting oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.value != oldWidget.value) {
      _currentValue = widget.value;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: widget.enabled ? Colors.white : AppColors.grey50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.grey200,
          width: 1,
        ),
        boxShadow: widget.enabled
            ? [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                // Icon
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: (widget.iconColor ?? AppColors.teal)
                        .withValues(alpha: widget.enabled ? 0.1 : 0.05),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    widget.icon,
                    color: widget.enabled
                        ? (widget.iconColor ?? AppColors.teal)
                        : AppColors.grey400,
                    size: 20,
                  ),
                ),

                const SizedBox(width: 16),

                // Title and subtitle
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.title,
                        style: AppTextStyles.titleSmall.copyWith(
                          color: widget.enabled
                              ? AppColors.black
                              : AppColors.grey400,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (widget.subtitle != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          widget.subtitle!,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: widget.enabled
                                ? AppColors.grey600
                                : AppColors.grey400,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Current value
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.teal.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    widget.valueFormatter?.call(_currentValue) ??
                        _currentValue.toStringAsFixed(0),
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.teal,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Slider
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: AppColors.teal,
                inactiveTrackColor: AppColors.grey200,
                thumbColor: AppColors.teal,
                overlayColor: AppColors.teal.withValues(alpha: 0.1),
                trackHeight: 4,
                thumbShape: const RoundSliderThumbShape(
                  enabledThumbRadius: 8,
                ),
              ),
              child: Slider(
                value: _currentValue,
                min: widget.min,
                max: widget.max,
                divisions: widget.divisions,
                onChanged: widget.enabled && !widget.isLoading
                    ? (value) {
                        setState(() {
                          _currentValue = value;
                        });
                      }
                    : null,
                onChangeEnd: widget.enabled && !widget.isLoading
                    ? (value) {
                        widget.onChanged(value);
                      }
                    : null,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

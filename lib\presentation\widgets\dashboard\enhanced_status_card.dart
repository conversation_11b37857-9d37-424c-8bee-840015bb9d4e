import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

/// Enhanced status card widget that follows the mockup design requirements
/// Features:
/// - 1:1 square aspect ratio
/// - 20dp border radius
/// - Typography hierarchy (42dp main number, 16sp title, 14sp subtitle)
/// - Arrow chip positioned at bottom-right corner
/// - Background icons at 10% opacity
/// - Material 3 design principles
class EnhancedStatusCard extends StatelessWidget {
  final String title;
  final String value;
  final String subtitle;
  final Color primaryColor;
  final Color backgroundColor;
  final Gradient? backgroundGradient;
  final IconData icon;
  final IconData? backgroundIcon;
  final VoidCallback? onTap;
  final bool showArrowChip;

  const EnhancedStatusCard({
    super.key,
    required this.title,
    required this.value,
    required this.subtitle,
    required this.primaryColor,
    required this.backgroundColor,
    this.backgroundGradient,
    required this.icon,
    this.backgroundIcon,
    this.onTap,
    this.showArrowChip = true,
  });

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 1.0, // 1:1 square aspect ratio
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20), // 20dp border radius
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Container(
            decoration: BoxDecoration(
              color: backgroundGradient == null ? backgroundColor : null,
              gradient: backgroundGradient,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Stack(
              children: [
                // Background icon at 10% opacity (top-right)
                if (backgroundIcon != null)
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      width: 56,
                      height: 56,
                      decoration: BoxDecoration(
                        color: primaryColor.withValues(alpha: 0.05),
                        borderRadius: BorderRadius.circular(28),
                      ),
                      child: Icon(
                        backgroundIcon!,
                        size: 32,
                        color: primaryColor.withValues(alpha: 0.15),
                      ),
                    ),
                  ),

                // Main content
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Main number (top-left, 42dp, bold)
                      Text(
                        value,
                        style: AppTextStyles.statusCardNumber.copyWith(
                          color: primaryColor,
                        ),
                      ),

                      const Spacer(),

                      // Category title (16sp, bold)
                      Text(
                        title,
                        style: AppTextStyles.statusCardTitle.copyWith(
                          color: primaryColor,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 4),

                      // Task/item count (14sp, regular)
                      Text(
                        subtitle,
                        style: AppTextStyles.statusCardSubtitle.copyWith(
                          color: primaryColor.withValues(alpha: 0.8),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),

                // Arrow chip (bottom-right corner)
                if (showArrowChip && onTap != null)
                  Positioned(
                    bottom: 16,
                    right: 16,
                    child: _buildArrowChip(),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildArrowChip() {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: primaryColor,
        borderRadius: BorderRadius.circular(16), // Pill-shaped
        boxShadow: [
          BoxShadow(
            color: primaryColor.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Icon(
        Icons.arrow_forward,
        color: Colors.white,
        size: 16,
      ),
    );
  }
}

/// Factory methods for creating specific status card types
class EnhancedStatusCardFactory {
  /// Create a total medicines status card
  static EnhancedStatusCard total({
    required String value,
    required String subtitle,
    VoidCallback? onTap,
  }) {
    return EnhancedStatusCard(
      title: 'Total',
      value: value,
      subtitle: subtitle,
      primaryColor: AppColors.teal,
      backgroundColor: AppColors.teal.withValues(alpha: 0.1),
      backgroundGradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.teal.withValues(alpha: 0.15),
          AppColors.teal.withValues(alpha: 0.05),
        ],
      ),
      icon: Icons.medication,
      backgroundIcon: Icons.medication_liquid,
      onTap: onTap,
    );
  }

  /// Create an expired medicines status card
  static EnhancedStatusCard expired({
    required String value,
    required String subtitle,
    VoidCallback? onTap,
  }) {
    return EnhancedStatusCard(
      title: 'Expirés',
      value: value,
      subtitle: subtitle,
      primaryColor: AppColors.error,
      backgroundColor: AppColors.error.withValues(alpha: 0.1),
      backgroundGradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.error.withValues(alpha: 0.15),
          AppColors.error.withValues(alpha: 0.05),
        ],
      ),
      icon: Icons.error,
      backgroundIcon: Icons.warning_amber_rounded,
      onTap: onTap,
    );
  }

  /// Create a low stock status card
  static EnhancedStatusCard lowStock({
    required String value,
    required String subtitle,
    VoidCallback? onTap,
  }) {
    return EnhancedStatusCard(
      title: 'Stock faible',
      value: value,
      subtitle: subtitle,
      primaryColor: AppColors.lowStock,
      backgroundColor: AppColors.lowStock.withValues(alpha: 0.1),
      backgroundGradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.lowStock.withValues(alpha: 0.15),
          AppColors.lowStock.withValues(alpha: 0.05),
        ],
      ),
      icon: Icons.inventory_2,
      backgroundIcon: Icons.inventory_2_outlined,
      onTap: onTap,
    );
  }

  /// Create an expiring soon status card
  static EnhancedStatusCard expiringSoon({
    required String value,
    required String subtitle,
    VoidCallback? onTap,
  }) {
    return EnhancedStatusCard(
      title: 'Expirent bientôt',
      value: value,
      subtitle: subtitle,
      primaryColor: AppColors.expiringSoon,
      backgroundColor: AppColors.expiringSoon.withValues(alpha: 0.1),
      backgroundGradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.expiringSoon.withValues(alpha: 0.15),
          AppColors.expiringSoon.withValues(alpha: 0.05),
        ],
      ),
      icon: Icons.schedule,
      backgroundIcon: Icons.access_time_rounded,
      onTap: onTap,
    );
  }
}

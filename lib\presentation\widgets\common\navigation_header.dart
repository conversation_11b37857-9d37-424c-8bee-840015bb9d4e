import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

/// Navigation header with home, settings, search, and menu icons
/// Matches the second screen in the mockup
class NavigationHeader extends StatelessWidget {
  final VoidCallback? onHomeTap;
  final VoidCallback? onSettingsTap;
  final VoidCallback? onSearchTap;
  final VoidCallback? onMenuTap;
  final bool showSearchBackground;
  final String? searchHint;
  final TextEditingController? searchController;
  final Function(String)? onSearchChanged;
  final bool isSearchActive;

  const NavigationHeader({
    super.key,
    this.onHomeTap,
    this.onSettingsTap,
    this.onSearchTap,
    this.onMenuTap,
    this.showSearchBackground = true,
    this.searchHint,
    this.searchController,
    this.onSearchChanged,
    this.isSearchActive = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.tealDark,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: isSearchActive ? _buildSearchBar() : _buildNavigationIcons(),
        ),
      ),
    );
  }

  Widget _buildNavigationIcons() {
    return Row(
      children: [
        // Home icon
        _buildIconButton(
          icon: Icons.home_outlined,
          onTap: onHomeTap,
          backgroundColor: AppColors.tealDark,
          iconColor: Colors.white,
        ),

        const SizedBox(width: 12),

        // Settings icon
        _buildIconButton(
            icon: Icons.settings_outlined,
            onTap: onSettingsTap,
            backgroundColor: AppColors.tealDark,
            iconColor: Colors.white),
        const Spacer(),

        // Search icon with teal background
        _buildIconButton(
          icon: Icons.search,
          onTap: onSearchTap,
          backgroundColor:
              showSearchBackground ? AppColors.tealDark : AppColors.tealDark,
          iconColor: showSearchBackground ? Colors.white : AppColors.navy,
        ),

        const SizedBox(width: 12),

        // Hamburger menu
        _buildIconButton(
          icon: Icons.menu,
          onTap: onMenuTap,
          backgroundColor: AppColors.tealDark,
          iconColor: Colors.white,
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Row(
      children: [
        // Back button to close search
        _buildIconButton(
          icon: Icons.arrow_back,
          onTap: onSearchTap, // Same callback to toggle search
          backgroundColor: Colors.white,
          iconColor: AppColors.tealDark,
        ),

        const SizedBox(width: 12),

        // Search input field
        Expanded(
          child: Container(
            height: 44,
            decoration: BoxDecoration(
              color: AppColors.grey100,
              borderRadius: BorderRadius.circular(12),
            ),
            child: TextField(
              controller: searchController,
              onChanged: onSearchChanged,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.navy,
              ),
              decoration: InputDecoration(
                hintText: searchHint ?? 'Rechercher...',
                hintStyle: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.grey500,
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: AppColors.grey500,
                  size: 20,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildIconButton({
    required IconData icon,
    VoidCallback? onTap,
    Color? backgroundColor,
    Color? iconColor,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 44,
        height: 44,
        decoration: BoxDecoration(
          color: backgroundColor ?? AppColors.grey100,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(
          icon,
          color: iconColor ?? AppColors.navy,
          size: 24,
        ),
      ),
    );
  }
}

/// Simple header with back button and title
/// Used for detail screens and forms
class SimpleHeader extends StatelessWidget {
  final String title;
  final VoidCallback? onBackTap;
  final List<Widget>? actions;
  final Color? backgroundColor;

  const SimpleHeader({
    super.key,
    required this.title,
    this.onBackTap,
    this.actions,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Row(
            children: [
              // Back button
              GestureDetector(
                onTap: onBackTap ?? () => Navigator.of(context).pop(),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.grey100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.arrow_back_ios_new,
                    color: AppColors.navy,
                    size: 20,
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // Title
              Expanded(
                child: Text(
                  title,
                  style: AppTextStyles.titleLarge.copyWith(
                    color: AppColors.navy,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              // Actions
              if (actions != null) ...actions!,
            ],
          ),
        ),
      ),
    );
  }
}

/// Welcome header for onboarding and landing screens
/// Matches the first screen in the mockup
class WelcomeHeader extends StatelessWidget {
  final String title;
  final String subtitle;
  final String buttonText;
  final VoidCallback? onButtonTap;
  final VoidCallback? onMenuTap;
  final Widget? illustration;
  final bool showLogo;

  const WelcomeHeader({
    super.key,
    required this.title,
    required this.subtitle,
    required this.buttonText,
    this.onButtonTap,
    this.onMenuTap,
    this.illustration,
    this.showLogo = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.tealExtraLight.withValues(alpha: 0.3),
            Colors.white,
          ],
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(32),
          bottomRight: Radius.circular(32),
        ),
      ),
      child: SafeArea(
        bottom: false,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(20, 20, 20, 40),
          child: Column(
            children: [
              // Top navigation
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (showLogo) _buildLogo(),
                  if (onMenuTap != null)
                    GestureDetector(
                      onTap: onMenuTap,
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.8),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.menu,
                          color: AppColors.navy,
                          size: 24,
                        ),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 40),

              // Main content
              Column(
                children: [
                  Text(
                    title,
                    style: AppTextStyles.displayMedium.copyWith(
                      color: AppColors.navy,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 16),

                  Text(
                    subtitle,
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.grey600,
                      height: 1.6,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 32),

                  // CTA Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: onButtonTap,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.teal,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        buttonText,
                        style: AppTextStyles.titleMedium.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),

                  if (illustration != null) ...[
                    const SizedBox(height: 32),
                    illustration!,
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: AppColors.teal,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.medical_services,
            color: Colors.white,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          'MEDYTRACK',
          style: AppTextStyles.titleMedium.copyWith(
            color: AppColors.navy,
            fontWeight: FontWeight.bold,
            letterSpacing: 0.5,
          ),
        ),
      ],
    );
  }
}

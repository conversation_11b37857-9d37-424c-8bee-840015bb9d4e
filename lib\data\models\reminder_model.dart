import 'package:medytrack_mobile_v2/domain/entities/reminder.dart';

class ReminderModel extends <PERSON>minder {
  const ReminderModel({
    super.id,
    required super.userMedicineId,
    super.name,
    super.dosageAmount,
    super.dosageUnit,
    required super.times,
    required super.frequencyType,
    super.frequencyValue,
    super.frequencyDays,
    super.specificDates,
    required super.startDate,
    super.endDate,
    super.notes,
    super.isActive,
    super.status,
    super.createdAt,
    super.updatedAt,
  });

  factory ReminderModel.fromJson(Map<String, dynamic> json) {
    // Parse status from database
    ReminderStatus status = ReminderStatus.active;
    if (json['status'] != null) {
      switch (json['status']) {
        case 'ACTIVE':
          status = ReminderStatus.active;
          break;
        case 'PAUSED':
          status = ReminderStatus.paused;
          break;
        case 'ARCHIVED':
          status = ReminderStatus.archived;
          break;
      }
    }

    return ReminderModel(
      id: json['id'],
      userMedicineId: json['user_medicine_id'],
      name: json['name'],
      dosageAmount: json['dosage_amount']?.toDouble(),
      dosageUnit: json['dosage_unit'],
      times: List<String>.from(json['times'] ?? []),
      frequencyType: json['frequency_type'] ?? 'DAILY',
      // Map DB columns to legacy domain fields for backward compatibility:
      // interval_hours -> frequencyValue when HOURLY_INTERVAL
      frequencyValue: json['interval_hours'],
      // days_of_week -> frequencyDays
      frequencyDays: json['days_of_week'] != null
          ? List<int>.from(json['days_of_week'])
          : [],
      // specific_dates not present in current schema; keep empty
      specificDates: const [],
      startDate: DateTime.parse(json['start_date']),
      endDate: json['end_date'] != null ? DateTime.parse(json['end_date']) : null,
      notes: json['notes'],
      isActive: json['is_active'] ?? true,
      status: status,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  factory ReminderModel.fromEntity(Reminder reminder) {
    return ReminderModel(
      id: reminder.id,
      userMedicineId: reminder.userMedicineId,
      name: reminder.name,
      dosageAmount: reminder.dosageAmount,
      dosageUnit: reminder.dosageUnit,
      times: reminder.times,
      frequencyType: reminder.frequencyType,
      frequencyValue: reminder.frequencyValue,
      frequencyDays: reminder.frequencyDays,
      specificDates: reminder.specificDates,
      startDate: reminder.startDate,
      endDate: reminder.endDate,
      notes: reminder.notes,
      isActive: reminder.isActive,
      status: reminder.status,
      createdAt: reminder.createdAt,
      updatedAt: reminder.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    // Convert status to database format
    String statusString;
    switch (status) {
      case ReminderStatus.active:
        statusString = 'ACTIVE';
        break;
      case ReminderStatus.paused:
        statusString = 'PAUSED';
        break;
      case ReminderStatus.archived:
        statusString = 'ARCHIVED';
        break;
    }

    final json = <String, dynamic>{
      'user_medicine_id': userMedicineId,
      // 'name' is NOT in reminders schema; do not send it
      if (dosageAmount != null) 'dosage_amount': dosageAmount,
      if (dosageUnit != null) 'dosage_unit': dosageUnit,
      'times': times,
      'frequency_type': frequencyType,
      // Map to schema columns
      if (frequencyType == 'HOURLY_INTERVAL' && frequencyValue != null)
        'interval_hours': frequencyValue,
      if (frequencyType == 'EVERY_X_DAYS' && frequencyValue != null)
        'interval_days': frequencyValue,
      if (frequencyType == 'WEEKLY' && (frequencyDays.isNotEmpty))
        'days_of_week': frequencyDays,
      'start_date': startDate.toIso8601String().split('T')[0],
      'end_date': endDate?.toIso8601String().split('T')[0],
      if (notes != null) 'notes': notes,
      'is_active': isActive,
      'status': statusString,
    };

    // Only include id if it's not null (for updates)
    if (id != null) {
      json['id'] = id;
    }

    return json;
  }
}

// You would also create a DoseHistoryModel in a similar fashion
// to handle its fromJson/toJson conversions.

import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

class BulkActionsBar extends StatelessWidget {
  final int selectedCount;
  final VoidCallback? onSelectAll;
  final VoidCallback? onClearSelection;
  final VoidCallback? onDeleteSelected;
  final bool areAllSelected;

  const BulkActionsBar({
    super.key,
    required this.selectedCount,
    this.onSelectAll,
    this.onClearSelection,
    this.onDeleteSelected,
    this.areAllSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.teal.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.teal.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Selection info
          Icon(
            Icons.check_circle,
            color: AppColors.teal,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            '$selectedCount sélectionné${selectedCount > 1 ? 's' : ''}',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.teal,
              fontWeight: FontWeight.w600,
            ),
          ),

          const Spacer(),

          // Actions
          Row(
            children: [
              // Select all / Deselect all
              TextButton.icon(
                onPressed: onSelectAll,
                icon: Icon(
                  areAllSelected ? Icons.deselect : Icons.select_all,
                  size: 18,
                  color: AppColors.teal,
                ),
                label: Text(
                  areAllSelected ? 'Désélectionner' : 'Tout sélectionner',
                  style: AppTextStyles.labelMedium.copyWith(
                    color: AppColors.teal,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),

              const SizedBox(width: 8),

              // Clear selection
              IconButton(
                onPressed: onClearSelection,
                icon: Icon(
                  Icons.close,
                  color: AppColors.grey600,
                  size: 20,
                ),
                style: IconButton.styleFrom(
                  padding: const EdgeInsets.all(4),
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                tooltip: 'Annuler la sélection',
              ),

              const SizedBox(width: 8),

              // Delete selected
              IconButton(
                onPressed: selectedCount > 0 ? onDeleteSelected : null,
                icon: Icon(
                  Icons.delete_outline,
                  color: selectedCount > 0 ? AppColors.error : AppColors.grey400,
                  size: 20,
                ),
                style: IconButton.styleFrom(
                  padding: const EdgeInsets.all(4),
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                tooltip: 'Supprimer la sélection',
              ),
            ],
          ),
        ],
      ),
    );
  }
}

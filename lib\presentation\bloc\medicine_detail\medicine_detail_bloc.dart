import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/repositories/medicine_repository.dart';
import '../../../core/utils/debug_logger.dart';
import 'medicine_detail_event.dart';
import 'medicine_detail_state.dart';

class MedicineDetailBloc
    extends Bloc<MedicineDetailEvent, MedicineDetailState> {
  final MedicineRepository _medicineRepository;

  MedicineDetailBloc({
    required MedicineRepository medicineRepository,
  })  : _medicineRepository = medicineRepository,
        super(const MedicineDetailInitial()) {
    on<MedicineDetailLoadRequested>(_onLoadRequested);
    on<MedicineQuantityUpdated>(_onQuantityUpdated);
    on<MedicineTaken>(_onMedicineTaken);
    on<MedicineDetailDeleteRequested>(_onDeleteRequested);
    on<MedicineDetailClearRequested>(_onClearRequested);
  }

  Future<void> _onLoadRequested(
    MedicineDetailLoadRequested event,
    Emitter<MedicineDetailState> emit,
  ) async {
    emit(const MedicineDetailLoading());

    try {
      DebugLogger.logBloc('Loading medicine detail', data: {
        'medicineId': event.medicineId,
      });

      // Use the new getMedicineById method
      final result =
          await _medicineRepository.getMedicineById(event.medicineId);

      result.fold(
        (failure) => emit(MedicineDetailError(message: failure.message)),
        (medicine) {
          DebugLogger.logBloc('Medicine detail loaded', data: {
            'medicineId': medicine.id,
            'medicineName': medicine.name,
          });

          emit(MedicineDetailLoaded(medicine: medicine));
        },
      );
    } catch (e) {
      DebugLogger.logError('MEDICINE_DETAIL', 'Failed to load medicine detail',
          data: {'medicineId': event.medicineId, 'error': e.toString()});
      emit(MedicineDetailError(
          message: 'Failed to load medicine: ${e.toString()}'));
    }
  }

  Future<void> _onQuantityUpdated(
    MedicineQuantityUpdated event,
    Emitter<MedicineDetailState> emit,
  ) async {
    final currentState = state;
    if (currentState is MedicineDetailLoaded) {
      emit(currentState.copyWith(isUpdating: true));

      try {
        // Update medicine quantity
        final updatedMedicine = currentState.medicine.copyWith(
          quantity: event.newQuantity,
        );

        final result =
            await _medicineRepository.updateMedicine(updatedMedicine);

        result.fold(
          (failure) => emit(MedicineDetailError(message: failure.message)),
          (medicine) {
            emit(MedicineDetailLoaded(medicine: medicine));
            emit(const MedicineDetailOperationSuccess(
              message: 'Quantité mise à jour avec succès',
              operationType: MedicineDetailOperationType.quantityUpdated,
            ));
          },
        );
      } catch (e) {
        emit(MedicineDetailError(
            message: 'Failed to update quantity: ${e.toString()}'));
      }
    }
  }

  Future<void> _onMedicineTaken(
    MedicineTaken event,
    Emitter<MedicineDetailState> emit,
  ) async {
    final currentState = state;
    if (currentState is MedicineDetailLoaded) {
      emit(currentState.copyWith(isUpdating: true));

      try {
        final newQuantity =
            (currentState.medicine.quantity - event.quantityTaken)
                .clamp(0, double.infinity)
                .toInt();

        final updatedMedicine = currentState.medicine.copyWith(
          quantity: newQuantity,
        );

        final result =
            await _medicineRepository.updateMedicine(updatedMedicine);

        result.fold(
          (failure) => emit(MedicineDetailError(message: failure.message)),
          (medicine) {
            emit(MedicineDetailLoaded(medicine: medicine));
            emit(const MedicineDetailOperationSuccess(
              message: 'Prise enregistrée avec succès',
              operationType: MedicineDetailOperationType.medicineTaken,
            ));
          },
        );
      } catch (e) {
        emit(MedicineDetailError(
            message: 'Failed to record medicine taken: ${e.toString()}'));
      }
    }
  }

  Future<void> _onDeleteRequested(
    MedicineDetailDeleteRequested event,
    Emitter<MedicineDetailState> emit,
  ) async {
    try {
      final result = await _medicineRepository.deleteMedicine(event.medicineId);

      result.fold(
        (failure) => emit(MedicineDetailError(message: failure.message)),
        (_) => emit(const MedicineDetailOperationSuccess(
          message: 'Médicament supprimé avec succès',
          operationType: MedicineDetailOperationType.medicineDeleted,
        )),
      );
    } catch (e) {
      emit(MedicineDetailError(
          message: 'Failed to delete medicine: ${e.toString()}'));
    }
  }

  Future<void> _onClearRequested(
    MedicineDetailClearRequested event,
    Emitter<MedicineDetailState> emit,
  ) async {
    emit(const MedicineDetailInitial());
  }
}

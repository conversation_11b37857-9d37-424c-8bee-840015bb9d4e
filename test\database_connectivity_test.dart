import 'package:flutter_test/flutter_test.dart';
import 'package:medytrack_mobile_v2/core/services/supabase_service.dart';
import 'package:medytrack_mobile_v2/core/network/network_info.dart';
import 'package:medytrack_mobile_v2/core/utils/supabase_utils.dart';
import 'package:medytrack_mobile_v2/data/repositories/location_repository_impl.dart';
import 'package:medytrack_mobile_v2/data/repositories/family_member_repository_impl.dart';

import 'package:medytrack_mobile_v2/domain/entities/location.dart';
import 'package:medytrack_mobile_v2/domain/entities/family_member.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

void main() {
  group('Database Connectivity Tests', () {
    late SupabaseService supabaseService;
    late NetworkInfo networkInfo;
    late SupabaseClient supabaseClient;

    setUpAll(() async {
      // Initialize test environment
      networkInfo = _MockNetworkInfo();
      supabaseService = SupabaseService.instance;

      // Mock Supabase client for testing
      supabaseClient = _MockSupabaseClient();
    });

    group('SupabaseService Tests', () {
      test('should initialize successfully', () async {
        expect(() async {
          await supabaseService.initialize(networkInfo: networkInfo);
        }, returnsNormally);
      });

      test('should handle network connectivity checks', () async {
        final isConnected = await networkInfo.isConnected;
        expect(isConnected, isA<bool>());
      });

      test('should provide connection status stream', () {
        expect(supabaseService.connectionStatus, isA<Stream<bool>>());
      });

      test('should execute operations with retry logic', () async {
        var callCount = 0;
        final result = await supabaseService.executeWithRetry<String>(
          () async {
            callCount++;
            if (callCount < 2) {
              throw Exception('Network error');
            }
            return 'Success';
          },
          maxRetries: 3,
          operationName: 'test_operation',
        );

        expect(result, equals('Success'));
        expect(callCount, equals(2));
      });

      test('should handle non-retryable errors correctly', () async {
        expect(
          () => supabaseService.executeWithRetry<String>(
            () async => throw Exception('401 Unauthorized'),
            maxRetries: 3,
          ),
          throwsException,
        );
      });

      test('should provide enhanced error messages', () {
        final networkError = supabaseService.getErrorMessage('network timeout');
        expect(networkError, contains('connexion réseau'));

        final authError = supabaseService.getErrorMessage('401 unauthorized');
        expect(authError, contains('Session expirée'));

        final notFoundError = supabaseService.getErrorMessage('404 not found');
        expect(notFoundError, contains('Ressource non trouvée'));
      });
    });

    group('Repository Integration Tests', () {
      late LocationRepositoryImpl locationRepository;
      late FamilyMemberRepositoryImpl familyMemberRepository;

      setUp(() {
        locationRepository =
            LocationRepositoryImpl(supabaseClient, supabaseService);
        familyMemberRepository =
            FamilyMemberRepositoryImpl(supabaseClient, supabaseService);
      });

      test('should create LocationRepository with enhanced service', () {
        expect(locationRepository, isNotNull);
        expect(locationRepository, isA<LocationRepositoryImpl>());
      });

      test('should create FamilyMemberRepository with enhanced service', () {
        expect(familyMemberRepository, isNotNull);
        expect(familyMemberRepository, isA<FamilyMemberRepositoryImpl>());
      });

      test('should validate household ID format in repositories', () {
        const validHouseholdId = '123e4567-e89b-12d3-a456-************';
        const invalidHouseholdId = 'invalid-id';

        expect(SupabaseUtils.isValidUUID(validHouseholdId), isTrue);
        expect(SupabaseUtils.isValidUUID(invalidHouseholdId), isFalse);
      });
    });

    group('Database Schema Compliance Tests', () {
      test('should validate Location entity fields match database schema', () {
        final location = Location(
          id: '123e4567-e89b-12d3-a456-************',
          householdId: '123e4567-e89b-12d3-a456-************',
          name: 'Test Location',
          description: 'Test Description',
          icon: 'home',
          createdAt: DateTime.now(),
        );

        expect(location.id, isA<String>());
        expect(location.householdId, isA<String>());
        expect(location.name, isA<String>());
        expect(location.description, isA<String?>());
        expect(location.icon, isA<String>());
        expect(location.createdAt, isA<DateTime>());
        expect(location.updatedAt, isA<DateTime?>());
      });

      test('should validate FamilyMember entity fields match database schema',
          () {
        final familyMember = FamilyMember(
          id: '123e4567-e89b-12d3-a456-************',
          householdId: '123e4567-e89b-12d3-a456-************',
          name: 'Test Member',
          relationship: 'Parent',
          dateOfBirth: DateTime.now().subtract(const Duration(days: 365 * 30)),
          createdAt: DateTime.now(),
        );

        expect(familyMember.id, isA<String>());
        expect(familyMember.householdId, isA<String>());
        expect(familyMember.name, isA<String>());
        expect(familyMember.relationship, isA<String>());
        expect(familyMember.dateOfBirth, isA<DateTime?>());
        expect(familyMember.createdAt, isA<DateTime>());
      });

      test('should validate required parameters for database operations', () {
        final validParams = {
          'household_id': '123e4567-e89b-12d3-a456-************',
          'name': 'Test Name',
        };

        final invalidParams = {
          'household_id': '',
          'name': null,
        };

        expect(SupabaseUtils.validateRequiredParams(validParams), isTrue);
        expect(SupabaseUtils.validateRequiredParams(invalidParams), isFalse);
      });
    });

    group('Error Handling Tests', () {
      test('should handle network connectivity errors', () {
        final errorMessage =
            SupabaseUtils.getErrorMessage('network connection failed');
        expect(errorMessage, contains('réseau'));
      });

      test('should handle authentication errors', () {
        final errorMessage = SupabaseUtils.getErrorMessage('401 unauthorized');
        expect(errorMessage, contains('reconnecter'));
      });

      test('should handle permission errors', () {
        final errorMessage = SupabaseUtils.getErrorMessage('403 forbidden');
        expect(errorMessage, contains('Accès refusé'));
      });

      test('should handle not found errors', () {
        final errorMessage = SupabaseUtils.getErrorMessage('404 not found');
        expect(errorMessage, contains('non trouvée'));
      });

      test('should handle generic database errors', () {
        final errorMessage =
            SupabaseUtils.getErrorMessage('500 internal server error');
        expect(errorMessage, contains('base de données'));
      });
    });

    group('Performance and Reliability Tests', () {
      test('should handle concurrent database operations', () async {
        final futures = List.generate(5, (index) {
          return supabaseService.executeWithRetry<String>(
            () async {
              await Future.delayed(const Duration(milliseconds: 100));
              return 'Operation $index completed';
            },
            operationName: 'concurrent_test_$index',
          );
        });

        final results = await Future.wait(futures);
        expect(results.length, equals(5));
        for (int i = 0; i < results.length; i++) {
          expect(results[i], equals('Operation $i completed'));
        }
      });

      test('should handle timeout scenarios gracefully', () async {
        expect(
          () => supabaseService.executeWithRetry<String>(
            () async {
              await Future.delayed(const Duration(seconds: 10));
              return 'Should timeout';
            },
            maxRetries: 1,
          ),
          throwsException,
        );
      });
    });
  });
}

/// Mock NetworkInfo for testing
class _MockNetworkInfo implements NetworkInfo {
  @override
  Future<bool> get isConnected async => true;

  @override
  Stream<bool> get onConnectivityChanged => Stream.value(true);
}

/// Mock SupabaseClient for testing
class _MockSupabaseClient implements SupabaseClient {
  @override
  dynamic noSuchMethod(Invocation invocation) => null;
}

import 'package:flutter/material.dart';
import '../../../domain/entities/dashboard_stats.dart';
import 'enhanced_status_card.dart';

class StatisticsGrid extends StatelessWidget {
  final DashboardStats stats;
  final Function(String type)? onStatisticTap;

  const StatisticsGrid({
    super.key,
    required this.stats,
    this.onStatisticTap,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.0, // Changed to 1:1 for square cards
      children: [
        EnhancedStatusCardFactory.total(
          value: '${stats.total}',
          subtitle: 'médicaments',
          onTap: () => onStatisticTap?.call('total'),
        ),
        EnhancedStatusCardFactory.expired(
          value: '${stats.expired}',
          subtitle: 'médicaments',
          onTap: () => onStatisticTap?.call('expired'),
        ),
        EnhancedStatusCardFactory.lowStock(
          value: '${stats.lowStock}',
          subtitle: 'médicaments',
          onTap: () => onStatisticTap?.call('low_stock'),
        ),
        EnhancedStatusCardFactory.expiringSoon(
          value: '${stats.expiringSoon}',
          subtitle: 'médicaments',
          onTap: () => onStatisticTap?.call('expiring'),
        ),
      ],
    );
  }
}

import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/usecases/auth/get_current_user_usecase.dart';
import '../../../domain/usecases/profile/update_profile_name_usecase.dart';
import '../../../domain/usecases/profile/update_email_usecase.dart';
import '../../../domain/usecases/profile/upload_avatar_usecase.dart';
import '../../../domain/usecases/profile/change_password_usecase.dart';
import '../../../domain/usecases/profile/delete_account_usecase.dart';
import '../../../domain/repositories/profile_repository.dart';
import 'profile_event.dart';
import 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  final GetCurrentUserUseCase getCurrentUserUseCase;
  final UpdateProfileNameUseCase updateProfileNameUseCase;
  final UpdateEmailUseCase updateEmailUseCase;
  final UploadAvatarUseCase uploadAvatarUseCase;
  final ChangePasswordUseCase changePasswordUseCase;
  final DeleteAccountUseCase deleteAccountUseCase;
  final ProfileRepository profileRepository;

  ProfileBloc({
    required this.getCurrentUserUseCase,
    required this.updateProfileNameUseCase,
    required this.updateEmailUseCase,
    required this.uploadAvatarUseCase,
    required this.changePasswordUseCase,
    required this.deleteAccountUseCase,
    required this.profileRepository,
  }) : super(ProfileInitial()) {
    on<ProfileLoadRequested>(_onLoadRequested);
    on<ProfileNameUpdateRequested>(_onNameUpdateRequested);
    on<ProfileEmailUpdateRequested>(_onEmailUpdateRequested);
    on<ProfileAvatarUpdateRequested>(_onAvatarUpdateRequested);
    on<ProfileAvatarRemoveRequested>(_onAvatarRemoveRequested);
    on<ProfilePasswordChangeRequested>(_onPasswordChangeRequested);
    on<ProfileLanguageUpdateRequested>(_onLanguageUpdateRequested);
    on<ProfileThemeUpdateRequested>(_onThemeUpdateRequested);
    on<ProfileAccountDeleteRequested>(_onAccountDeleteRequested);
    on<ProfileResetRequested>(_onResetRequested);
  }

  Future<void> _onLoadRequested(
    ProfileLoadRequested event,
    Emitter<ProfileState> emit,
  ) async {
    emit(ProfileLoading());

    try {
      // Get current user
      final userResult = await getCurrentUserUseCase();
      
      // Get avatar URL
      final avatarResult = await profileRepository.getAvatarUrl();
      
      // Get language preference
      final languageResult = await profileRepository.getLanguagePreference();
      
      // Get theme preference
      final themeResult = await profileRepository.getThemePreference();

      userResult.fold(
        (failure) => emit(ProfileError(message: failure.message)),
        (user) {
          final avatarUrl = avatarResult.fold((l) => null, (r) => r);
          final language = languageResult.fold((l) => 'fr', (r) => r);
          final isDarkMode = themeResult.fold((l) => false, (r) => r);

          emit(ProfileLoaded(
            user: user,
            avatarUrl: avatarUrl,
            language: language,
            isDarkMode: isDarkMode,
          ));
        },
      );
    } catch (e) {
      emit(ProfileError(message: 'Erreur lors du chargement du profil: $e'));
    }
  }

  Future<void> _onNameUpdateRequested(
    ProfileNameUpdateRequested event,
    Emitter<ProfileState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ProfileLoaded) return;

    emit(ProfileUpdating(
      user: currentState.user,
      avatarUrl: currentState.avatarUrl,
      language: currentState.language,
      isDarkMode: currentState.isDarkMode,
      updateType: 'name',
    ));

    final result = await updateProfileNameUseCase(
      UpdateProfileNameParams(name: event.name),
    );

    result.fold(
      (failure) => emit(ProfileError(
        message: failure.message,
        user: currentState.user,
        avatarUrl: currentState.avatarUrl,
        language: currentState.language,
        isDarkMode: currentState.isDarkMode,
      )),
      (user) => emit(ProfileUpdateSuccess(
        user: user,
        avatarUrl: currentState.avatarUrl,
        language: currentState.language,
        isDarkMode: currentState.isDarkMode,
        message: 'Nom mis à jour avec succès',
      )),
    );
  }

  Future<void> _onEmailUpdateRequested(
    ProfileEmailUpdateRequested event,
    Emitter<ProfileState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ProfileLoaded) return;

    emit(ProfileUpdating(
      user: currentState.user,
      avatarUrl: currentState.avatarUrl,
      language: currentState.language,
      isDarkMode: currentState.isDarkMode,
      updateType: 'email',
    ));

    final result = await updateEmailUseCase(
      UpdateEmailParams(email: event.email),
    );

    result.fold(
      (failure) => emit(ProfileError(
        message: failure.message,
        user: currentState.user,
        avatarUrl: currentState.avatarUrl,
        language: currentState.language,
        isDarkMode: currentState.isDarkMode,
      )),
      (_) => emit(ProfileUpdateSuccess(
        user: currentState.user,
        avatarUrl: currentState.avatarUrl,
        language: currentState.language,
        isDarkMode: currentState.isDarkMode,
        message: 'Email mis à jour. Vérifiez votre boîte de réception.',
      )),
    );
  }

  Future<void> _onAvatarUpdateRequested(
    ProfileAvatarUpdateRequested event,
    Emitter<ProfileState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ProfileLoaded) return;

    emit(ProfileUpdating(
      user: currentState.user,
      avatarUrl: currentState.avatarUrl,
      language: currentState.language,
      isDarkMode: currentState.isDarkMode,
      updateType: 'avatar',
    ));

    final result = await uploadAvatarUseCase(
      UploadAvatarParams(imageFile: event.imageFile),
    );

    result.fold(
      (failure) => emit(ProfileError(
        message: failure.message,
        user: currentState.user,
        avatarUrl: currentState.avatarUrl,
        language: currentState.language,
        isDarkMode: currentState.isDarkMode,
      )),
      (avatarUrl) => emit(ProfileUpdateSuccess(
        user: currentState.user,
        avatarUrl: avatarUrl,
        language: currentState.language,
        isDarkMode: currentState.isDarkMode,
        message: 'Avatar mis à jour avec succès',
      )),
    );
  }

  Future<void> _onAvatarRemoveRequested(
    ProfileAvatarRemoveRequested event,
    Emitter<ProfileState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ProfileLoaded) return;

    emit(ProfileUpdating(
      user: currentState.user,
      avatarUrl: currentState.avatarUrl,
      language: currentState.language,
      isDarkMode: currentState.isDarkMode,
      updateType: 'avatar',
    ));

    final result = await profileRepository.removeAvatar();

    result.fold(
      (failure) => emit(ProfileError(
        message: failure.message,
        user: currentState.user,
        avatarUrl: currentState.avatarUrl,
        language: currentState.language,
        isDarkMode: currentState.isDarkMode,
      )),
      (_) => emit(ProfileUpdateSuccess(
        user: currentState.user,
        avatarUrl: null,
        language: currentState.language,
        isDarkMode: currentState.isDarkMode,
        message: 'Avatar supprimé avec succès',
      )),
    );
  }

  Future<void> _onPasswordChangeRequested(
    ProfilePasswordChangeRequested event,
    Emitter<ProfileState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ProfileLoaded) return;

    emit(ProfileUpdating(
      user: currentState.user,
      avatarUrl: currentState.avatarUrl,
      language: currentState.language,
      isDarkMode: currentState.isDarkMode,
      updateType: 'password',
    ));

    final result = await changePasswordUseCase(
      ChangePasswordParams(
        currentPassword: event.currentPassword,
        newPassword: event.newPassword,
      ),
    );

    result.fold(
      (failure) => emit(ProfileError(
        message: failure.message,
        user: currentState.user,
        avatarUrl: currentState.avatarUrl,
        language: currentState.language,
        isDarkMode: currentState.isDarkMode,
      )),
      (_) => emit(ProfileUpdateSuccess(
        user: currentState.user,
        avatarUrl: currentState.avatarUrl,
        language: currentState.language,
        isDarkMode: currentState.isDarkMode,
        message: 'Mot de passe modifié avec succès',
      )),
    );
  }

  Future<void> _onLanguageUpdateRequested(
    ProfileLanguageUpdateRequested event,
    Emitter<ProfileState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ProfileLoaded) return;

    final result = await profileRepository.updateLanguagePreference(event.language);

    result.fold(
      (failure) => emit(ProfileError(
        message: failure.message,
        user: currentState.user,
        avatarUrl: currentState.avatarUrl,
        language: currentState.language,
        isDarkMode: currentState.isDarkMode,
      )),
      (_) => emit(ProfileLoaded(
        user: currentState.user,
        avatarUrl: currentState.avatarUrl,
        language: event.language,
        isDarkMode: currentState.isDarkMode,
      )),
    );
  }

  Future<void> _onThemeUpdateRequested(
    ProfileThemeUpdateRequested event,
    Emitter<ProfileState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ProfileLoaded) return;

    final result = await profileRepository.updateThemePreference(event.isDarkMode);

    result.fold(
      (failure) => emit(ProfileError(
        message: failure.message,
        user: currentState.user,
        avatarUrl: currentState.avatarUrl,
        language: currentState.language,
        isDarkMode: currentState.isDarkMode,
      )),
      (_) => emit(ProfileLoaded(
        user: currentState.user,
        avatarUrl: currentState.avatarUrl,
        language: currentState.language,
        isDarkMode: event.isDarkMode,
      )),
    );
  }

  Future<void> _onAccountDeleteRequested(
    ProfileAccountDeleteRequested event,
    Emitter<ProfileState> emit,
  ) async {
    emit(ProfileAccountDeleting());

    final result = await deleteAccountUseCase(
      DeleteAccountParams(password: event.password),
    );

    result.fold(
      (failure) => emit(ProfileError(message: failure.message)),
      (_) => emit(const ProfileAccountDeleted(
        message: 'Compte supprimé avec succès',
      )),
    );
  }

  Future<void> _onResetRequested(
    ProfileResetRequested event,
    Emitter<ProfileState> emit,
  ) async {
    emit(ProfileInitial());
  }
}

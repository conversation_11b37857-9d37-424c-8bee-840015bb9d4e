import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../bloc/family_manager/family_manager_event.dart';

class FamilyMemberSortDialog extends StatelessWidget {
  final FamilyMemberSortOption currentSort;
  final Function(FamilyMemberSortOption) onSortChanged;

  const FamilyMemberSortDialog({
    super.key,
    required this.currentSort,
    required this.onSortChanged,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        'Trier par',
        style: AppTextStyles.titleMedium.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      contentPadding: const EdgeInsets.symmetric(vertical: 16),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildSortGroup(
              context,
              title: 'Nom',
              options: [
                FamilyMemberSortOption.nameAsc,
                FamilyMemberSortOption.nameDesc,
              ],
            ),
            const Divider(height: 1),
            _buildSortGroup(
              context,
              title: 'Âge',
              options: [
                FamilyMemberSortOption.ageAsc,
                FamilyMemberSortOption.ageDesc,
              ],
            ),
            const Divider(height: 1),
            _buildSortGroup(
              context,
              title: 'Relation familiale',
              options: [
                FamilyMemberSortOption.relationshipAsc,
                FamilyMemberSortOption.relationshipDesc,
              ],
            ),
            const Divider(height: 1),
            _buildSortGroup(
              context,
              title: 'Date d\'ajout',
              options: [
                FamilyMemberSortOption.createdAtAsc,
                FamilyMemberSortOption.createdAtDesc,
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Fermer'),
        ),
      ],
    );
  }

  Widget _buildSortGroup(
    BuildContext context, {
    required String title,
    required List<FamilyMemberSortOption> options,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
          child: Text(
            title,
            style: AppTextStyles.labelMedium.copyWith(
              color: AppColors.grey600,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        ...options.map((option) => _buildSortOption(context, option)),
      ],
    );
  }

  Widget _buildSortOption(BuildContext context, FamilyMemberSortOption option) {
    final isSelected = currentSort == option;

    return ListTile(
      dense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 24),
      leading: Icon(
        _getSortIcon(option),
        size: 20,
        color: isSelected ? AppColors.teal : AppColors.grey600,
      ),
      title: Text(
        option.displayName,
        style: AppTextStyles.bodyMedium.copyWith(
          color: isSelected ? AppColors.teal : AppColors.grey800,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
        ),
      ),
      trailing: isSelected
          ? Icon(
              Icons.check,
              color: AppColors.teal,
              size: 20,
            )
          : null,
      onTap: () {
        onSortChanged(option);
        Navigator.of(context).pop();
      },
    );
  }

  IconData _getSortIcon(FamilyMemberSortOption option) {
    IconData baseIcon;
    
    switch (option) {
      case FamilyMemberSortOption.nameAsc:
      case FamilyMemberSortOption.nameDesc:
        baseIcon = Icons.sort_by_alpha;
        break;
      case FamilyMemberSortOption.ageAsc:
      case FamilyMemberSortOption.ageDesc:
        baseIcon = Icons.cake;
        break;
      case FamilyMemberSortOption.relationshipAsc:
      case FamilyMemberSortOption.relationshipDesc:
        baseIcon = Icons.family_restroom;
        break;
      case FamilyMemberSortOption.createdAtAsc:
      case FamilyMemberSortOption.createdAtDesc:
        baseIcon = Icons.access_time;
        break;
    }

    return baseIcon;
  }
}

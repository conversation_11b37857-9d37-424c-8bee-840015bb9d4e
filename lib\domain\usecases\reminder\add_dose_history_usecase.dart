import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/reminder.dart';
import '../../repositories/reminder_repository.dart';

@injectable
class AddDoseHistoryUseCase implements UseCase<Unit, AddDoseHistoryParams> {
  final ReminderRepository repository;

  AddDoseHistoryUseCase(this.repository);

  @override
  Future<Either<Failure, Unit>> call(AddDoseHistoryParams params) async {
    return await repository.addDoseHistory(params.doseHistory);
  }
}

class AddDoseHistoryParams {
  final DoseHistory doseHistory;

  AddDoseHistoryParams({required this.doseHistory});
}

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mocktail/mocktail.dart';

import 'package:medytrack_mobile_v2/presentation/pages/settings/settings_page.dart';
import 'package:medytrack_mobile_v2/presentation/bloc/auth/auth_bloc.dart';
import 'package:medytrack_mobile_v2/presentation/bloc/auth/auth_state.dart'
    as auth_state;
import 'package:medytrack_mobile_v2/domain/entities/user.dart';

// Mock classes
class MockAuthBloc extends Mock implements AuthBloc {}

class MockUser extends Mock implements User {}

void main() {
  group('SettingsPage Tests', () {
    late MockAuthBloc mockAuthBloc;
    late MockUser mockUser;

    setUp(() {
      mockAuthBloc = MockAuthBloc();
      mockUser = MockUser();

      // Setup mock user data
      when(() => mockUser.email).thenReturn('<EMAIL>');
      when(() => mockUser.name).thenReturn('<PERSON>');
      when(() => mockUser.id).thenReturn('test-user-id');
      when(() => mockUser.displayName).thenReturn('<PERSON>e');
      when(() => mockUser.initials).thenReturn('JD');
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: BlocProvider<AuthBloc>(
          create: (context) => mockAuthBloc,
          child: const SettingsPage(),
        ),
      );
    }

    testWidgets('should display loading indicator when not authenticated',
        (WidgetTester tester) async {
      // Arrange
      when(() => mockAuthBloc.state).thenReturn(auth_state.AuthLoading());

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should display settings sections when authenticated',
        (WidgetTester tester) async {
      // Arrange
      when(() => mockAuthBloc.state).thenReturn(
        auth_state.AuthAuthenticated(
          user: mockUser,
          householdId: 'test-household',
          householdName: 'Test Household',
        ),
      );

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Paramètres'), findsOneWidget);
      expect(find.text('Préférences'), findsOneWidget);
      expect(find.text('Données'), findsOneWidget);
      expect(find.text('Compte'), findsOneWidget);
      expect(find.text('À propos'), findsOneWidget);
    });

    testWidgets('should display user profile section',
        (WidgetTester tester) async {
      // Arrange
      when(() => mockAuthBloc.state).thenReturn(
        auth_state.AuthAuthenticated(
          user: mockUser,
          householdId: 'test-household',
          householdName: 'Test Household',
        ),
      );

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.byIcon(Icons.edit), findsOneWidget);
    });

    testWidgets('should display preferences section with settings tiles',
        (WidgetTester tester) async {
      // Arrange
      when(() => mockAuthBloc.state).thenReturn(
        auth_state.AuthAuthenticated(
          user: mockUser,
          householdId: 'test-household',
          householdName: 'Test Household',
        ),
      );

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Notifications'), findsOneWidget);
      expect(find.text('Langue'), findsOneWidget);
      expect(find.text('Thème'), findsOneWidget);
      expect(find.text('Format de date'), findsOneWidget);
    });

    testWidgets('should display data management section',
        (WidgetTester tester) async {
      // Arrange
      when(() => mockAuthBloc.state).thenReturn(
        auth_state.AuthAuthenticated(
          user: mockUser,
          householdId: 'test-household',
          householdName: 'Test Household',
        ),
      );

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Synchronisation'), findsOneWidget);
      expect(find.text('Sauvegarde'), findsOneWidget);
      expect(find.text('Exporter les données'), findsOneWidget);
    });

    testWidgets('should display account section', (WidgetTester tester) async {
      // Arrange
      when(() => mockAuthBloc.state).thenReturn(
        auth_state.AuthAuthenticated(
          user: mockUser,
          householdId: 'test-household',
          householdName: 'Test Household',
        ),
      );

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Famille'), findsOneWidget);
      expect(find.text('Sécurité'), findsOneWidget);
      expect(find.text('Confidentialité'), findsOneWidget);
    });

    testWidgets('should display about section', (WidgetTester tester) async {
      // Arrange
      when(() => mockAuthBloc.state).thenReturn(
        auth_state.AuthAuthenticated(
          user: mockUser,
          householdId: 'test-household',
          householdName: 'Test Household',
        ),
      );

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Version de l\'application'), findsOneWidget);
      expect(find.text('Conditions d\'utilisation'), findsOneWidget);
      expect(find.text('Politique de confidentialité'), findsOneWidget);
      expect(find.text('Aide et support'), findsOneWidget);
    });

    testWidgets('should display logout button', (WidgetTester tester) async {
      // Arrange
      when(() => mockAuthBloc.state).thenReturn(
        auth_state.AuthAuthenticated(
          user: mockUser,
          householdId: 'test-household',
          householdName: 'Test Household',
        ),
      );

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Se déconnecter'), findsOneWidget);
      expect(find.byIcon(Icons.logout), findsOneWidget);
    });
  });
}

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mocktail/mocktail.dart';

import 'package:medytrack_mobile_v2/presentation/pages/medicine/medicine_list_page.dart';
import 'package:medytrack_mobile_v2/presentation/pages/medicine/add_medicine_page.dart';
import 'package:medytrack_mobile_v2/presentation/bloc/medicine/medicine_bloc.dart';
import 'package:medytrack_mobile_v2/presentation/bloc/auth/auth_bloc.dart';
import 'package:medytrack_mobile_v2/presentation/bloc/auth/auth_state.dart'
    as auth_state;
import 'package:medytrack_mobile_v2/domain/entities/medicine.dart';
import 'package:medytrack_mobile_v2/domain/entities/user.dart';

// Mock classes
class MockMedicineBloc extends Mock implements MedicineBloc {}

class MockAuthBloc extends Mock implements AuthBloc {}

class MockUser extends Mock implements User {}

void main() {
  group('Medicine Pages Tests', () {
    late MockMedicineBloc mockMedicineBloc;
    late MockAuthBloc mockAuthBloc;
    late MockUser mockUser;

    setUp(() {
      mockMedicineBloc = MockMedicineBloc();
      mockAuthBloc = MockAuthBloc();
      mockUser = MockUser();

      // Setup mock user data
      when(() => mockUser.email).thenReturn('<EMAIL>');
      when(() => mockUser.householdId).thenReturn('test-household');
      when(() => mockAuthBloc.state).thenReturn(
        auth_state.AuthAuthenticated(
          user: mockUser,
          householdId: 'test-household',
          householdName: 'Test Household',
        ),
      );
    });

    group('MedicineListPage Tests', () {
      Widget createMedicineListWidget() {
        return MaterialApp(
          home: MultiBlocProvider(
            providers: [
              BlocProvider<MedicineBloc>(create: (context) => mockMedicineBloc),
              BlocProvider<AuthBloc>(create: (context) => mockAuthBloc),
            ],
            child: const MedicineListPage(),
          ),
        );
      }

      testWidgets('should display loading indicator when medicines are loading',
          (WidgetTester tester) async {
        // Arrange
        when(() => mockMedicineBloc.state).thenReturn(const MedicineLoading());

        // Act
        await tester.pumpWidget(createMedicineListWidget());

        // Assert
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });

      testWidgets('should display empty state when no medicines',
          (WidgetTester tester) async {
        // Arrange
        when(() => mockMedicineBloc.state)
            .thenReturn(const MedicineLoaded(medicines: []));

        // Act
        await tester.pumpWidget(createMedicineListWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Aucun médicament'), findsOneWidget);
        expect(find.text('Commencez par ajouter votre premier médicament'),
            findsOneWidget);
        expect(find.text('Ajouter un médicament'), findsAtLeastNWidgets(1));
        expect(find.byIcon(Icons.medication_outlined), findsOneWidget);
      });

      testWidgets('should display error state with retry button',
          (WidgetTester tester) async {
        // Arrange
        when(() => mockMedicineBloc.state)
            .thenReturn(const MedicineError(message: 'Test error'));

        // Act
        await tester.pumpWidget(createMedicineListWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Erreur'), findsOneWidget);
        expect(find.text('Test error'), findsOneWidget);
        expect(find.text('Réessayer'), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
      });

      testWidgets('should display medicines when loaded',
          (WidgetTester tester) async {
        // Arrange
        final testMedicine = Medicine(
          id: 'test-id',
          householdId: 'test-household',
          customName: 'Test Medicine',
          isCustom: true,
          quantity: 10,
          createdAt: DateTime.now(),
        );

        when(() => mockMedicineBloc.state)
            .thenReturn(MedicineLoaded(medicines: [testMedicine]));

        // Act
        await tester.pumpWidget(createMedicineListWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Test Medicine'), findsOneWidget);
        expect(find.byType(RefreshIndicator), findsOneWidget);
      });

      testWidgets('should have add medicine button in app bar',
          (WidgetTester tester) async {
        // Arrange
        when(() => mockMedicineBloc.state)
            .thenReturn(const MedicineLoaded(medicines: []));

        // Act
        await tester.pumpWidget(createMedicineListWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Médicaments'), findsOneWidget);
        expect(find.byIcon(Icons.add), findsAtLeastNWidgets(1));
      });
    });

    group('AddMedicinePage Tests', () {
      Widget createAddMedicineWidget() {
        return MaterialApp(
          home: MultiBlocProvider(
            providers: [
              BlocProvider<MedicineBloc>(create: (context) => mockMedicineBloc),
              BlocProvider<AuthBloc>(create: (context) => mockAuthBloc),
            ],
            child: const AddMedicinePage(),
          ),
        );
      }

      testWidgets('should display add medicine form',
          (WidgetTester tester) async {
        // Arrange
        when(() => mockMedicineBloc.state).thenReturn(const MedicineInitial());

        // Act
        await tester.pumpWidget(createAddMedicineWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Ajouter un médicament'), findsOneWidget);
        expect(find.text('Informations de base'), findsOneWidget);
        expect(find.text('Quantité et stock'), findsOneWidget);
        expect(find.text('Localisation'), findsOneWidget);
        expect(find.text('Notes'), findsOneWidget);
      });

      testWidgets('should display form fields', (WidgetTester tester) async {
        // Arrange
        when(() => mockMedicineBloc.state).thenReturn(const MedicineInitial());

        // Act
        await tester.pumpWidget(createAddMedicineWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Nom du médicament *'), findsOneWidget);
        expect(find.text('Dosage'), findsOneWidget);
        expect(find.text('Forme'), findsOneWidget);
        expect(find.text('Date d\'expiration'), findsOneWidget);
        expect(find.text('Quantité *'), findsOneWidget);
        expect(find.text('Seuil stock faible'), findsOneWidget);
        expect(find.text('Emplacement'), findsOneWidget);
        expect(find.text('Notes additionnelles'), findsOneWidget);
      });

      testWidgets('should display submit button', (WidgetTester tester) async {
        // Arrange
        when(() => mockMedicineBloc.state).thenReturn(const MedicineInitial());

        // Act
        await tester.pumpWidget(createAddMedicineWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Ajouter le médicament'), findsOneWidget);
      });

      testWidgets('should show loading state when submitting',
          (WidgetTester tester) async {
        // Arrange
        when(() => mockMedicineBloc.state).thenReturn(const MedicineLoading());

        // Act
        await tester.pumpWidget(createAddMedicineWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Ajout en cours...'), findsOneWidget);
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });
    });
  });
}

import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/entities/settings.dart';
import 'settings_event.dart';
import 'settings_state.dart';

/// Simplified BLoC for managing settings
class SettingsBloc extends Bloc<SettingsEvent, SettingsState> {
  SettingsBloc() : super(const SettingsInitial()) {
    on<SettingsLoadRequested>(_onLoadRequested);
    on<NotificationSettingsUpdateRequested>(
        _onNotificationSettingsUpdateRequested);
    on<LanguageUpdateRequested>(_onLanguageUpdateRequested);
    on<ThemeUpdateRequested>(_onThemeUpdateRequested);
    on<AppSettingsUpdateRequested>(_onAppSettingsUpdateRequested);
  }

  Future<void> _onLoadRequested(
    SettingsLoadRequested event,
    Emitter<SettingsState> emit,
  ) async {
    emit(const SettingsLoading());

    try {
      // Simulate loading delay
      await Future.delayed(const Duration(milliseconds: 500));

      // Create mock settings data using factory method
      final mockSettings = Settings.defaultSettings(
        userId: event.userId.isEmpty ? 'mock-user-id' : event.userId,
        householdId: 'mock-household-id',
      );

      emit(SettingsLoaded(settings: mockSettings));
    } catch (e) {
      emit(SettingsError(
          message: 'Erreur lors du chargement des paramètres: $e'));
    }
  }

  Future<void> _onNotificationSettingsUpdateRequested(
    NotificationSettingsUpdateRequested event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      emit(SettingsLoading());

      try {
        // Simulate update delay
        await Future.delayed(const Duration(milliseconds: 300));

        final updatedSettings = currentState.settings.copyWith(
          notifications: event.settings,
        );

        emit(SettingsLoaded(settings: updatedSettings));
      } catch (e) {
        emit(SettingsError(message: 'Erreur lors de la mise à jour: $e'));
      }
    }
  }

  Future<void> _onLanguageUpdateRequested(
    LanguageUpdateRequested event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      emit(SettingsLoading());

      try {
        // Simulate update delay
        await Future.delayed(const Duration(milliseconds: 300));

        final updatedSettings = currentState.settings.copyWith(
          app: currentState.settings.app.copyWith(language: event.language),
        );

        emit(SettingsLoaded(settings: updatedSettings));
      } catch (e) {
        emit(SettingsError(message: 'Erreur lors de la mise à jour: $e'));
      }
    }
  }

  Future<void> _onThemeUpdateRequested(
    ThemeUpdateRequested event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      emit(SettingsLoading());

      try {
        // Simulate update delay
        await Future.delayed(const Duration(milliseconds: 300));

        final updatedSettings = currentState.settings.copyWith(
          app: currentState.settings.app.copyWith(isDarkMode: event.isDarkMode),
        );

        emit(SettingsLoaded(settings: updatedSettings));
      } catch (e) {
        emit(SettingsError(message: 'Erreur lors de la mise à jour: $e'));
      }
    }
  }

  Future<void> _onAppSettingsUpdateRequested(
    AppSettingsUpdateRequested event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      emit(SettingsLoading());

      try {
        // Simulate update delay
        await Future.delayed(const Duration(milliseconds: 300));

        final updatedSettings = currentState.settings.copyWith(
          app: event.settings,
        );

        emit(SettingsLoaded(settings: updatedSettings));
      } catch (e) {
        emit(SettingsError(message: 'Erreur lors de la mise à jour: $e'));
      }
    }
  }
}

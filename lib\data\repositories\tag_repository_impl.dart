import 'package:injectable/injectable.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../core/utils/supabase_utils.dart';
import '../../domain/entities/tag.dart';
import '../../domain/repositories/tag_repository.dart';
import '../models/tag_model.dart';

@Injectable(as: TagRepository)
class TagRepositoryImpl implements TagRepository {
  final SupabaseClient _supabaseClient;

  TagRepositoryImpl(this._supabaseClient);

  @override
  Stream<List<Tag>> getHouseholdTags(String householdId) {
    // Validate household ID before creating stream
    if (!SupabaseUtils.isValidUUID(householdId)) {
      return Stream.error(
          Exception('Invalid household ID format: $householdId'));
    }

    return _supabaseClient
        .from('tags')
        .stream(primaryKey: ['id'])
        .eq('household_id', householdId)
        .order('category')
        .order('name')
        .map((data) => data
            .map((json) => TagModel.fromJson(json))
            .map((model) => model.toEntity())
            .toList())
        .handleError((error) {
          throw Exception(SupabaseUtils.getErrorMessage(error));
        });
  }

  @override
  Future<List<Tag>> getTagsByCategory(
      String householdId, String category) async {
    try {
      // Validate required parameters
      final requiredParams = {
        'household_id': householdId,
        'category': category,
      };

      if (!SupabaseUtils.validateRequiredParams(requiredParams)) {
        throw Exception('Missing required parameters for tag retrieval');
      }

      final response = await _supabaseClient
          .from('tags')
          .select()
          .eq('household_id', householdId)
          .eq('category', category)
          .order('name');

      final List<dynamic> data = response;

      return data
          .map((json) => TagModel.fromJson(json))
          .map((model) => model.toEntity())
          .toList();
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }

  @override
  Future<Tag> createTag(Tag tag) async {
    try {
      // Validate required parameters
      final requiredParams = {
        'household_id': tag.householdId,
        'name': tag.name,
        'category': tag.category,
      };

      if (!SupabaseUtils.validateRequiredParams(requiredParams)) {
        throw Exception('Missing required parameters for tag creation');
      }

      final model = TagModel.fromEntity(tag);
      final response = await _supabaseClient
          .from('tags')
          .insert(model.toJson())
          .select()
          .single();

      final createdModel = TagModel.fromJson(response);
      return createdModel.toEntity();
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }

  @override
  Future<Tag> updateTag(Tag tag) async {
    try {
      // Validate required parameters
      final requiredParams = {
        'id': tag.id,
        'household_id': tag.householdId,
        'name': tag.name,
        'category': tag.category,
      };

      if (!SupabaseUtils.validateRequiredParams(requiredParams)) {
        throw Exception('Missing required parameters for tag update');
      }

      final model = TagModel.fromEntity(tag);
      final response = await _supabaseClient
          .from('tags')
          .update(model.toJson())
          .eq('id', tag.id)
          .select()
          .single();

      final updatedModel = TagModel.fromJson(response);
      return updatedModel.toEntity();
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }

  @override
  Future<void> deleteTag(String tagId) async {
    try {
      // Validate tag ID
      if (!SupabaseUtils.isValidUUID(tagId)) {
        throw Exception('Invalid tag ID format: $tagId');
      }

      await _supabaseClient.from('tags').delete().eq('id', tagId);
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }

  @override
  Future<void> addTagToMedicine(String medicineId, String tagId) async {
    try {
      // Validate required parameters
      final requiredParams = {
        'medicine_id': medicineId,
        'tag_id': tagId,
      };

      if (!SupabaseUtils.validateRequiredParams(requiredParams)) {
        throw Exception('Missing required parameters for tag association');
      }

      await _supabaseClient.from('medicine_tag_assignments').insert({
        'medicine_id': medicineId,
        'tag_id': tagId,
      });
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }

  @override
  Future<void> removeTagFromMedicine(String medicineId, String tagId) async {
    try {
      // Validate required parameters
      final requiredParams = {
        'medicine_id': medicineId,
        'tag_id': tagId,
      };

      if (!SupabaseUtils.validateRequiredParams(requiredParams)) {
        throw Exception('Missing required parameters for tag removal');
      }

      await _supabaseClient
          .from('medicine_tag_assignments')
          .delete()
          .eq('medicine_id', medicineId)
          .eq('tag_id', tagId);
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }

  @override
  Future<List<Tag>> getMedicineTags(String medicineId) async {
    try {
      // Validate medicine ID
      if (!SupabaseUtils.isValidUUID(medicineId)) {
        throw Exception('Invalid medicine ID format: $medicineId');
      }

      final response = await _supabaseClient
          .from('medicine_tag_assignments')
          .select('tags(*)')
          .eq('medicine_id', medicineId);

      final List<dynamic> data = response;

      return data
          .map((item) => item['tags'] as Map<String, dynamic>)
          .map((json) => TagModel.fromJson(json))
          .map((model) => model.toEntity())
          .toList();
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }
}

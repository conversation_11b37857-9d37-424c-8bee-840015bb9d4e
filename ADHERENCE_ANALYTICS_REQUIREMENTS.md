
# Comprehensive Project Requirements: MedyTrack Mobile

**Document Version:** 1.0
**Version:** 1.0
**Status:** Active

---

This document provides a comprehensive overview of the functional requirements for the MedyTrack Mobile application. It is structured to serve as a knowledge base for development and for AI coding assistants.

| Requirement ID | Description | User Story | Expected Behaviour |
| :--- | :--- | :--- | :--- |
| **MED-FUNC-001** | **User Authentication & Management** | As a user, I want to securely create an account, log in, and manage my profile, so that my personal health data is kept private and accessible only to me. | **Workflow:**<br>1. User provides email/password for registration or login.<br>2. On success, the user is navigated to the main dashboard.<br>3. The user can navigate to a profile page to update their display name and avatar.<br><br>**Supabase Interaction:**<br>- Uses `supabase.auth.signUp()` and `supabase.auth.signInWithPassword()` for authentication.<br>- User session is managed by the `supabase_flutter` package.<br>- All data tables containing user-specific information (e.g., `user_medicines`, `reminders`) have Row Level Security (RLS) policies enabled, filtering data based on `auth.uid()`.<br><br>**Actions to be Created:**<br>- `AuthBloc` manages states like `Authenticated`, `Unauthenticated`, `AuthInProgress`.<br>- Events include `LoginRequested`, `RegistrationRequested`, `LogoutRequested`. |
| **MED-FUNC-002** | **Medicine Management** | As a user, I want to add, view, edit, and delete my medications, including details like dosage and inventory, so that I can maintain an accurate, centralized list of all my treatments. | **Workflow:**<br>1. User navigates to the "My Medicines" screen.<br>2. User taps an "Add" button to open a form.<br>3. User inputs medicine details (name, dosage, form, inventory count, associated family member, storage location).<br>4. Upon saving, the medicine appears in their list.<br>5. Existing medicines can be tapped to edit or delete.<br><br>**Supabase Interaction:**<br>- Full CRUD operations on the `user_medicines` table.<br>- RLS policies ensure users can only access their own records.<br>- The `medicines` table might be used for auto-completing common medicine names.<br><br>**Actions to be Created:**<br>- `MyMedicinesBloc` handles loading and modification events.<br>- Events include `LoadMyMedicines`, `AddUserMedicine`, `UpdateUserMedicine`, `DeleteUserMedicine`. |
| **MED-FUNC-003** | **Reminder System** | As a user with a complex medication schedule, I want to set up flexible reminders (e.g., daily, on specific weekdays, or at hourly intervals), so that I never miss a dose. | **Workflow:**<br>1. From a medicine's detail view, the user selects "Add Reminder".<br>2. A modal or new page appears for configuration.<br>3. User selects a frequency type: `DAILY`, `WEEKLY`, or `INTERVAL`.<br>4. User selects specific times (for Daily/Weekly) or an interval in hours.<br>5. User sets a start and optional end date.<br>6. Upon saving, the reminder is activated and local notifications are scheduled.<br><br>**Supabase Interaction:**<br>- Creates, updates, and deletes records in the `reminders` table.<br>- The `reminders` table is linked to `user_medicines`.<br>- RLS policies on `reminders` are enforced by checking ownership of the linked `user_medicine_id`, as defined in `fix_rls_policies.sql`.<br><br>**Actions to be Created:**<br>- `ReminderBloc` manages all reminder logic.<br>- Events: `AddReminder`, `UpdateReminder`, `DeleteReminder`, `LoadRemindersForMedicine`.<br>- The `flutter_local_notifications` package is used to schedule device notifications based on the created reminder. |
| **MED-FUNC-004** | **Dashboard & Dose Tracking** | As a user, I want to see all my scheduled doses for today on a single screen and quickly mark them as "Taken", "Skipped", or "Snoozed", so I can easily follow my regimen and maintain an accurate history. | **Workflow:**<br>1. The app opens to the Dashboard, which displays a list of today's scheduled doses.<br>2. Each list item has interactive buttons: "Take", "Skip", "Snooze".<br>3. Clicking a button triggers the action and provides immediate visual feedback.<br>4. The action is recorded permanently.<br><br>**Supabase Interaction:**<br>- Reads from `reminders` and `user_medicines` to build the daily schedule.<br>- On action, a new record is **inserted** into the `dose_history` table.<br>- **Critical**: The JSON payload for the insert must **exclude the `id` field** if it is null, allowing the PostgreSQL `SERIAL` type to auto-generate the primary key. This was a key fix noted in `DATABASE_FIXES_COMPLETION_REPORT.md`.<br><br>**Actions to be Created:**<br>- A `DashboardBloc` or similar loads the data for the current day.<br>- A `DoseHistoryBloc` or the `ReminderBloc` handles the `AddDoseHistory` event, which takes the dose details and a status (`TAKEN`, `SKIPPED`, `SNOOZED`). |
| **MED-FUNC-005** | **Settings & Personalization** | As a user, I want to manage app settings, such as family members, storage locations, and notification preferences, so that I can customize the app to fit my specific needs. | **Workflow:**<br>1. User navigates to the "Settings" page.<br>2. User can access sub-pages for managing "Family Members" and "Locations".<br>3. These sub-pages provide full CRUD functionality for their respective items.<br>4. User can also configure notification settings, like default snooze times.<br><br>**Supabase Interaction:**<br>- CRUD operations on `family_members` and `locations` tables (assuming these exist).<br>- RLS policies on these tables must be tied to the `user_id`.<br>- User preferences might be stored in a `user_profiles` table.<br><br>**Actions to be Created:**<br>- Dedicated BLoCs for each settings section (e.g., `FamilyMemberBloc`, `LocationBloc`).<br>- Events would include `LoadFamilyMembers`, `AddFamilyMember`, etc. |
| **MED-FUNC-006** | **Adherence Analytics Dashboard** | As a user, I want to view a dashboard with clear charts and statistics about my medication history, so that I can easily track my adherence, identify patterns, and share a summary with my healthcare provider. | **Workflow:**<br>1. User navigates to the "Analytics" screen.<br>2. The page displays filters for date range (e.g., Last 7/30/90 days) and family member.<br>3. The dashboard shows an overall adherence score, key stats (doses taken/skipped), a trend chart, and a dose status breakdown chart.<br>4. The UI handles loading, empty, and error states gracefully.<br><br>**Supabase Interaction:**<br>- To ensure performance, this feature will **not** query the `dose_history` table directly from the client for aggregation.<br>- It will call a PostgreSQL function (e.g., `get_adherence_summary`) via RPC.<br>- The function will perform all calculations (counts, percentages) on the server and return a single JSON object.<br>- The function must be created with `SECURITY DEFINER` to access data across RLS policies but must internally filter by the `user_id` passed as a parameter.<br><br>**Actions to be Created:**<br>- `AnalyticsBloc` will manage the state.<br>- A single event `LoadAnalyticsData(dateRange, familyMemberId)` will trigger the process.<br>- The BLoC will emit `AnalyticsLoading`, `AnalyticsLoaded(stats)`, or `AnalyticsError` states. |

The purpose of this feature is to provide users with a visual and statistical overview of their medication adherence over time. It will introduce a new, dedicated screen that leverages the data collected in the `dose_history` table to generate insightful charts and key performance metrics.

This feature directly addresses the "Advanced Analytics" item on the future enhancement roadmap (see `PROJECT_KNOWLEDGE_BASE.md`). It will be implemented following the established Clean Architecture + BLoC pattern, utilize the existing Material Design 3 component library, and be fully internationalized.

## 2. User Story

**As a** user managing one or more medications for myself or my family,
**I want to** view a dashboard with clear charts and statistics about my medication history (e.g., doses taken vs. skipped),
**So that I can** easily track my adherence, identify patterns in my habits, and share a summary of my progress with my healthcare provider.

## 3. Expected Behaviour / Outcome

### 3.1. User Interface & Navigation

- **Access Point**: A new "Analytics" or "Progress" item will be added to the main `HamburgerMenu`.
- **Navigation**: Tapping this menu item will navigate the user to a new `/analytics` route managed by `GoRouter`.
- **Screen Layout**: The new `AnalyticsPage` will use the `HeaderScreen` and `ScreenContainer` layout widgets for consistency. It will feature a `SimpleHeader` with the title "Adherence Analytics".
- **State Handling**: The page must gracefully handle different data states:
    - **Loading State**: A `LoadingState` widget will be displayed while data is being fetched.
    - **Empty State**: An `EmptyState` widget will be shown if the user has no dose history for the selected period, with a message like "No adherence data available yet. Start tracking your doses to see your progress!"
    - **Error State**: An `ErrorState` widget will be displayed if data fetching fails, including a "Retry" button.

### 3.2. Core Components & Metrics

The dashboard will be composed of several key visual components:

- **Overall Adherence Score**: A prominent circular progress indicator (or similar gauge) displaying the overall adherence percentage for the selected period. This is calculated as `(TAKEN / (TAKEN + SKIPPED)) * 100`.
- **Key Statistics Cards**: A horizontal row of `InfoCard` or `SpecialistCard` widgets displaying:
    - Total Doses Taken
    - Total Doses Skipped
    - Longest Streak (number of consecutive days with 100% adherence).
- **Adherence Trend Chart**: A bar chart visualizing adherence trends.
    - For "Last 7 Days" view: Shows daily adherence percentage for each of the last 7 days.
    - For "Last 30 Days" view: Shows weekly adherence percentage for each of the last 4 weeks.
- **Dose Status Breakdown**: A pie or donut chart showing the proportion of all dose actions: `TAKEN`, `SKIPPED`, and `SNOOZED`.

### 3.3. Interactivity & Filtering

- **Date Range Filter**: A set of `ModernChipSelection` or `ModernToggleButtons` will allow the user to filter the entire dashboard by a time period.
    - **Options**: "Last 7 Days", "Last 30 Days", "Last 90 Days".
    - **Default**: "Last 30 Days".
- **Family Member Filter**: If the user has family members configured, a `ModernDropdown` will be displayed to filter analytics for a specific person or "All Members". This filter is hidden if no family members exist.
- **Export/Share**: A "Share" icon in the `SimpleHeader` will allow the user to generate and share a simple, non-interactive summary (e.g., an image or PDF) of the current dashboard view.

### 3.4. Technical Implementation

The implementation must strictly adhere to the project's established patterns.

- **Architecture**:
    - **Presentation Layer**:
        - A new `AnalyticsBloc` will be created to manage the page's state (`AnalyticsState` with `loading`, `loaded`, `error` unions via `freezed`).
        - `AnalyticsEvent` will be used to trigger data loading (e.g., `LoadAnalyticsData(dateRange, familyMemberId)`).
        - The `AnalyticsPage` will use `BlocProvider` to supply the BLoC and `BlocBuilder` to render the UI based on the current state.
    - **Domain Layer**:
        - A new `GetAdherenceStats` use case will orchestrate the data flow from the repository.
        - The `DoseHistoryRepository` interface will be extended with a new method: `Future<Either<Failure, AdherenceStats>> getAdherenceStats({required DateRange range, String? familyMemberId});`.
        - A new `AdherenceStats` entity will be created to model the aggregated data returned from the repository.
    - **Data Layer**:
        - The `DoseHistoryRepositoryImpl` will implement the new repository method.
        - The `DoseHistoryRemoteDataSource` will be updated to call a new Supabase RPC function.

### 3.5. Backend & Data Source

- **Primary Data Source**: All analytics will be calculated exclusively from the `dose_history` table.
- **Performance**: To ensure performance and minimize data transfer, a new PostgreSQL function (e.g., `get_adherence_summary`) will be created in Supabase and exposed via RPC.
    - **Function Signature**: `get_adherence_summary(start_date_in TEXT, end_date_in TEXT, user_id_in UUID)`
    - **Logic**: The function will perform all aggregations (counts, percentages, streaks) on the server side.
    - **Security**: The function must be defined with `SECURITY DEFINER` to correctly query data across RLS policies, while internally filtering results based on the `user_id_in` parameter, which will be `auth.uid()`.
    - **Return Value**: The function will return a single JSON object containing all the necessary metrics for the dashboard.
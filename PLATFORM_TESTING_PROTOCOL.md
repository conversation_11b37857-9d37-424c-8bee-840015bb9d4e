# MedyTrack Mobile v0.5.0 - Platform Testing Protocol

## 🧪 Comprehensive Platform Testing for Error Tracking System

Execute the following testing protocol on native platforms to verify error tracking functionality before production release.

## 📱 Android Testing Protocol

### Debug Build Testing

#### Build and Install
```bash
# 1. Clean and build debug APK
flutter clean
flutter pub get
flutter build apk --debug

# 2. Install on Android device/emulator
adb install build/app/outputs/flutter-apk/app-debug.apk

# 3. Launch application
adb shell am start -n com.medytrack.mobile/com.medytrack.mobile.MainActivity
```

#### Functionality Tests
```bash
# Test 1: Debug Tools Accessibility
# - Navigate to Settings
# - Verify "Debug Tools" option is visible
# - Tap "Debug Tools" to access debug page
# - Expected: Debug page loads with teal header and test options

# Test 2: Error Tracking Verification
# - In Debug Tools, tap "Test Crash"
# - Expected: App shows snackbar "Test crash triggered! Check Supabase for error report."
# - Verify error appears in Supabase app_errors table with Android platform info

# Test 3: Async Error Testing
# - In Debug Tools, tap "Test Async Error"
# - Expected: App shows snackbar "Async error triggered! Check logs and Supabase."
# - Verify async error captured with source: "async_zone"

# Test 4: System Information Display
# - Check System Information section in Debug Tools
# - Expected: Shows Android device details, manufacturer, model, OS version

# Test 5: Error Log Viewer
# - Generate some errors using test buttons
# - Tap "Refresh Logs" in Error Logs section
# - Expected: Error logs display in terminal-style viewer
# - Tap "Clear Logs" to verify clearing functionality
```

#### Android-Specific Error Scenarios
```bash
# Test 6: Memory Pressure Simulation
# - Use Android Studio Memory Profiler or adb commands
# - Simulate low memory conditions
# - Verify app handles memory errors gracefully

# Test 7: Network State Changes
# - Toggle airplane mode on/off during app usage
# - Verify error reporting handles network failures gracefully
# - Check offline error queuing (if implemented)

# Test 8: App Lifecycle Transitions
# - Background/foreground app multiple times
# - Rotate device orientation
# - Verify error tracking continues working after state changes
```

### Release Build Testing

#### Build and Install
```bash
# 1. Build release APK
flutter build apk --release

# 2. Install release APK
adb install build/app/outputs/flutter-apk/app-release.apk
```

#### Security Verification
```bash
# Test 9: Debug Tools Isolation
# - Navigate to Settings
# - Expected: "Debug Tools" option is completely absent
# - No debug functionality should be accessible

# Test 10: Production Error Reporting
# - Trigger app errors through normal usage (invalid inputs, network issues)
# - Expected: Errors still reported to Supabase without debug dependencies
# - No debug logs should appear in system logs

# Test 11: Performance Verification
# - Monitor app performance with release build
# - Expected: No performance impact from error tracking system
# - Logging should be completely silent
```

## 🍎 iOS Testing Protocol

### Debug Build Testing

#### Build and Install
```bash
# 1. Clean and build debug iOS app
flutter clean
flutter pub get
flutter build ios --debug

# 2. Install on iOS device/simulator using Xcode
# - Open ios/Runner.xcworkspace in Xcode
# - Select target device/simulator
# - Build and run (Cmd+R)
```

#### Functionality Tests
```bash
# Test 12: iOS Debug Tools Access
# - Navigate to Settings
# - Verify "Debug Tools" option visible
# - Access debug page and verify iOS-specific system info

# Test 13: iOS Error Tracking
# - Test crash functionality in Debug Tools
# - Verify Supabase receives error with platform: "iOS"
# - Check device info includes iOS version, device model

# Test 14: iOS-Specific Error Scenarios
# - Test app backgrounding/foregrounding
# - Simulate memory warnings
# - Test device rotation and multitasking
# - Verify all errors captured with proper iOS context
```

### Release Build Testing

#### Build and Install
```bash
# 1. Build release iOS app
flutter build ios --release

# 2. Create archive in Xcode for distribution testing
# - Product → Archive
# - Distribute for testing or App Store
```

#### iOS Security Verification
```bash
# Test 15: iOS Production Security
# - Verify debug tools completely hidden in release build
# - Test error reporting still functional
# - Confirm no debug symbols in release build
# - Verify App Store compliance (no debug dependencies)
```

## 🌐 Cross-Platform Verification

### Supabase Integration Testing

#### Database Verification
```sql
-- Test 16: Platform-Specific Error Records
-- Query app_errors table to verify platform detection
SELECT platform, device, COUNT(*) as error_count 
FROM app_errors 
WHERE created_at > NOW() - INTERVAL '1 hour'
GROUP BY platform, device;

-- Expected results should show:
-- platform: "Android" with Android device info
-- platform: "iOS" with iOS device info
-- platform: "Web" with browser info (from previous testing)
```

#### Error Context Validation
```sql
-- Test 17: Error Context Completeness
SELECT 
  id,
  user_id,
  platform,
  device,
  error_message,
  context,
  created_at
FROM app_errors 
ORDER BY created_at DESC 
LIMIT 10;

-- Verify each record contains:
-- - Valid user_id (UUID format)
-- - Accurate platform detection
-- - Detailed device information
-- - Complete error message
-- - Appropriate context data
-- - Proper timestamp
```

### Edge Case Testing

#### Network Resilience
```bash
# Test 18: Offline Error Handling
# - Disconnect device from internet
# - Trigger errors in app
# - Reconnect to internet
# - Verify errors are reported when connection restored (if queuing implemented)
```

#### Performance Impact
```bash
# Test 19: Performance Monitoring
# - Use platform-specific profiling tools
# - Monitor CPU, memory, and battery usage
# - Compare debug vs release build performance
# - Verify error tracking has minimal impact
```

## 📊 Expected Test Results

### Successful Test Criteria

#### Android Results
- [ ] Debug tools accessible in debug build, hidden in release
- [ ] Error tracking functional on both debug and release builds
- [ ] Android-specific device info accurately captured
- [ ] Platform detected as "Android" in Supabase
- [ ] App performance unaffected by error tracking

#### iOS Results
- [ ] Debug tools properly isolated in release builds
- [ ] iOS-specific error scenarios handled correctly
- [ ] Device information includes iOS version and model
- [ ] Platform detected as "iOS" in Supabase
- [ ] App Store compliance maintained

#### Cross-Platform Results
- [ ] Consistent error reporting across all platforms
- [ ] Supabase integration working on all target platforms
- [ ] Error context includes appropriate platform-specific details
- [ ] Network resilience verified on mobile networks
- [ ] Performance impact minimal across all platforms

## 🚨 Failure Criteria

**Immediately halt release if:**
- Debug tools visible in any release build
- Error tracking fails on any target platform
- Sensitive data found in error reports
- App crashes due to error reporting
- Performance significantly degraded
- Supabase integration fails on mobile platforms

## 📝 Test Report Template

```markdown
# MedyTrack Mobile v0.5.0 - Platform Testing Report

## Test Environment
- **Android Version**: ___________
- **iOS Version**: ___________
- **Test Date**: ___________
- **Tester**: ___________

## Test Results Summary
- **Android Debug Build**: [ ] PASS [ ] FAIL
- **Android Release Build**: [ ] PASS [ ] FAIL  
- **iOS Debug Build**: [ ] PASS [ ] FAIL
- **iOS Release Build**: [ ] PASS [ ] FAIL
- **Cross-Platform Integration**: [ ] PASS [ ] FAIL

## Issues Found
1. ___________
2. ___________
3. ___________

## Supabase Verification
- **Total Errors Captured**: ___________
- **Platform Detection Accuracy**: [ ] PASS [ ] FAIL
- **Device Info Completeness**: [ ] PASS [ ] FAIL

## Recommendation
[ ] APPROVE FOR RELEASE
[ ] REQUIRES FIXES BEFORE RELEASE
[ ] MAJOR ISSUES - DO NOT RELEASE

**Notes**: ___________
```

---

**Complete this testing protocol before approving v0.5.0 for production release.**

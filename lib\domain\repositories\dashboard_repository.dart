import 'package:dartz/dartz.dart';
import '../entities/dashboard_stats.dart';
import '../../core/error/failures.dart';

/// Parameters for filtering dashboard statistics
class DashboardStatsParams {
  final String householdId;
  final String? locationId;
  final String? familyMemberId;
  final String? filter; // URL filter parameter (expired, low_stock, etc.)

  const DashboardStatsParams({
    required this.householdId,
    this.locationId,
    this.familyMemberId,
    this.filter,
  });

  /// Check if any filtering is applied
  bool get hasFiltering =>
      locationId != null || familyMemberId != null || filter != null;

  /// Get filter description for debugging
  String get filterDescription {
    final filters = <String>[];
    if (locationId != null) filters.add('location:$locationId');
    if (familyMemberId != null) filters.add('family:$familyMemberId');
    if (filter != null) filters.add('filter:$filter');
    return filters.isEmpty ? 'no filters' : filters.join(', ');
  }
}

abstract class DashboardRepository {
  Future<Either<Failure, DashboardStats>> getDashboardStats(String householdId);

  /// Get filtered dashboard statistics based on context
  Future<Either<Failure, DashboardStats>> getFilteredDashboardStats(
      DashboardStatsParams params);
}

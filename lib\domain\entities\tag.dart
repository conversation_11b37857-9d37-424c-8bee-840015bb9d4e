import 'package:equatable/equatable.dart';

/// Tag entity for medicine categorization
/// Mirrors the tags table from the web app
class Tag extends Equatable {
  final String id;
  final String householdId;
  final String name;
  final String color;
  final String category; // 'therapeutic' or 'usage'
  final DateTime createdAt;
  final DateTime? updatedAt;

  const Tag({
    required this.id,
    required this.householdId,
    required this.name,
    required this.color,
    required this.category,
    required this.createdAt,
    this.updatedAt,
  });

  /// Check if this is a therapeutic tag
  bool get isTherapeutic => category == 'therapeutic';

  /// Check if this is a usage tag
  bool get isUsage => category == 'usage';

  /// Get display name with proper capitalization
  String get displayName {
    return name.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  /// Get color as hex string
  String get hexColor {
    if (color.startsWith('#')) {
      return color;
    }
    return '#$color';
  }

  /// Get category display name
  String get categoryDisplayName {
    switch (category) {
      case 'therapeutic':
        return 'Thérapeutique';
      case 'usage':
        return 'Usage';
      default:
        return 'Autre';
    }
  }

  /// Copy with method for immutable updates
  Tag copyWith({
    String? id,
    String? householdId,
    String? name,
    String? color,
    String? category,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Tag(
      id: id ?? this.id,
      householdId: householdId ?? this.householdId,
      name: name ?? this.name,
      color: color ?? this.color,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        householdId,
        name,
        color,
        category,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'Tag(id: $id, name: $name, category: $category, color: $color)';
  }
}

/// Predefined therapeutic tags
class TherapeuticTags {
  static const List<Map<String, String>> defaultTags = [
    {'name': 'Antibiotique', 'color': '#EF4444'},
    {'name': 'Antalgique', 'color': '#F97316'},
    {'name': 'Anti-inflammatoire', 'color': '#EAB308'},
    {'name': 'Cardiovasculaire', 'color': '#22C55E'},
    {'name': 'Digestif', 'color': '#06B6D4'},
    {'name': 'Respiratoire', 'color': '#3B82F6'},
    {'name': 'Neurologique', 'color': '#8B5CF6'},
    {'name': 'Endocrinien', 'color': '#EC4899'},
    {'name': 'Dermatologique', 'color': '#F59E0B'},
    {'name': 'Ophtalmologique', 'color': '#10B981'},
    {'name': 'Gynécologie', 'color': '#F472B6'},
  ];
}

/// Predefined usage tags
class UsageTags {
  static const List<Map<String, String>> defaultTags = [
    {'name': 'Quotidien', 'color': '#6B7280'},
    {'name': 'Au besoin', 'color': '#9CA3AF'},
    {'name': 'Urgence', 'color': '#DC2626'},
    {'name': 'Enfant', 'color': '#FBBF24'},
    {'name': 'Adulte', 'color': '#059669'},
    {'name': 'Personne âgée', 'color': '#7C3AED'},
  ];
}

import 'package:equatable/equatable.dart';

/// Medicine status enum
enum MedicineStatus {
  normal,
  expired,
  expiringSoon,
  lowStock,
  outOfStock,
}

/// Medicine entity that mirrors the web app's medicine model
class Medicine extends Equatable {
  final String id;
  final String householdId;
  final String? userId; // User who owns this medicine
  final String? medicineId; // Reference to tunisia_medicines table
  final String? customName;
  final bool isCustom;
  final String? dosage;
  final String? form;
  final DateTime? expiration;
  final int quantity;
  final String? category; // Kept for backward compatibility
  final List<String> tags; // New tag system
  final String? location;
  final String? locationName;
  final String? familyMemberId;
  final String? familyMemberName;
  final String? notes;
  final int lowStockThreshold;
  final String? barcode;
  final DateTime createdAt;
  final DateTime? updatedAt;

  // Medicine information from tunisia_medicines (when not custom)
  final String? medicineName;
  final String? laboratoire;
  final String? dci;
  final String? classe;
  final String? sousClasse;
  final String? amm;

  const Medicine({
    required this.id,
    required this.householdId,
    this.userId,
    this.medicineId,
    this.customName,
    this.isCustom = false,
    this.dosage,
    this.form,
    this.expiration,
    this.quantity = 1,
    this.category,
    this.tags = const [],
    this.location,
    this.locationName,
    this.familyMemberId,
    this.familyMemberName,
    this.notes,
    this.lowStockThreshold = 0,
    this.barcode,
    required this.createdAt,
    this.updatedAt,
    this.medicineName,
    this.laboratoire,
    this.dci,
    this.classe,
    this.sousClasse,
    this.amm,
  });

  factory Medicine.unknown() {
    return Medicine(
      id: '',
      householdId: '',
      customName: 'Médicament inconnu',
      isCustom: true,
      quantity: 0,
      lowStockThreshold: 0,
      tags: const [],
      createdAt: DateTime.now(),
    );
  }

  /// Get the simple name for the medicine (without dosage/form)
  String get name {
    if (isCustom) {
      return customName ?? 'Médicament sans nom';
    }
    return medicineName ?? 'Médicament inconnu';
  }

  /// Get the display name for the medicine (matching web app logic)
  String get displayName {
    if (isCustom) {
      return customName ?? 'Médicament sans nom';
    }

    // Create official label combining name, dosage, and form
    final parts = <String>[];

    if (medicineName != null && medicineName!.isNotEmpty) {
      parts.add(medicineName!);
    } else {
      parts.add('Médicament inconnu');
    }

    if (dosage != null && dosage!.isNotEmpty) {
      parts.add(dosage!);
    }

    if (form != null && form!.isNotEmpty) {
      parts.add(form!);
    }

    return parts.join(' - ');
  }

  /// Get medicine status based on expiration and stock
  MedicineStatus get status {
    final now = DateTime.now();

    // Check if expired
    if (expiration != null && expiration!.isBefore(now)) {
      return MedicineStatus.expired;
    }

    // Check if out of stock
    if (quantity <= 0) {
      return MedicineStatus.outOfStock;
    }

    // Check if low stock (only if threshold > 0, meaning tracking is enabled)
    if (lowStockThreshold > 0 && quantity <= lowStockThreshold) {
      return MedicineStatus.lowStock;
    }

    // Check if expiring soon (using 1 month default)
    if (expiration != null) {
      final oneMonthFromNow = now.add(const Duration(days: 30));
      if (expiration!.isBefore(oneMonthFromNow)) {
        return MedicineStatus.expiringSoon;
      }
    }

    return MedicineStatus.normal;
  }

  /// Get expiration date formatted as MM/YY (matching web app)
  String get formattedExpiration {
    if (expiration == null) return 'Non définie';

    final month = expiration!.month.toString().padLeft(2, '0');
    final year = expiration!.year.toString().substring(2);
    return '$month/$year';
  }

  /// Get days until expiration
  int? get daysUntilExpiration {
    if (expiration == null) return null;

    final now = DateTime.now();
    final difference = expiration!.difference(now);
    return difference.inDays;
  }

  /// Check if medicine is expired
  bool get isExpired {
    if (expiration == null) return false;
    return expiration!.isBefore(DateTime.now());
  }

  /// Check if medicine is expiring soon
  bool get isExpiringSoon {
    if (expiration == null) return false;

    final now = DateTime.now();
    final oneMonthFromNow = now.add(const Duration(days: 30));
    return expiration!.isBefore(oneMonthFromNow) && !isExpired;
  }

  /// Check if medicine is low stock
  bool get isLowStock {
    return lowStockThreshold > 0 &&
        quantity <= lowStockThreshold &&
        quantity > 0;
  }

  /// Check if medicine is out of stock
  bool get isOutOfStock {
    return quantity <= 0;
  }

  /// Get the primary therapeutic class color
  String? get primaryTherapeuticClass {
    if (tags.isNotEmpty) {
      // Return first therapeutic class tag
      final therapeuticTags = [
        'antibiotique',
        'antalgique',
        'anti-inflammatoire',
        'cardiovasculaire',
        'digestif',
        'respiratoire',
        'neurologique',
        'endocrinien',
        'dermatologique',
        'ophtalmologique',
        'gynécologie'
      ];

      for (final tag in tags) {
        if (therapeuticTags.contains(tag.toLowerCase())) {
          return tag;
        }
      }
    }

    // Fallback to legacy category
    return category;
  }

  /// Copy with method for immutable updates
  Medicine copyWith({
    String? id,
    String? householdId,
    String? userId,
    String? medicineId,
    String? customName,
    bool? isCustom,
    String? dosage,
    String? form,
    DateTime? expiration,
    int? quantity,
    String? category,
    List<String>? tags,
    String? location,
    String? locationName,
    String? familyMemberId,
    String? familyMemberName,
    String? notes,
    int? lowStockThreshold,
    String? barcode,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? medicineName,
    String? laboratoire,
    String? dci,
    String? classe,
    String? sousClasse,
    String? amm,
  }) {
    return Medicine(
      id: id ?? this.id,
      householdId: householdId ?? this.householdId,
      userId: userId ?? this.userId,
      medicineId: medicineId ?? this.medicineId,
      customName: customName ?? this.customName,
      isCustom: isCustom ?? this.isCustom,
      dosage: dosage ?? this.dosage,
      form: form ?? this.form,
      expiration: expiration ?? this.expiration,
      quantity: quantity ?? this.quantity,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      location: location ?? this.location,
      locationName: locationName ?? this.locationName,
      familyMemberId: familyMemberId ?? this.familyMemberId,
      familyMemberName: familyMemberName ?? this.familyMemberName,
      notes: notes ?? this.notes,
      lowStockThreshold: lowStockThreshold ?? this.lowStockThreshold,
      barcode: barcode ?? this.barcode,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      medicineName: medicineName ?? this.medicineName,
      laboratoire: laboratoire ?? this.laboratoire,
      dci: dci ?? this.dci,
      classe: classe ?? this.classe,
      sousClasse: sousClasse ?? this.sousClasse,
      amm: amm ?? this.amm,
    );
  }

  @override
  List<Object?> get props => [
        id,
        householdId,
        userId,
        medicineId,
        customName,
        isCustom,
        dosage,
        form,
        expiration,
        quantity,
        category,
        tags,
        location,
        locationName,
        familyMemberId,
        familyMemberName,
        notes,
        lowStockThreshold,
        barcode,
        createdAt,
        updatedAt,
        medicineName,
        laboratoire,
        dci,
        classe,
        sousClasse,
        amm,
      ];

  @override
  String toString() {
    return 'Medicine(id: $id, displayName: $displayName, status: $status, '
        'quantity: $quantity, expiration: $expiration)';
  }
}

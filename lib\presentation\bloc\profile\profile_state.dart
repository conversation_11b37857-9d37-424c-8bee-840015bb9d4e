import 'package:equatable/equatable.dart';
import '../../../domain/entities/user.dart';

/// Profile states for managing user profile operations
abstract class ProfileState extends Equatable {
  const ProfileState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class ProfileInitial extends ProfileState {}

/// Loading state
class ProfileLoading extends ProfileState {}

/// Profile loaded successfully
class ProfileLoaded extends ProfileState {
  final User user;
  final String? avatarUrl;
  final String language;
  final bool isDarkMode;

  const ProfileLoaded({
    required this.user,
    this.avatarUrl,
    this.language = 'fr',
    this.isDarkMode = false,
  });

  @override
  List<Object?> get props => [user, avatarUrl, language, isDarkMode];

  ProfileLoaded copyWith({
    User? user,
    String? avatarUrl,
    String? language,
    bool? isDarkMode,
  }) {
    return ProfileLoaded(
      user: user ?? this.user,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      language: language ?? this.language,
      isDarkMode: isDarkMode ?? this.isDarkMode,
    );
  }
}

/// Profile update in progress
class ProfileUpdating extends ProfileState {
  final User user;
  final String? avatarUrl;
  final String language;
  final bool isDarkMode;
  final String updateType;

  const ProfileUpdating({
    required this.user,
    this.avatarUrl,
    this.language = 'fr',
    this.isDarkMode = false,
    required this.updateType,
  });

  @override
  List<Object?> get props => [user, avatarUrl, language, isDarkMode, updateType];
}

/// Profile update successful
class ProfileUpdateSuccess extends ProfileState {
  final User user;
  final String? avatarUrl;
  final String language;
  final bool isDarkMode;
  final String message;

  const ProfileUpdateSuccess({
    required this.user,
    this.avatarUrl,
    this.language = 'fr',
    this.isDarkMode = false,
    required this.message,
  });

  @override
  List<Object?> get props => [user, avatarUrl, language, isDarkMode, message];
}

/// Profile operation error
class ProfileError extends ProfileState {
  final String message;
  final User? user;
  final String? avatarUrl;
  final String language;
  final bool isDarkMode;

  const ProfileError({
    required this.message,
    this.user,
    this.avatarUrl,
    this.language = 'fr',
    this.isDarkMode = false,
  });

  @override
  List<Object?> get props => [message, user, avatarUrl, language, isDarkMode];
}

/// Account deletion in progress
class ProfileAccountDeleting extends ProfileState {}

/// Account deleted successfully
class ProfileAccountDeleted extends ProfileState {
  final String message;

  const ProfileAccountDeleted({required this.message});

  @override
  List<Object?> get props => [message];
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_state.dart' as auth_state;
import '../../bloc/medicine/medicine_bloc.dart';
import '../../widgets/common/category_card.dart';
import '../../widgets/common/form_components.dart';

/// Redesigned medicine list page matching the mockup
class MedicineListPageRedesign extends StatefulWidget {
  final String? categoryFilter;
  final String? searchQuery;

  const MedicineListPageRedesign({
    super.key,
    this.categoryFilter,
    this.searchQuery,
  });

  @override
  State<MedicineListPageRedesign> createState() =>
      _MedicineListPageRedesignState();
}

class _MedicineListPageRedesignState extends State<MedicineListPageRedesign> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearchActive = false;
  String? _selectedCategoryId;
  String _sortBy = 'name'; // name, date, frequency
  bool _showFilters = false;

  @override
  void initState() {
    super.initState();
    _selectedCategoryId = widget.categoryFilter;
    _searchController.text = widget.searchQuery ?? '';
    _isSearchActive = widget.searchQuery?.isNotEmpty ?? false;
    _loadMedicines();
  }

  void _loadMedicines() {
    final householdId =
        context.read<AuthBloc>().state is auth_state.AuthAuthenticated
            ? (context.read<AuthBloc>().state as auth_state.AuthAuthenticated)
                .householdId
            : null;

    if (householdId != null) {
      context
          .read<MedicineBloc>()
          .add(MedicineLoadRequested(householdId: householdId));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      body: Column(
        children: [
          // Header with teal background
          Container(
            padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => context.go('/dashboard'),
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Mes Médicaments',
                    style: AppTextStyles.headlineMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _isSearchActive = !_isSearchActive;
                    });
                  },
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.search,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _showFilters = !_showFilters;
                    });
                  },
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      _showFilters
                          ? Icons.filter_list
                          : Icons.filter_list_outlined,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content container with white background
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -4),
                  ),
                ],
              ),
              child: RefreshIndicator(
                onRefresh: () async {
                  _loadMedicines();
                },
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (_isSearchActive) _buildSearchBar(),
                      const SizedBox(height: 16),

                      // Page title and filters
                      _buildPageHeader(),

                      const SizedBox(height: 20),

                      // Category filters
                      if (_showFilters) ...[
                        _buildCategoryFilters(),
                        const SizedBox(height: 20),
                      ],

                      // Sort and filter controls
                      _buildControlsRow(),

                      const SizedBox(height: 20),

                      // Medicine list
                      _buildMedicineList(),

                      const SizedBox(height: 100), // Bottom padding for FAB
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go('/medicines/add'),
        backgroundColor: AppColors.teal,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.grey200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Rechercher un médicament...',
          hintStyle: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.grey500,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: AppColors.grey400,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
        style: AppTextStyles.bodyMedium,
        onChanged: _onSearchChanged,
      ),
    );
  }

  Widget _buildPageHeader() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Mes Médicaments',
                style: AppTextStyles.headlineMedium.copyWith(
                  color: AppColors.navy,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              BlocBuilder<MedicineBloc, MedicineState>(
                builder: (context, state) {
                  if (state is MedicineLoaded) {
                    return Text(
                      '${state.medicines.length} médicament${state.medicines.length > 1 ? 's' : ''}',
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: AppColors.grey600,
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ],
          ),
        ),

        // Filter toggle button
        IconButton(
          onPressed: () {
            setState(() {
              _showFilters = !_showFilters;
            });
          },
          icon: Icon(
            _showFilters ? Icons.filter_list_off : Icons.filter_list,
            color: AppColors.teal,
          ),
          style: IconButton.styleFrom(
            backgroundColor: AppColors.tealLight.withValues(alpha: 0.1),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Filtrer par catégorie',
          style: AppTextStyles.titleMedium.copyWith(
            color: AppColors.navy,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              // All categories chip
              _buildCategoryChip(
                id: null,
                title: 'Tous',
                icon: Icons.all_inclusive,
                color: AppColors.grey600,
              ),
              const SizedBox(width: 12),

              // Category chips
              ...MedicineCategories.items.map(
                (category) => Padding(
                  padding: const EdgeInsets.only(right: 12),
                  child: _buildCategoryChip(
                    id: category.id,
                    title: category.title,
                    icon: category.icon,
                    color: category.color,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryChip({
    required String? id,
    required String title,
    required IconData icon,
    required Color color,
  }) {
    final isSelected = _selectedCategoryId == id;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedCategoryId = isSelected ? null : id;
        });
        _filterMedicines();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? color : Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? color : AppColors.grey300,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : color,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: AppTextStyles.labelMedium.copyWith(
                color: isSelected ? Colors.white : AppColors.navy,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlsRow() {
    return Row(
      children: [
        // Sort dropdown
        Expanded(
          child: ModernDropdown<String>(
            hint: 'Trier par',
            value: _sortBy,
            items: const [
              DropdownMenuItem(value: 'name', child: Text('Nom')),
              DropdownMenuItem(value: 'date', child: Text('Date d\'ajout')),
              DropdownMenuItem(value: 'frequency', child: Text('Fréquence')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _sortBy = value;
                });
                _sortMedicines();
              }
            },
          ),
        ),

        const SizedBox(width: 16),

        // View toggle (list/grid)
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.grey300),
          ),
          child: Row(
            children: [
              IconButton(
                onPressed: () {
                  // Switch to list view
                },
                icon: Icon(
                  Icons.list,
                  color: AppColors.teal,
                ),
              ),
              Container(
                width: 1,
                height: 24,
                color: AppColors.grey300,
              ),
              IconButton(
                onPressed: () {
                  // Switch to grid view
                },
                icon: Icon(
                  Icons.grid_view,
                  color: AppColors.grey500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMedicineList() {
    return BlocBuilder<MedicineBloc, MedicineState>(
      builder: (context, state) {
        if (state is MedicineLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (state is MedicineError) {
          return Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                Icon(
                  Icons.error_outline,
                  size: 48,
                  color: AppColors.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Erreur',
                  style: AppTextStyles.titleMedium.copyWith(
                    color: AppColors.error,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  state.message,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.grey600,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadMedicines,
                  child: const Text('Réessayer'),
                ),
              ],
            ),
          );
        }

        if (state is MedicineLoaded) {
          var medicines = state.medicines;

          // Apply filters
          if (_selectedCategoryId != null) {
            medicines = medicines
                .where((m) => m.category == _selectedCategoryId)
                .toList();
          }

          if (_searchController.text.isNotEmpty) {
            final query = _searchController.text.toLowerCase();
            medicines = medicines
                .where((m) =>
                    m.name.toLowerCase().contains(query) ||
                    (m.dci?.toLowerCase().contains(query) == true))
                .toList();
          }

          if (medicines.isEmpty) {
            return Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  Icon(
                    Icons.medication_outlined,
                    size: 48,
                    color: AppColors.grey400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _searchController.text.isNotEmpty
                        ? 'Aucun résultat'
                        : 'Aucun médicament',
                    style: AppTextStyles.titleMedium.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _searchController.text.isNotEmpty
                        ? 'Aucun médicament ne correspond à votre recherche.'
                        : 'Commencez par ajouter vos premiers médicaments.',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.grey500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => context.go('/medicines/add'),
                    child: const Text('Ajouter un médicament'),
                  ),
                ],
              ),
            );
          }

          // Return only the list widget (no Expanded) so it can be placed into a sliver/container
          return ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: medicines.length,
            itemBuilder: (context, index) {
              final medicine = medicines[index];
              return MedicineCard(
                medicineName: medicine.name,
                dosage: medicine.dosage ?? 'Non spécifié',
                frequency: 'Selon prescription', // Default frequency text
                nextDose: _getNextDoseText(medicine),
                statusColor: _getMedicineStatusColor(medicine),
                onTap: () => context.go('/medicines/${medicine.id}'),
                onEditTap: () => context.go('/medicines/${medicine.id}/edit'),
                onDeleteTap: () => _showDeleteConfirmation(medicine),
              );
            },
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  // Helper that builds only the list body, to be embedded in a sliver
  Widget _buildMedicineListListOnly() {
    return BlocBuilder<MedicineBloc, MedicineState>(
      builder: (context, state) {
        if (state is MedicineLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is MedicineError) {
          return Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                Icon(Icons.error_outline, size: 48, color: AppColors.error),
                const SizedBox(height: 16),
                Text('Erreur', style: AppTextStyles.titleMedium.copyWith(color: AppColors.error)),
                const SizedBox(height: 8),
                Text(
                  state.message,
                  style: AppTextStyles.bodyMedium.copyWith(color: AppColors.grey600),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(onPressed: _loadMedicines, child: const Text('Réessayer')),
              ],
            ),
          );
        }

        if (state is MedicineLoaded) {
          var medicines = state.medicines;

          if (_selectedCategoryId != null) {
            medicines = medicines.where((m) => m.category == _selectedCategoryId).toList();
          }

          if (_searchController.text.isNotEmpty) {
            final query = _searchController.text.toLowerCase();
            medicines = medicines
                .where((m) =>
                    m.name.toLowerCase().contains(query) ||
                    (m.dci?.toLowerCase().contains(query) == true))
                .toList();
          }

          if (medicines.isEmpty) {
            return Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  Icon(Icons.medication_outlined, size: 48, color: AppColors.grey400),
                  const SizedBox(height: 16),
                  Text(
                    _searchController.text.isNotEmpty ? 'Aucun résultat' : 'Aucun médicament',
                    style: AppTextStyles.titleMedium.copyWith(color: AppColors.grey600),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _searchController.text.isNotEmpty
                        ? 'Aucun médicament ne correspond à votre recherche.'
                        : 'Commencez par ajouter vos premiers médicaments.',
                    style: AppTextStyles.bodyMedium.copyWith(color: AppColors.grey500),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => context.go('/medicines/add'),
                    child: const Text('Ajouter un médicament'),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: medicines.length,
            itemBuilder: (context, index) {
              final medicine = medicines[index];
              return MedicineCard(
                medicineName: medicine.name,
                dosage: medicine.dosage ?? 'Non spécifié',
                frequency: 'Selon prescription',
                nextDose: _getNextDoseText(medicine),
                statusColor: _getMedicineStatusColor(medicine),
                onTap: () => context.go('/medicines/${medicine.id}'),
                onEditTap: () => context.go('/medicines/${medicine.id}/edit'),
                onDeleteTap: () => _showDeleteConfirmation(medicine),
              );
            },
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  void _onSearchChanged(String query) {
    // The query is already in the controller, just trigger rebuild to filter medicines
    setState(() {});
  }

  void _filterMedicines() {
    // Trigger rebuild to apply category filter
    setState(() {});
  }

  void _sortMedicines() {
    // Trigger rebuild to apply sorting
    setState(() {});
  }

  String? _getNextDoseText(dynamic medicine) {
    // Calculate next dose time
    return '08:00'; // Placeholder
  }

  Color? _getMedicineStatusColor(dynamic medicine) {
    // Determine status color
    return AppColors.teal; // Placeholder
  }

  void _showDeleteConfirmation(dynamic medicine) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'Supprimer le médicament',
          style: AppTextStyles.titleLarge.copyWith(
            color: AppColors.navy,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer ${medicine.name} ? Cette action est irréversible.',
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.grey600,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Annuler',
              style: AppTextStyles.labelLarge.copyWith(
                color: AppColors.grey600,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              final householdId =
                  context.read<AuthBloc>().state is auth_state.AuthAuthenticated
                      ? (context.read<AuthBloc>().state
                              as auth_state.AuthAuthenticated)
                          .householdId
                      : null;
              if (householdId != null) {
                context.read<MedicineBloc>().add(MedicineDeleteRequested(
                    medicineId: medicine.id, householdId: householdId));
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

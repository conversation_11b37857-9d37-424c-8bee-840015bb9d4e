import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../../domain/entities/medicine.dart';
import '../../../domain/repositories/medicine_repository.dart';
import '../../../core/utils/debug_logger.dart';
import 'my_medicines_event.dart';
import 'my_medicines_state.dart';

@injectable
class MyMedicinesBloc extends Bloc<MyMedicinesEvent, MyMedicinesState> {
  final MedicineRepository _medicineRepository;
  Timer? _searchDebounceTimer;

  MyMedicinesBloc(this._medicineRepository)
      : super(const MyMedicinesInitial()) {
    on<MyMedicinesInitialized>(_onMyMedicinesInitialized);
    on<MyMedicinesRefreshed>(_onMyMedicinesRefreshed);
    on<MyMedicinesSearched>(_onMyMedicinesSearched);
    on<MyMedicinesSearchCleared>(_onMyMedicinesSearchCleared);
    on<MyMedicinesFiltered>(_onMyMedicinesFiltered);
    on<MyMedicinesSorted>(_onMyMedicinesSorted);
    on<MedicineQuantityUpdated>(_onMedicineQuantityUpdated);
    on<MedicineDeleted>(_onMedicineDeleted);
    on<MedicineSelected>(_onMedicineSelected);
    on<AllMedicinesSelected>(_onAllMedicinesSelected);
    on<MedicineSelectionCleared>(_onMedicineSelectionCleared);
    on<SelectedMedicinesDeleted>(_onSelectedMedicinesDeleted);
    on<ViewModeToggled>(_onViewModeToggled);
  }

  @override
  Future<void> close() {
    _searchDebounceTimer?.cancel();
    return super.close();
  }

  Future<void> _onMyMedicinesInitialized(
    MyMedicinesInitialized event,
    Emitter<MyMedicinesState> emit,
  ) async {
    emit(const MyMedicinesLoading());

    try {
      DebugLogger.logBloc('MyMedicinesInitialized', data: {
        'householdId': event.householdId,
      });

      final result = await _medicineRepository.getMedicines(event.householdId);

      result.fold(
        (failure) => emit(MyMedicinesError(message: failure.message)),
        (medicines) {
          DebugLogger.logBloc('Medicines loaded in MyMedicinesBloc', data: {
            'totalMedicines': medicines.length,
          });

          final filteredMedicines = _applyFiltersAndSort(
            medicines,
            '',
            MedicineFilter.all,
            MedicineSortOption.nameAsc,
          );

          emit(MyMedicinesLoaded(
            householdId: event.householdId,
            medicines: medicines,
            filteredMedicines: filteredMedicines,
          ));
        },
      );
    } catch (e) {
      emit(MyMedicinesError(
        message: 'Erreur lors du chargement des médicaments: ${e.toString()}',
      ));
    }
  }

  Future<void> _onMyMedicinesRefreshed(
    MyMedicinesRefreshed event,
    Emitter<MyMedicinesState> emit,
  ) async {
    final currentState = state;
    if (currentState is MyMedicinesLoaded) {
      emit(const MyMedicinesLoading());

      try {
        final result =
            await _medicineRepository.getMedicines(currentState.householdId);

        result.fold(
          (failure) => emit(MyMedicinesError(message: failure.message)),
          (medicines) {
            final filteredMedicines = _applyFiltersAndSort(
              medicines,
              currentState.searchQuery,
              currentState.currentFilter,
              currentState.currentSort,
            );

            emit(currentState.copyWith(
              medicines: medicines,
              filteredMedicines: filteredMedicines,
              selectedMedicineIds: {},
              isSelectionMode: false,
            ));
          },
        );
      } catch (e) {
        emit(MyMedicinesError(
          message: 'Erreur lors du rafraîchissement: ${e.toString()}',
        ));
      }
    }
  }

  Future<void> _onMyMedicinesSearched(
    MyMedicinesSearched event,
    Emitter<MyMedicinesState> emit,
  ) async {
    final currentState = state;
    if (currentState is MyMedicinesLoaded) {
      // Cancel previous timer
      _searchDebounceTimer?.cancel();

      // Set searching state immediately
      emit(currentState.copyWith(isSearching: true));

      // Debounce search with 300ms delay using async approach
      _searchDebounceTimer = Timer(const Duration(milliseconds: 300), () async {
        if (!isClosed && !emit.isDone) {
          final filteredMedicines = _applyFiltersAndSort(
            currentState.medicines,
            event.query,
            currentState.currentFilter,
            currentState.currentSort,
          );

          if (!emit.isDone) {
            emit(currentState.copyWith(
              searchQuery: event.query,
              filteredMedicines: filteredMedicines,
              isSearching: false,
            ));
          }
        }
      });
    }
  }

  Future<void> _onMyMedicinesSearchCleared(
    MyMedicinesSearchCleared event,
    Emitter<MyMedicinesState> emit,
  ) async {
    final currentState = state;
    if (currentState is MyMedicinesLoaded) {
      _searchDebounceTimer?.cancel();

      final filteredMedicines = _applyFiltersAndSort(
        currentState.medicines,
        '',
        currentState.currentFilter,
        currentState.currentSort,
      );

      emit(currentState.copyWith(
        searchQuery: '',
        filteredMedicines: filteredMedicines,
        isSearching: false,
      ));
    }
  }

  Future<void> _onMyMedicinesFiltered(
    MyMedicinesFiltered event,
    Emitter<MyMedicinesState> emit,
  ) async {
    final currentState = state;
    if (currentState is MyMedicinesLoaded) {
      DebugLogger.logBloc('MyMedicinesFiltered', data: {
        'filter': event.filter.toString(),
        'totalMedicines': currentState.medicines.length,
        'currentFilter': currentState.currentFilter.toString(),
      });

      final filteredMedicines = _applyFiltersAndSort(
        currentState.medicines,
        currentState.searchQuery,
        event.filter,
        currentState.currentSort,
      );

      DebugLogger.logBloc('Filter applied', data: {
        'filter': event.filter.toString(),
        'filteredCount': filteredMedicines.length,
        'totalCount': currentState.medicines.length,
      });

      emit(currentState.copyWith(
        currentFilter: event.filter,
        filteredMedicines: filteredMedicines,
        selectedMedicineIds: {},
        isSelectionMode: false,
      ));
    } else {
      DebugLogger.logBloc('MyMedicinesFiltered - Invalid State', data: {
        'currentState': currentState.runtimeType.toString(),
        'filter': event.filter.toString(),
      });
    }
  }

  Future<void> _onMyMedicinesSorted(
    MyMedicinesSorted event,
    Emitter<MyMedicinesState> emit,
  ) async {
    final currentState = state;
    if (currentState is MyMedicinesLoaded) {
      final filteredMedicines = _applyFiltersAndSort(
        currentState.medicines,
        currentState.searchQuery,
        currentState.currentFilter,
        event.sortOption,
      );

      emit(currentState.copyWith(
        currentSort: event.sortOption,
        filteredMedicines: filteredMedicines,
      ));
    }
  }

  Future<void> _onMedicineQuantityUpdated(
    MedicineQuantityUpdated event,
    Emitter<MyMedicinesState> emit,
  ) async {
    final currentState = state;
    if (currentState is MyMedicinesLoaded) {
      try {
        // Find the medicine to update
        final medicineIndex = currentState.medicines.indexWhere(
          (medicine) => medicine.id == event.medicineId,
        );

        if (medicineIndex == -1) {
          emit(const MyMedicinesError(message: 'Médicament non trouvé'));
          return;
        }

        final medicine = currentState.medicines[medicineIndex];
        final updatedMedicine = medicine.copyWith(
          quantity: event.newQuantity,
          updatedAt: DateTime.now(),
        );

        // Update in repository
        final result =
            await _medicineRepository.updateMedicine(updatedMedicine);

        result.fold(
          (failure) => emit(MyMedicinesError(message: failure.message)),
          (updated) {
            // Update local state
            final updatedMedicines =
                List<Medicine>.from(currentState.medicines);
            updatedMedicines[medicineIndex] = updated;

            final filteredMedicines = _applyFiltersAndSort(
              updatedMedicines,
              currentState.searchQuery,
              currentState.currentFilter,
              currentState.currentSort,
            );

            emit(currentState.copyWith(
              medicines: updatedMedicines,
              filteredMedicines: filteredMedicines,
            ));

            emit(const MyMedicinesOperationSuccess(
              message: 'Quantité mise à jour avec succès',
              operationType: MyMedicinesOperationType.quantityUpdated,
            ));
          },
        );
      } catch (e) {
        emit(MyMedicinesError(
          message: 'Erreur lors de la mise à jour: ${e.toString()}',
        ));
      }
    }
  }

  Future<void> _onMedicineDeleted(
    MedicineDeleted event,
    Emitter<MyMedicinesState> emit,
  ) async {
    final currentState = state;
    if (currentState is MyMedicinesLoaded) {
      // Find the medicine being deleted for better error messages
      final medicineToDelete = currentState.medicines.firstWhere(
          (medicine) => medicine.id == event.medicineId,
          orElse: () => throw Exception('Medicine not found'));

      try {
        DebugLogger.logBloc('MedicineDeleted event received', data: {
          'medicineId': event.medicineId,
          'medicineName': medicineToDelete.name,
        });

        final result =
            await _medicineRepository.deleteMedicine(event.medicineId);

        result.fold(
          (failure) {
            DebugLogger.logError(
                'MEDICINE_DELETION', 'Failed to delete medicine',
                data: {
                  'medicineId': event.medicineId,
                  'medicineName': medicineToDelete.name,
                  'error': failure.message,
                });

            // Return to previous state and show error
            emit(MyMedicinesError(
              message:
                  'Impossible de supprimer "${medicineToDelete.name}": ${failure.message}',
            ));
          },
          (_) {
            DebugLogger.logBloc('Medicine deleted successfully', data: {
              'medicineId': event.medicineId,
              'medicineName': medicineToDelete.name,
            });

            // Remove from local state
            final updatedMedicines = currentState.medicines
                .where((medicine) => medicine.id != event.medicineId)
                .toList();

            final filteredMedicines = _applyFiltersAndSort(
              updatedMedicines,
              currentState.searchQuery,
              currentState.currentFilter,
              currentState.currentSort,
            );

            emit(currentState.copyWith(
              medicines: updatedMedicines,
              filteredMedicines: filteredMedicines,
              selectedMedicineIds: currentState.selectedMedicineIds
                  .where((id) => id != event.medicineId)
                  .toSet(),
            ));

            emit(MyMedicinesOperationSuccess(
              message: '"${medicineToDelete.name}" supprimé avec succès',
              operationType: MyMedicinesOperationType.medicineDeleted,
            ));
          },
        );
      } catch (e) {
        DebugLogger.logError(
            'MEDICINE_DELETION', 'Unexpected error during deletion',
            data: {
              'medicineId': event.medicineId,
              'medicineName': medicineToDelete.name,
              'error': e.toString(),
            });

        // Return to previous state and show error
        emit(MyMedicinesError(
          message:
              'Erreur inattendue lors de la suppression de "${medicineToDelete.name}". Veuillez réessayer.',
        ));
      }
    }
  }

  Future<void> _onMedicineSelected(
    MedicineSelected event,
    Emitter<MyMedicinesState> emit,
  ) async {
    final currentState = state;
    if (currentState is MyMedicinesLoaded) {
      final selectedIds = Set<String>.from(currentState.selectedMedicineIds);

      if (event.isSelected) {
        selectedIds.add(event.medicineId);
      } else {
        selectedIds.remove(event.medicineId);
      }

      emit(currentState.copyWith(
        selectedMedicineIds: selectedIds,
        isSelectionMode: selectedIds.isNotEmpty,
      ));
    }
  }

  Future<void> _onAllMedicinesSelected(
    AllMedicinesSelected event,
    Emitter<MyMedicinesState> emit,
  ) async {
    final currentState = state;
    if (currentState is MyMedicinesLoaded) {
      final selectedIds = event.selectAll
          ? currentState.filteredMedicines
              .map((medicine) => medicine.id)
              .toSet()
          : <String>{};

      emit(currentState.copyWith(
        selectedMedicineIds: selectedIds,
        isSelectionMode: selectedIds.isNotEmpty,
      ));
    }
  }

  Future<void> _onMedicineSelectionCleared(
    MedicineSelectionCleared event,
    Emitter<MyMedicinesState> emit,
  ) async {
    final currentState = state;
    if (currentState is MyMedicinesLoaded) {
      emit(currentState.copyWith(
        selectedMedicineIds: {},
        isSelectionMode: false,
      ));
    }
  }

  Future<void> _onSelectedMedicinesDeleted(
    SelectedMedicinesDeleted event,
    Emitter<MyMedicinesState> emit,
  ) async {
    final currentState = state;
    if (currentState is MyMedicinesLoaded &&
        currentState.selectedMedicineIds.isNotEmpty) {
      final selectedMedicines = currentState.medicines
          .where((medicine) =>
              currentState.selectedMedicineIds.contains(medicine.id))
          .toList();

      DebugLogger.logBloc('SelectedMedicinesDeleted event received', data: {
        'selectedCount': currentState.selectedMedicineIds.length,
        'selectedMedicines':
            selectedMedicines.map((m) => m.name).toList(),
      });

      final List<String> successfullyDeleted = [];
      final List<String> failedDeletions = [];
      final List<String> failureMessages = [];

      // Process each deletion individually to handle partial failures
      for (int i = 0; i < selectedMedicines.length; i++) {
        final medicine = selectedMedicines[i];

        // Emit progress update
        emit(MyMedicinesDeletionLoading(
          message: 'Suppression de "${medicine.name}"...',
          totalCount: selectedMedicines.length,
          currentIndex: i + 1,
        ));
        try {
          final result = await _medicineRepository.deleteMedicine(medicine.id);

          result.fold(
            (failure) {
              failedDeletions.add(medicine.displayName);
              failureMessages
                  .add('${medicine.displayName}: ${failure.message}');
              DebugLogger.logError(
                  'BULK_MEDICINE_DELETION', 'Failed to delete medicine',
                  data: {
                    'medicineId': medicine.id,
                    'medicineName': medicine.displayName,
                    'error': failure.message,
                  });
            },
            (_) {
              successfullyDeleted.add(medicine.displayName);
              DebugLogger.logBloc(
                  'Medicine deleted successfully in bulk operation',
                  data: {
                    'medicineId': medicine.id,
                    'medicineName': medicine.displayName,
                  });
            },
          );
        } catch (e) {
          failedDeletions.add(medicine.displayName);
          failureMessages.add('${medicine.displayName}: Erreur inattendue');
          DebugLogger.logError(
              'BULK_MEDICINE_DELETION', 'Unexpected error during bulk deletion',
              data: {
                'medicineId': medicine.id,
                'medicineName': medicine.displayName,
                'error': e.toString(),
              });
        }
      }

      // Update local state to remove successfully deleted medicines
      final successfullyDeletedIds = selectedMedicines
          .where(
              (medicine) => successfullyDeleted.contains(medicine.displayName))
          .map((medicine) => medicine.id)
          .toSet();

      final updatedMedicines = currentState.medicines
          .where((medicine) => !successfullyDeletedIds.contains(medicine.id))
          .toList();

      final filteredMedicines = _applyFiltersAndSort(
        updatedMedicines,
        currentState.searchQuery,
        currentState.currentFilter,
        currentState.currentSort,
      );

      // Clear selection and exit selection mode
      emit(currentState.copyWith(
        medicines: updatedMedicines,
        filteredMedicines: filteredMedicines,
        selectedMedicineIds: {},
        isSelectionMode: false,
      ));

      // Provide appropriate feedback based on results
      if (failedDeletions.isEmpty) {
        // All deletions successful
        emit(MyMedicinesOperationSuccess(
          message:
              '${successfullyDeleted.length} médicament(s) supprimé(s) avec succès',
          operationType: MyMedicinesOperationType.medicinesDeleted,
        ));
      } else if (successfullyDeleted.isEmpty) {
        // All deletions failed
        emit(MyMedicinesError(
          message:
              'Échec de la suppression de tous les médicaments sélectionnés:\n${failureMessages.join('\n')}',
        ));
      } else {
        // Partial success
        emit(MyMedicinesOperationSuccess(
          message: '${successfullyDeleted.length} médicament(s) supprimé(s). '
              '${failedDeletions.length} échec(s): ${failedDeletions.join(', ')}',
          operationType: MyMedicinesOperationType.medicinesDeleted,
        ));
      }
    }
  }

  Future<void> _onViewModeToggled(
    ViewModeToggled event,
    Emitter<MyMedicinesState> emit,
  ) async {
    final currentState = state;
    if (currentState is MyMedicinesLoaded) {
      emit(currentState.copyWith(viewMode: event.viewMode));
    }
  }

  /// Apply filters and sorting to medicines list
  List<Medicine> _applyFiltersAndSort(
    List<Medicine> medicines,
    String searchQuery,
    MedicineFilter filter,
    MedicineSortOption sortOption,
  ) {
    var filtered = List<Medicine>.from(medicines);

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      filtered = filtered.where((medicine) {
        return medicine.displayName.toLowerCase().contains(query) ||
            (medicine.notes?.toLowerCase().contains(query) ?? false) ||
            (medicine.locationName?.toLowerCase().contains(query) ?? false) ||
            (medicine.familyMemberName?.toLowerCase().contains(query) ??
                false) ||
            medicine.tags.any((tag) => tag.toLowerCase().contains(query));
      }).toList();
    }

    // Apply status filter
    filtered = _applyStatusFilter(filtered, filter);

    // Apply sorting
    filtered = _applySorting(filtered, sortOption);

    return filtered;
  }

  /// Apply status filter to medicines
  List<Medicine> _applyStatusFilter(
      List<Medicine> medicines, MedicineFilter filter) {
    final now = DateTime.now();
    final expiringSoonThreshold = now.add(const Duration(days: 30));

    switch (filter) {
      case MedicineFilter.all:
        return medicines;
      case MedicineFilter.expired:
        return medicines.where((medicine) {
          return medicine.expiration != null &&
              medicine.expiration!.isBefore(now);
        }).toList();
      case MedicineFilter.expiringSoon:
        return medicines.where((medicine) {
          return medicine.expiration != null &&
              medicine.expiration!.isAfter(now) &&
              medicine.expiration!.isBefore(expiringSoonThreshold);
        }).toList();
      case MedicineFilter.lowStock:
        return medicines.where((medicine) {
          return medicine.quantity > 0 &&
              medicine.lowStockThreshold > 0 &&
              medicine.quantity <= medicine.lowStockThreshold;
        }).toList();
      case MedicineFilter.outOfStock:
        return medicines.where((medicine) => medicine.quantity == 0).toList();
      case MedicineFilter.custom:
        return medicines.where((medicine) => medicine.isCustom).toList();
      case MedicineFilter.prescription:
        return medicines.where((medicine) => !medicine.isCustom).toList();
      case MedicineFilter.byLocation:
        // Group medicines by location - for now return all, will be handled by UI
        return medicines;
      case MedicineFilter.byFamilyMember:
        // Group medicines by family member - for now return all, will be handled by UI
        return medicines;
    }
  }

  /// Apply sorting to medicines
  List<Medicine> _applySorting(
      List<Medicine> medicines, MedicineSortOption sortOption) {
    final sorted = List<Medicine>.from(medicines);

    switch (sortOption) {
      case MedicineSortOption.nameAsc:
        sorted.sort((a, b) => a.displayName.compareTo(b.displayName));
        break;
      case MedicineSortOption.nameDesc:
        sorted.sort((a, b) => b.displayName.compareTo(a.displayName));
        break;
      case MedicineSortOption.expirationAsc:
        sorted.sort((a, b) {
          if (a.expiration == null && b.expiration == null) return 0;
          if (a.expiration == null) return 1;
          if (b.expiration == null) return -1;
          return a.expiration!.compareTo(b.expiration!);
        });
        break;
      case MedicineSortOption.expirationDesc:
        sorted.sort((a, b) {
          if (a.expiration == null && b.expiration == null) return 0;
          if (a.expiration == null) return 1;
          if (b.expiration == null) return -1;
          return b.expiration!.compareTo(a.expiration!);
        });
        break;
      case MedicineSortOption.quantityAsc:
        sorted.sort((a, b) => a.quantity.compareTo(b.quantity));
        break;
      case MedicineSortOption.quantityDesc:
        sorted.sort((a, b) => b.quantity.compareTo(a.quantity));
        break;
      case MedicineSortOption.createdAtAsc:
        sorted.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case MedicineSortOption.createdAtDesc:
        sorted.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case MedicineSortOption.locationAsc:
        sorted.sort((a, b) {
          final aLocation = a.locationName ?? '';
          final bLocation = b.locationName ?? '';
          return aLocation.compareTo(bLocation);
        });
        break;
      case MedicineSortOption.locationDesc:
        sorted.sort((a, b) {
          final aLocation = a.locationName ?? '';
          final bLocation = b.locationName ?? '';
          return bLocation.compareTo(aLocation);
        });
        break;
    }

    return sorted;
  }
}

import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/medicine.dart';
import '../../repositories/medicine_repository.dart';

@injectable
class SearchMedicinesUseCase implements UseCase<List<Medicine>, SearchMedicinesParams> {
  final MedicineRepository repository;

  SearchMedicinesUseCase(this.repository);

  @override
  Future<Either<Failure, List<Medicine>>> call(SearchMedicinesParams params) async {
    return await repository.searchMedicines(params.householdId, params.query);
  }
}

class SearchMedicinesParams {
  final String householdId;
  final String query;

  SearchMedicinesParams({
    required this.householdId,
    required this.query,
  });
}

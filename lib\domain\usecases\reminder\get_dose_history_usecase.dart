import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/reminder.dart';
import '../../repositories/reminder_repository.dart';

@injectable
class GetDoseHistoryUseCase implements UseCase<List<DoseHistory>, GetDoseHistoryParams> {
  final ReminderRepository repository;

  GetDoseHistoryUseCase(this.repository);

  @override
  Future<Either<Failure, List<DoseHistory>>> call(GetDoseHistoryParams params) async {
    return await repository.getDoseHistoryForUserMedicine(params.userMedicineId);
  }
}

class GetDoseHistoryParams {
  final String userMedicineId;

  GetDoseHistoryParams({required this.userMedicineId});
}

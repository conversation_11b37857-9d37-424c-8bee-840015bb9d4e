import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/supabase_utils.dart';
import '../../../core/utils/debug_logger.dart';
import '../../../domain/entities/medicine.dart';
import '../../bloc/medicine/medicine_bloc.dart';

class MedicineSearch extends StatefulWidget {
  const MedicineSearch({super.key});

  @override
  State<MedicineSearch> createState() => _MedicineSearchState();
}

class _MedicineSearchState extends State<MedicineSearch> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  List<Medicine> _allMedicines = [];
  List<Medicine> _filteredMedicines = [];
  bool _isSearchActive = false;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
    _searchFocusNode.addListener(_onFocusChanged);

    // Debug: Log initialization
    DebugLogger.logUI('MedicineSearch', 'Widget initialized', data: {
      'allMedicinesCount': _allMedicines.length,
      'isSearchActive': _isSearchActive,
    });

    // Load medicines after widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadMedicines();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _loadMedicines() {
    final householdId = SupabaseUtils.getHouseholdId(context);
    if (householdId != null) {
      context
          .read<MedicineBloc>()
          .add(MedicineLoadRequested(householdId: householdId));
    } else {
      // Handle case where household ID is not available
      debugPrint('Cannot load medicines: household ID not available');
    }
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase().trim();

    DebugLogger.logUI('MedicineSearch', 'Search query changed', data: {
      'query': query,
      'allMedicinesCount': _allMedicines.length,
      'isSearchActive': _isSearchActive,
    });

    if (query.isEmpty) {
      setState(() {
        _filteredMedicines = [];
        _isSearchActive = false;
      });
      return;
    }

    setState(() {
      _isSearchActive = true;
      _filteredMedicines = _allMedicines.where((medicine) {
        final name = medicine.displayName.toLowerCase();
        final customName = medicine.customName?.toLowerCase() ?? '';
        final medicineName = medicine.medicineName?.toLowerCase() ?? '';
        final dosage = medicine.dosage?.toLowerCase() ?? '';
        final form = medicine.form?.toLowerCase() ?? '';
        final location = medicine.locationName?.toLowerCase() ?? '';
        final tags = medicine.tags.join(' ').toLowerCase();

        return name.contains(query) ||
            customName.contains(query) ||
            medicineName.contains(query) ||
            dosage.contains(query) ||
            form.contains(query) ||
            location.contains(query) ||
            tags.contains(query);
      }).toList();
    });
  }

  void _onFocusChanged() {
    if (!_searchFocusNode.hasFocus && _searchController.text.isEmpty) {
      setState(() {
        _isSearchActive = false;
        _filteredMedicines = [];
      });
    }
  }

  void _clearSearch() {
    _searchController.clear();
    _searchFocusNode.unfocus();
    setState(() {
      _isSearchActive = false;
      _filteredMedicines = [];
    });
  }

  /// Helper method to compare two medicine lists for equality
  bool _listsAreEqual(List<Medicine> list1, List<Medicine> list2) {
    if (list1.length != list2.length) return false;

    for (int i = 0; i < list1.length; i++) {
      if (list1[i].id != list2[i].id) return false;
    }

    return true;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MedicineBloc, MedicineState>(
      builder: (context, medicineState) {
        // Update medicines when state changes - fix the condition to handle initial load
        if (medicineState is MedicineLoaded) {
          // Check if we need to update (either different length or initial empty state)
          final needsUpdate = _allMedicines.isEmpty ||
              _allMedicines.length != medicineState.medicines.length ||
              !_listsAreEqual(_allMedicines, medicineState.medicines);

          if (needsUpdate) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              setState(() {
                _allMedicines = medicineState.medicines;
                _onSearchChanged(); // Re-filter with new data
              });

              DebugLogger.logUI(
                  'MedicineSearch', 'Medicines updated from BlocBuilder',
                  data: {
                    'totalMedicines': medicineState.medicines.length,
                    'filteredMedicines': _filteredMedicines.length,
                    'currentQuery': _searchController.text,
                    'wasEmpty': _allMedicines.isEmpty,
                    'needsUpdate': needsUpdate,
                  });
            });
          }
        }

        return Column(
          children: [
            // Search Bar - positioned as header-content transition
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: _searchFocusNode.hasFocus
                      ? AppColors.teal
                      : AppColors.grey200.withValues(alpha: 0.3),
                  width: _searchFocusNode.hasFocus ? 2 : 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.black.withValues(alpha: 0.1),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: TextField(
                controller: _searchController,
                focusNode: _searchFocusNode,
                decoration: InputDecoration(
                  hintText: _allMedicines.isEmpty
                      ? 'Chargement des médicaments...'
                      : 'Rechercher parmi ${_allMedicines.length} médicament${_allMedicines.length > 1 ? 's' : ''}...',
                  hintStyle: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.grey500,
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    color: _searchFocusNode.hasFocus
                        ? AppColors.teal
                        : AppColors.grey400,
                  ),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          onPressed: _clearSearch,
                          icon: Icon(
                            Icons.clear,
                            color: AppColors.grey400,
                          ),
                        )
                      : _allMedicines.isNotEmpty
                          ? Padding(
                              padding: const EdgeInsets.all(12),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: AppColors.teal.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  '${_allMedicines.length}',
                                  style: AppTextStyles.labelSmall.copyWith(
                                    color: AppColors.teal,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            )
                          : null,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                ),
                style: AppTextStyles.bodyMedium,
              ),
            ),

            // Search Results
            if (_isSearchActive) ...[
              BlocListener<MedicineBloc, MedicineState>(
                listener: (context, state) {
                  DebugLogger.logUI('MedicineSearch',
                      'MedicineBloc state changed in listener',
                      data: {
                        'stateType': state.runtimeType.toString(),
                        'isLoaded': state is MedicineLoaded,
                        'medicineCount': state is MedicineLoaded
                            ? state.medicines.length
                            : 0,
                        'currentAllMedicinesCount': _allMedicines.length,
                      });

                  if (state is MedicineLoaded) {
                    // Check if we need to update (same logic as BlocBuilder)
                    final needsUpdate = _allMedicines.isEmpty ||
                        _allMedicines.length != state.medicines.length ||
                        !_listsAreEqual(_allMedicines, state.medicines);

                    if (needsUpdate) {
                      setState(() {
                        _allMedicines = state.medicines;
                        _onSearchChanged(); // Re-filter with new data
                      });

                      DebugLogger.logUI('MedicineSearch',
                          'Medicines loaded and search updated in listener',
                          data: {
                            'totalMedicines': state.medicines.length,
                            'filteredMedicines': _filteredMedicines.length,
                            'currentQuery': _searchController.text,
                            'needsUpdate': needsUpdate,
                          });
                    }
                  }
                },
                child: Container(
                  margin: const EdgeInsets.only(top: 8),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                        color: AppColors.grey200.withValues(alpha: 0.3)),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.black.withValues(alpha: 0.1),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  constraints: const BoxConstraints(maxHeight: 300),
                  child: _filteredMedicines.isEmpty
                      ? Padding(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            children: [
                              Icon(
                                Icons.search_off,
                                size: 48,
                                color: AppColors.grey400,
                              ),
                              const SizedBox(height: 12),
                              Text(
                                'Aucun médicament trouvé',
                                style: AppTextStyles.titleMedium.copyWith(
                                  color: AppColors.grey600,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Essayez avec d\'autres mots-clés',
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: AppColors.grey500,
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.separated(
                          shrinkWrap: true,
                          padding: const EdgeInsets.all(8),
                          itemCount: _filteredMedicines.length,
                          separatorBuilder: (context, index) => Divider(
                            height: 1,
                            color: AppColors.grey200,
                          ),
                          itemBuilder: (context, index) {
                            final medicine = _filteredMedicines[index];
                            return _SearchResultItem(
                              medicine: medicine,
                              searchQuery: _searchController.text,
                              onTap: () {
                                _clearSearch();
                                context.push('/medicines/${medicine.id}');
                              },
                            );
                          },
                        ),
                ),
              ),
            ],
          ],
        );
      },
    );
  }
}

class _SearchResultItem extends StatelessWidget {
  final Medicine medicine;
  final String searchQuery;
  final VoidCallback onTap;

  const _SearchResultItem({
    required this.medicine,
    required this.searchQuery,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: onTap,
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: _getStatusColor().withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.medication,
          color: _getStatusColor(),
          size: 20,
        ),
      ),
      title: Text(
        medicine.displayName,
        style: AppTextStyles.titleSmall,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (medicine.dosage != null)
            Text(
              medicine.dosage!,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.grey600,
              ),
            ),
          Row(
            children: [
              Text(
                'Qté: ${medicine.quantity}',
                style: AppTextStyles.labelSmall.copyWith(
                  color: AppColors.grey500,
                ),
              ),
              if (medicine.locationName != null) ...[
                const SizedBox(width: 8),
                Icon(
                  Icons.location_on,
                  size: 12,
                  color: AppColors.grey500,
                ),
                const SizedBox(width: 2),
                Expanded(
                  child: Text(
                    medicine.locationName!,
                    style: AppTextStyles.labelSmall.copyWith(
                      color: AppColors.grey500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: AppColors.grey400,
      ),
    );
  }

  Color _getStatusColor() {
    switch (medicine.status) {
      case MedicineStatus.expired:
        return AppColors.error;
      case MedicineStatus.expiringSoon:
        return AppColors.warning;
      case MedicineStatus.lowStock:
        return AppColors.warning;
      case MedicineStatus.outOfStock:
        return AppColors.error;
      case MedicineStatus.normal:
        return AppColors.success;
    }
  }
}

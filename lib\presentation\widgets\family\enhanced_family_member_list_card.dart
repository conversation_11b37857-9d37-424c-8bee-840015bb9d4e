import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/family_member.dart';
import '../common/base_list_card.dart';
import '../common/status_badge.dart';

/// Enhanced family member list card following the new design specifications
/// Features:
/// - 72dp fixed height
/// - 16dp border radius
/// - Age badges with proper styling
/// - Consistent typography and icon sizing
/// - Material 3 design principles
class EnhancedFamilyMemberListCard extends StatelessWidget {
  final FamilyMember member;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool isSelected;
  final bool isSelectionMode;

  const EnhancedFamilyMemberListCard({
    super.key,
    required this.member,
    this.onTap,
    this.onLongPress,
    this.onEdit,
    this.onDelete,
    this.isSelected = false,
    this.isSelectionMode = false,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedBaseListCard(
      leading: _buildLeading(),
      title: _buildTitle(),
      subtitle: _buildSubtitle(),
      trailing: _buildTrailing(),
      actions: _buildActions(),
      onTap: onTap,
      onLongPress: onLongPress,
      isSelected: isSelected,
      showBorder: isSelectionMode,
      backgroundColor: _getAvatarColor().withValues(alpha: 0.05),
      borderColor: _getAvatarColor(),
    );
  }

  Widget _buildLeading() {
    return ListCardLeading.avatar(
      icon: _getGenderIcon(),
      color: _getAvatarColor(),
      size: 56.0,
      iconSize: 28.0,
    );
  }

  Widget _buildTitle() {
    return Text(
      member.displayName,
      style: AppTextStyles.titleMedium.copyWith(
        fontWeight: FontWeight.w600,
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildSubtitle() {
    final List<String> subtitleParts = [member.relationshipDisplayName];

    // Add gender if available
    if (member.gender != null) {
      subtitleParts.add(_getGenderDisplayName());
    }

    // Add creation date
    subtitleParts.add(_formatDate(member.createdAt));

    return Text(
      subtitleParts.join(' • '),
      style: AppTextStyles.bodySmall.copyWith(
        color: AppColors.grey600,
      ),
      maxLines: 2, // Allow 2 lines for better content display
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildTrailing() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Age badge only - simplified to prevent overflow
        if (member.age != null) GeneralBadge.age(member.age!),
      ],
    );
  }

  List<Widget>? _buildActions() {
    if (onEdit == null && onDelete == null) return null;

    return [
      ListCardTrailing.moreOptions(
        items: [
          if (onEdit != null)
            PopupMenuItem<String>(
              value: 'edit',
              child: Row(
                children: [
                  Icon(
                    Icons.edit_outlined,
                    size: 18,
                    color: AppColors.grey600,
                  ),
                  const SizedBox(width: 8),
                  const Text('Modifier'),
                ],
              ),
            ),
          if (onDelete != null)
            PopupMenuItem<String>(
              value: 'delete',
              child: Row(
                children: [
                  Icon(
                    Icons.delete_outline,
                    size: 18,
                    color: AppColors.error,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Supprimer',
                    style: TextStyle(color: AppColors.error),
                  ),
                ],
              ),
            ),
        ],
        onSelected: (value) {
          switch (value) {
            case 'edit':
              onEdit?.call();
              break;
            case 'delete':
              onDelete?.call();
              break;
          }
        },
      ),
    ];
  }

  /// Get gender-based icon
  IconData _getGenderIcon() {
    switch (member.gender?.toLowerCase()) {
      case 'male':
      case 'homme':
      case 'masculin':
        return Icons.male;
      case 'female':
      case 'femme':
      case 'féminin':
        return Icons.female;
      default:
        return Icons.person;
    }
  }

  /// Get gender display name
  String _getGenderDisplayName() {
    switch (member.gender?.toLowerCase()) {
      case 'male':
      case 'homme':
      case 'masculin':
        return 'Homme';
      case 'female':
      case 'femme':
      case 'féminin':
        return 'Femme';
      default:
        return 'Non spécifié';
    }
  }

  /// Get avatar color based on gender
  Color _getAvatarColor() {
    switch (member.gender?.toLowerCase()) {
      case 'male':
      case 'homme':
      case 'masculin':
        return AppColors.blue;
      case 'female':
      case 'femme':
      case 'féminin':
        return Colors.pink;
      default:
        return AppColors.teal;
    }
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Aujourd\'hui';
    } else if (difference.inDays == 1) {
      return 'Hier';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}j';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '${weeks}sem';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '${months}mois';
    } else {
      final years = (difference.inDays / 365).floor();
      return '${years}an${years > 1 ? 's' : ''}';
    }
  }
}

/// Compact version of the enhanced family member card
class CompactFamilyMemberListCard extends StatelessWidget {
  final FamilyMember member;
  final VoidCallback? onTap;
  final bool isSelected;

  const CompactFamilyMemberListCard({
    super.key,
    required this.member,
    this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return BaseListCard(
      height: 56.0, // Smaller height for compact version
      onTap: onTap,
      isSelected: isSelected,
      backgroundColor: _getAvatarColor().withValues(alpha: 0.05),
      borderColor: _getAvatarColor(),
      child: Row(
        children: [
          // Compact avatar
          ListCardLeading.avatar(
            icon: _getGenderIcon(),
            color: _getAvatarColor(),
            size: 40.0,
            iconSize: 20.0,
          ),

          const SizedBox(width: 12.0),

          // Main content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  member.displayName,
                  style: AppTextStyles.titleSmall.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2.0),
                Text(
                  member.relationshipDisplayName,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.grey600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          // Age badge if available
          if (member.age != null)
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8.0,
                vertical: 4.0,
              ),
              decoration: BoxDecoration(
                color: _getAgeGroupColor().withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Text(
                '${member.age}',
                style: AppTextStyles.labelSmall.copyWith(
                  color: _getAgeGroupColor(),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  IconData _getGenderIcon() {
    switch (member.gender?.toLowerCase()) {
      case 'male':
      case 'homme':
      case 'masculin':
        return Icons.male;
      case 'female':
      case 'femme':
      case 'féminin':
        return Icons.female;
      default:
        return Icons.person;
    }
  }

  Color _getAvatarColor() {
    switch (member.gender?.toLowerCase()) {
      case 'male':
      case 'homme':
      case 'masculin':
        return AppColors.blue;
      case 'female':
      case 'femme':
      case 'féminin':
        return Colors.pink;
      default:
        return AppColors.teal;
    }
  }

  Color _getAgeGroupColor() {
    final ageGroup = member.ageGroup;
    switch (ageGroup) {
      case 'infant':
        return Colors.pink;
      case 'child':
        return Colors.orange;
      case 'adolescent':
        return AppColors.purple;
      case 'adult':
        return AppColors.teal;
      case 'senior':
        return Colors.brown;
      default:
        return AppColors.teal;
    }
  }
}

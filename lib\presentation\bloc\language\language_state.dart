import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// States for language management
abstract class LanguageState extends Equatable {
  const LanguageState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class LanguageInitial extends LanguageState {
  const LanguageInitial();
}

/// Loading state
class LanguageLoading extends LanguageState {
  const LanguageLoading();
}

/// Loaded state with current language
class LanguageLoaded extends LanguageState {
  final String languageCode;
  final Locale locale;

  const LanguageLoaded({
    required this.languageCode,
    required this.locale,
  });

  @override
  List<Object?> get props => [languageCode, locale];
}

/// Error state
class LanguageError extends LanguageState {
  final String message;

  const LanguageError({required this.message});

  @override
  List<Object?> get props => [message];
}

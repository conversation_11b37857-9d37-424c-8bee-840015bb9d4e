import 'package:equatable/equatable.dart';

import '../../../domain/entities/location.dart';

/// Base class for location states
abstract class LocationState extends Equatable {
  const LocationState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class LocationInitial extends LocationState {
  const LocationInitial();
}

/// Loading state
class LocationLoading extends LocationState {
  const LocationLoading();
}

/// Loaded state with locations
class LocationLoaded extends LocationState {
  final String householdId;
  final List<Location> locations;
  final List<Location> filteredLocations;
  final String searchQuery;
  final Location? selectedLocation;
  final bool isSearching;

  const LocationLoaded({
    required this.householdId,
    required this.locations,
    required this.filteredLocations,
    this.searchQuery = '',
    this.selectedLocation,
    this.isSearching = false,
  });

  /// Get locations count
  int get locationsCount => locations.length;

  /// Check if locations are empty
  bool get isEmpty => locations.isEmpty;

  /// Check if search is active
  bool get hasSearchQuery => searchQuery.isNotEmpty;

  /// Get display locations (filtered or all)
  List<Location> get displayLocations => hasSearchQuery ? filteredLocations : locations;

  /// Copy with method for immutable updates
  LocationLoaded copyWith({
    String? householdId,
    List<Location>? locations,
    List<Location>? filteredLocations,
    String? searchQuery,
    Location? selectedLocation,
    bool clearSelectedLocation = false,
    bool? isSearching,
  }) {
    return LocationLoaded(
      householdId: householdId ?? this.householdId,
      locations: locations ?? this.locations,
      filteredLocations: filteredLocations ?? this.filteredLocations,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedLocation: clearSelectedLocation ? null : (selectedLocation ?? this.selectedLocation),
      isSearching: isSearching ?? this.isSearching,
    );
  }

  @override
  List<Object?> get props => [
        householdId,
        locations,
        filteredLocations,
        searchQuery,
        selectedLocation,
        isSearching,
      ];
}

/// Error state
class LocationError extends LocationState {
  final String message;

  const LocationError({required this.message});

  @override
  List<Object?> get props => [message];
}

/// Success state for operations
class LocationOperationSuccess extends LocationState {
  final String message;
  final LocationOperationType operationType;

  const LocationOperationSuccess({
    required this.message,
    required this.operationType,
  });

  @override
  List<Object?> get props => [message, operationType];
}

/// Form validation state
class LocationFormValidation extends LocationState {
  final Map<String, String?> errors;
  final bool isValid;

  const LocationFormValidation({
    required this.errors,
    required this.isValid,
  });

  /// Get error for specific field
  String? getError(String field) => errors[field];

  /// Check if field has error
  bool hasError(String field) => errors[field] != null;

  @override
  List<Object?> get props => [errors, isValid];
}

/// Operation types for success messages
enum LocationOperationType {
  created,
  updated,
  deleted,
  defaultsInitialized,
}

/// Extension for operation type messages
extension LocationOperationTypeExtension on LocationOperationType {
  String get message {
    switch (this) {
      case LocationOperationType.created:
        return 'Emplacement créé avec succès';
      case LocationOperationType.updated:
        return 'Emplacement mis à jour avec succès';
      case LocationOperationType.deleted:
        return 'Emplacement supprimé avec succès';
      case LocationOperationType.defaultsInitialized:
        return 'Emplacements par défaut initialisés';
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/supabase_utils.dart';
import '../../../domain/entities/family_member.dart';
import '../../bloc/family_manager/family_manager_bloc.dart';
import '../../bloc/family_manager/family_manager_event.dart';
import '../../bloc/family_manager/family_manager_state.dart';
import '../../widgets/family/enhanced_family_member_list_card.dart';
import '../../widgets/family/add_family_member_dialog.dart';

class FamilyManagerPage extends StatefulWidget {
  final String householdId;

  const FamilyManagerPage({
    super.key,
    required this.householdId,
  });

  @override
  State<FamilyManagerPage> createState() => _FamilyManagerPageState();
}

class _FamilyManagerPageState extends State<FamilyManagerPage> {
  @override
  void initState() {
    super.initState();
    // Initialize family members with validation
    _initializeFamilyManager();
  }

  void _initializeFamilyManager() {
    if (widget.householdId.isNotEmpty &&
        widget.householdId != 'default-household') {
      context.read<FamilyManagerBloc>().add(
            FamilyManagerInitialized(householdId: widget.householdId),
          );
    } else {
      // Retry after a short delay if household ID is not ready
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          final householdId = SupabaseUtils.getHouseholdId(context);
          if (householdId != null && householdId.isNotEmpty) {
            context.read<FamilyManagerBloc>().add(
                  FamilyManagerInitialized(householdId: householdId),
                );
          }
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      body: BlocListener<FamilyManagerBloc, FamilyManagerState>(
        listener: (context, state) {
          if (state is FamilyManagerOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.success,
                behavior: SnackBarBehavior.floating,
              ),
            );
          } else if (state is FamilyManagerError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        },
        child: Column(
          children: [
            // Header with teal background
            Container(
              padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      if (Navigator.canPop(context)) {
                        context.pop();
                      }
                    },
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.arrow_back,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      'Gestion Familiale',
                      style: AppTextStyles.headlineMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Content container with white background
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, -4),
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.only(top: 20),
                  child: BlocBuilder<FamilyManagerBloc, FamilyManagerState>(
                    builder: (context, state) {
                      if (state is FamilyManagerLoading) {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      }

                      if (state is FamilyManagerError) {
                        return _buildErrorState(context, state.message);
                      }

                      if (state is FamilyManagerLoaded) {
                        if (state.isEmpty) {
                          return _buildEmptyState(context);
                        }

                        return Column(
                          children: [
                            Expanded(
                              child: RefreshIndicator(
                                onRefresh: () async {
                                  context.read<FamilyManagerBloc>().add(
                                        const FamilyMembersRefreshed(),
                                      );
                                },
                                child: ListView.builder(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20),
                                  itemCount: state.members.length,
                                  itemBuilder: (context, index) {
                                    final member = state.members[index];
                                    return EnhancedFamilyMemberListCard(
                                      member: member,
                                      onTap: () =>
                                          _showMemberDetails(context, member),
                                      onEdit: () => _showEditMemberDialog(
                                          context, member),
                                      onDelete: () => _showDeleteConfirmation(
                                          context, member),
                                    );
                                  },
                                ),
                              ),
                            ),
                            // Centered Add Member Button
                            Padding(
                              padding:
                                  const EdgeInsets.only(top: 24, bottom: 20),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  ElevatedButton.icon(
                                    onPressed: () =>
                                        _showAddMemberDialog(context),
                                    icon:
                                        const Icon(Icons.person_add, size: 20),
                                    label: Text(
                                      'Ajouter un membre',
                                      style: AppTextStyles.labelLarge,
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.teal,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 24,
                                        vertical: 12,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      elevation: 2,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        );
                      }

                      return const SizedBox.shrink();
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Erreur de chargement',
            style: AppTextStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.grey600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.read<FamilyManagerBloc>().add(
                    FamilyManagerInitialized(householdId: widget.householdId),
                  );
            },
            child: const Text('Réessayer'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.family_restroom,
            size: 64,
            color: AppColors.grey400,
          ),
          const SizedBox(height: 16),
          Text(
            'Aucun membre',
            style: AppTextStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Commencez par ajouter les membres de votre famille',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.grey600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showAddMemberDialog(context),
            icon: const Icon(Icons.person_add),
            label: const Text('Ajouter un membre'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.teal,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _showAddMemberDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AddFamilyMemberDialog(
        householdId: widget.householdId,
      ),
    );
  }

  void _showEditMemberDialog(BuildContext context, FamilyMember member) {
    showDialog(
      context: context,
      builder: (context) => AddFamilyMemberDialog(
        householdId: widget.householdId,
        member: member,
      ),
    );
  }

  void _showMemberDetails(BuildContext context, FamilyMember member) {
    // TODO: Implement member details page
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Détails de ${member.displayName}'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, FamilyMember member) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer le membre'),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer "${member.displayName}" ?\n\n'
          'Cette action est irréversible et tous les médicaments associés '
          'perdront leur assignation à ce membre.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<FamilyManagerBloc>().add(
                    FamilyMemberDeleted(memberId: member.id),
                  );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }
}

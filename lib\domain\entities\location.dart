import 'package:equatable/equatable.dart';

/// Location entity representing medicine storage locations
class Location extends Equatable {
  final String id;
  final String householdId;
  final String name;
  final String? description;
  final String icon;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const Location({
    required this.id,
    required this.householdId,
    required this.name,
    this.description,
    required this.icon,
    required this.createdAt,
    this.updatedAt,
  });

  /// Get display name with proper capitalization
  String get displayName {
    return name.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  /// Get default color for location icons
  String get hexColor {
    return '#14B8A6'; // Default teal color for all locations
  }

  /// Copy with method for immutable updates
  Location copyWith({
    String? id,
    String? householdId,
    String? name,
    String? description,
    String? icon,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Location(
      id: id ?? this.id,
      householdId: householdId ?? this.householdId,
      name: name ?? this.name,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        householdId,
        name,
        description,
        icon,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'Location(id: $id, name: $name, icon: $icon)';
  }
}

/// Predefined default locations
class DefaultLocations {
  static const List<Map<String, String>> values = [
    {
      'name': 'Armoire à pharmacie',
      'description': 'Armoire principale pour les médicaments',
      'icon': 'medical_services',
      'color': '#14B8A6',
    },
    {
      'name': 'Réfrigérateur',
      'description': 'Médicaments nécessitant une conservation au froid',
      'icon': 'kitchen',
      'color': '#3B82F6',
    },
    {
      'name': 'Chambre',
      'description': 'Médicaments personnels dans la chambre',
      'icon': 'bed',
      'color': '#8B5CF6',
    },
    {
      'name': 'Cuisine',
      'description': 'Médicaments dans la cuisine',
      'icon': 'restaurant',
      'color': '#F59E0B',
    },
    {
      'name': 'Salle de bain',
      'description': 'Médicaments dans la salle de bain',
      'icon': 'bathtub',
      'color': '#06B6D4',
    },
    {
      'name': 'Bureau',
      'description': 'Médicaments dans le bureau',
      'icon': 'work',
      'color': '#6B7280',
    },
    {
      'name': 'Trousse de secours',
      'description': 'Trousse de premiers secours portable',
      'icon': 'local_hospital',
      'color': '#EF4444',
    },
    {
      'name': 'Voiture',
      'description': 'Médicaments dans la voiture',
      'icon': 'directions_car',
      'color': '#84CC16',
    },
  ];
}

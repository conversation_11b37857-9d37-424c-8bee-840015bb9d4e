import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../../domain/entities/location.dart';
import '../../../domain/repositories/location_repository.dart';
import 'location_event.dart';
import 'location_state.dart';

@injectable
class LocationBloc extends Bloc<LocationEvent, LocationState> {
  final LocationRepository _locationRepository;
  StreamSubscription<List<Location>>? _locationsSubscription;

  LocationBloc(this._locationRepository) : super(const LocationInitial()) {
    on<LocationsInitialized>(_onLocationsInitialized);
    on<LocationsRefreshed>(_onLocationsRefreshed);
    on<LocationCreated>(_onLocationCreated);
    on<LocationUpdated>(_onLocationUpdated);
    on<LocationDeleted>(_onLocationDeleted);
    on<LocationsSearched>(_onLocationsSearched);
    on<LocationSearchCleared>(_onLocationSearchCleared);
    on<DefaultLocationsInitialized>(_onDefaultLocationsInitialized);
    on<LocationSelected>(_onLocationSelected);
    on<LocationSelectionCleared>(_onLocationSelectionCleared);
    on<LocationFormValidated>(_onLocationFormValidated);
    on<LocationFormReset>(_onLocationFormReset);
  }

  @override
  Future<void> close() {
    _locationsSubscription?.cancel();
    return super.close();
  }

  Future<void> _onLocationsInitialized(
    LocationsInitialized event,
    Emitter<LocationState> emit,
  ) async {
    emit(const LocationLoading());

    try {
      // Cancel previous subscription
      await _locationsSubscription?.cancel();

      // Use emit.forEach to properly handle stream emissions
      await emit.forEach<List<Location>>(
        _locationRepository.getHouseholdLocations(event.householdId),
        onData: (locations) => LocationLoaded(
          householdId: event.householdId,
          locations: locations,
          filteredLocations: locations,
        ),
        onError: (error, stackTrace) => LocationError(
          message:
              'Erreur lors du chargement des emplacements: ${error.toString()}',
        ),
      );
    } catch (e) {
      if (!emit.isDone) {
        emit(LocationError(
          message: 'Erreur lors de l\'initialisation: ${e.toString()}',
        ));
      }
    }
  }

  Future<void> _onLocationsRefreshed(
    LocationsRefreshed event,
    Emitter<LocationState> emit,
  ) async {
    final currentState = state;
    if (currentState is LocationLoaded) {
      emit(const LocationLoading());
      // The stream subscription will automatically emit new data
    }
  }

  Future<void> _onLocationCreated(
    LocationCreated event,
    Emitter<LocationState> emit,
  ) async {
    try {
      await _locationRepository.createLocation(event.location);
      emit(const LocationOperationSuccess(
        message: 'Emplacement créé avec succès',
        operationType: LocationOperationType.created,
      ));
      // The stream subscription will automatically refresh the list
    } catch (e) {
      emit(LocationError(
        message: 'Erreur lors de la création: ${e.toString()}',
      ));
    }
  }

  Future<void> _onLocationUpdated(
    LocationUpdated event,
    Emitter<LocationState> emit,
  ) async {
    try {
      await _locationRepository.updateLocation(event.location);
      emit(const LocationOperationSuccess(
        message: 'Emplacement mis à jour avec succès',
        operationType: LocationOperationType.updated,
      ));
      // The stream subscription will automatically refresh the list
    } catch (e) {
      emit(LocationError(
        message: 'Erreur lors de la mise à jour: ${e.toString()}',
      ));
    }
  }

  Future<void> _onLocationDeleted(
    LocationDeleted event,
    Emitter<LocationState> emit,
  ) async {
    try {
      await _locationRepository.deleteLocation(event.locationId);
      emit(const LocationOperationSuccess(
        message: 'Emplacement supprimé avec succès',
        operationType: LocationOperationType.deleted,
      ));
    } catch (e) {
      emit(LocationError(
        message: 'Erreur lors de la suppression: ${e.toString()}',
      ));
    }
  }

  Future<void> _onLocationsSearched(
    LocationsSearched event,
    Emitter<LocationState> emit,
  ) async {
    final currentState = state;
    if (currentState is LocationLoaded) {
      final query = event.query.toLowerCase().trim();

      if (query.isEmpty) {
        emit(currentState.copyWith(
          searchQuery: '',
          filteredLocations: currentState.locations,
          isSearching: false,
        ));
        return;
      }

      final filteredLocations = currentState.locations.where((location) {
        return location.name.toLowerCase().contains(query) ||
            (location.description?.toLowerCase().contains(query) ?? false);
      }).toList();

      emit(currentState.copyWith(
        searchQuery: query,
        filteredLocations: filteredLocations,
        isSearching: true,
      ));
    }
  }

  Future<void> _onLocationSearchCleared(
    LocationSearchCleared event,
    Emitter<LocationState> emit,
  ) async {
    final currentState = state;
    if (currentState is LocationLoaded) {
      emit(currentState.copyWith(
        searchQuery: '',
        filteredLocations: currentState.locations,
        isSearching: false,
      ));
    }
  }

  Future<void> _onDefaultLocationsInitialized(
    DefaultLocationsInitialized event,
    Emitter<LocationState> emit,
  ) async {
    try {
      await _locationRepository.initializeDefaultLocations(event.householdId);
      emit(const LocationOperationSuccess(
        message: 'Emplacements par défaut initialisés',
        operationType: LocationOperationType.defaultsInitialized,
      ));
    } catch (e) {
      emit(LocationError(
        message: 'Erreur lors de l\'initialisation: ${e.toString()}',
      ));
    }
  }

  Future<void> _onLocationSelected(
    LocationSelected event,
    Emitter<LocationState> emit,
  ) async {
    final currentState = state;
    if (currentState is LocationLoaded) {
      emit(currentState.copyWith(selectedLocation: event.location));
    }
  }

  Future<void> _onLocationSelectionCleared(
    LocationSelectionCleared event,
    Emitter<LocationState> emit,
  ) async {
    final currentState = state;
    if (currentState is LocationLoaded) {
      emit(currentState.copyWith(clearSelectedLocation: true));
    }
  }

  Future<void> _onLocationFormValidated(
    LocationFormValidated event,
    Emitter<LocationState> emit,
  ) async {
    final errors = <String, String?>{};

    // Validate name
    if (event.name.trim().isEmpty) {
      errors['name'] = 'Le nom est requis';
    } else if (event.name.trim().length < 2) {
      errors['name'] = 'Le nom doit contenir au moins 2 caractères';
    } else if (event.name.trim().length > 50) {
      errors['name'] = 'Le nom ne peut pas dépasser 50 caractères';
    }

    // Validate description (optional)
    if (event.description != null && event.description!.length > 200) {
      errors['description'] =
          'La description ne peut pas dépasser 200 caractères';
    }

    // Validate icon
    if (event.icon.trim().isEmpty) {
      errors['icon'] = 'L\'icône est requise';
    }

    final isValid = errors.values.every((error) => error == null);

    emit(LocationFormValidation(
      errors: errors,
      isValid: isValid,
    ));
  }

  Future<void> _onLocationFormReset(
    LocationFormReset event,
    Emitter<LocationState> emit,
  ) async {
    emit(const LocationFormValidation(
      errors: {},
      isValid: false,
    ));
  }
}

---
name: Bug report
about: Create a report to help us improve MedyTrack Mobile
title: '[BUG] '
labels: 'bug'
assignees: ''
---

## 🐛 Bug Description

A clear and concise description of what the bug is.

## 🔄 Steps to Reproduce

Steps to reproduce the behavior:
1. Go to '...'
2. Click on '...'
3. Scroll down to '...'
4. See error

## ✅ Expected Behavior

A clear and concise description of what you expected to happen.

## ❌ Actual Behavior

A clear and concise description of what actually happened.

## 📱 Environment

**Device Information:**
- Device: [e.g. iPhone 12, Samsung Galaxy S21, Pixel 5]
- OS: [e.g. iOS 15.0, Android 12]
- App Version: [e.g. 1.0.0]
- Flutter Version: [e.g. 3.24.0]

**Additional Context:**
- Network: [e.g. WiFi, 4G, 5G]
- Account Type: [e.g. Free, Premium]
- Household Size: [e.g. 2 users, 5 users]

## 📸 Screenshots

If applicable, add screenshots to help explain your problem.

## 📋 Additional Context

Add any other context about the problem here, such as:
- When did this start happening?
- Does it happen consistently or intermittently?
- Have you tried any workarounds?
- Any error messages in logs?

## 🔍 Logs

If applicable, add relevant log output:

```
Paste any relevant logs here
```

## ✅ Checklist

- [ ] I have searched existing issues to ensure this is not a duplicate
- [ ] I have provided all the requested information
- [ ] I have tested this on the latest version of the app
- [ ] I have included screenshots/videos if applicable

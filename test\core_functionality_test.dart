import 'package:flutter_test/flutter_test.dart';
import 'package:medytrack_mobile_v2/core/utils/supabase_utils.dart';
import 'package:medytrack_mobile_v2/core/network/network_info.dart';
import 'package:medytrack_mobile_v2/core/services/supabase_service.dart';
import 'package:medytrack_mobile_v2/data/datasources/medicine_remote_data_source.dart';
import 'package:medytrack_mobile_v2/data/repositories/medicine_repository_impl.dart';
import 'package:medytrack_mobile_v2/data/repositories/location_repository_impl.dart';
import 'package:medytrack_mobile_v2/data/repositories/family_member_repository_impl.dart';
import 'package:medytrack_mobile_v2/data/repositories/tag_repository_impl.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Core Functionality Test Suite
///
/// This test suite verifies that the core functionality we fixed is working:
/// 1. SupabaseUtils UUID validation
/// 2. Repository initialization without errors
/// 3. Data source creation without crashes
/// 4. Basic parameter validation
void main() {
  group('Core Functionality Tests', () {
    group('SupabaseUtils', () {
      test('should validate UUID correctly', () {
        // Test valid UUIDs
        expect(
            SupabaseUtils.isValidUUID('123e4567-e89b-12d3-a456-************'),
            isTrue);
        expect(
            SupabaseUtils.isValidUUID('550e8400-e29b-41d4-a716-************'),
            isTrue);

        // Test invalid UUIDs
        expect(SupabaseUtils.isValidUUID('invalid-uuid'), isFalse);
        expect(SupabaseUtils.isValidUUID('default-household'), isFalse);
        expect(SupabaseUtils.isValidUUID(''), isFalse);
        expect(SupabaseUtils.isValidUUID('123'), isFalse);
      });

      test('should validate required parameters correctly', () {
        // Test with valid parameters
        final validParams = {
          'household_id': '123e4567-e89b-12d3-a456-************',
          'name': 'Test Medicine',
          'quantity': 10,
        };
        expect(SupabaseUtils.validateRequiredParams(validParams), isTrue);

        // Test with missing parameters
        final invalidParams = {
          'household_id': '123e4567-e89b-12d3-a456-************',
          'name': '', // Empty string
          'quantity': 10,
        };
        expect(SupabaseUtils.validateRequiredParams(invalidParams), isFalse);

        // Test with null parameters
        final nullParams = {
          'household_id': '123e4567-e89b-12d3-a456-************',
          'name': null,
          'quantity': 10,
        };
        expect(SupabaseUtils.validateRequiredParams(nullParams), isFalse);
      });

      test('should format query filters correctly', () {
        final filters = SupabaseUtils.formatQueryFilters(
          householdId: '123e4567-e89b-12d3-a456-************',
          additionalFilters: {'name': 'Medicine Name'},
        );

        expect(filters['household_id'],
            equals('123e4567-e89b-12d3-a456-************'));
        expect(filters['name'], equals('Medicine Name'));
      });

      test('should provide standardized error messages', () {
        final testError = Exception('Database connection failed');
        final errorMessage = SupabaseUtils.getErrorMessage(testError);

        expect(errorMessage, isNotEmpty);
        expect(errorMessage, contains('Database connection failed'));
      });
    });

    group('Repository Initialization', () {
      test('should create MedicineRepositoryImpl without errors', () {
        // This tests that our dependency injection setup works
        expect(() {
          // Note: This would normally use GetIt, but we're testing the class creation
          // In a real app, this would be handled by the DI container
          final mockClient = _MockSupabaseClient();
          final dataSource =
              MedicineRemoteDataSourceImpl(supabaseClient: mockClient);
          final mockNetworkInfo = _MockNetworkInfo();
          final repository = MedicineRepositoryImpl(
            remoteDataSource: dataSource,
            networkInfo: mockNetworkInfo,
          );
          expect(repository, isNotNull);
        }, returnsNormally);
      });

      test('should create LocationRepositoryImpl without errors', () {
        expect(() {
          final mockClient = _MockSupabaseClient();
          final mockService = _MockSupabaseService();
          final repository = LocationRepositoryImpl(mockClient, mockService);
          expect(repository, isNotNull);
        }, returnsNormally);
      });

      test('should create FamilyMemberRepositoryImpl without errors', () {
        expect(() {
          final mockClient = _MockSupabaseClient();
          final mockService = _MockSupabaseService();
          final repository =
              FamilyMemberRepositoryImpl(mockClient, mockService);
          expect(repository, isNotNull);
        }, returnsNormally);
      });

      test('should create TagRepositoryImpl without errors', () {
        expect(() {
          final mockClient = _MockSupabaseClient();
          final repository = TagRepositoryImpl(mockClient);
          expect(repository, isNotNull);
        }, returnsNormally);
      });
    });

    group('Data Source Creation', () {
      test('should create MedicineRemoteDataSource without crashes', () {
        final mockClient = _MockSupabaseClient();

        expect(() {
          final dataSource =
              MedicineRemoteDataSourceImpl(supabaseClient: mockClient);
          expect(dataSource, isNotNull);
        }, returnsNormally);
      });
    });

    group('Parameter Validation Edge Cases', () {
      test('should handle empty household ID', () {
        expect(SupabaseUtils.isValidUUID(''), isFalse);
      });

      test('should handle null household ID', () {
        expect(SupabaseUtils.isValidUUID(null), isFalse);
      });

      test('should handle malformed UUIDs', () {
        expect(SupabaseUtils.isValidUUID('123-456-789'), isFalse);
        expect(SupabaseUtils.isValidUUID('not-a-uuid-at-all'), isFalse);
        expect(SupabaseUtils.isValidUUID('123e4567-e89b-12d3-a456'),
            isFalse); // Too short
      });
    });
  });
}

/// Mock Supabase client for testing
class _MockSupabaseClient implements SupabaseClient {
  @override
  dynamic noSuchMethod(Invocation invocation) => null;
}

/// Mock Supabase service for testing
class _MockSupabaseService implements SupabaseService {
  @override
  dynamic noSuchMethod(Invocation invocation) => null;
}

/// Mock NetworkInfo for testing
class _MockNetworkInfo implements NetworkInfo {
  @override
  Future<bool> get isConnected => Future.value(true);

  @override
  Stream<bool> get onConnectivityChanged => Stream.value(true);
}

import 'package:equatable/equatable.dart';
import '../../../domain/entities/family_member.dart';
import '../../../domain/entities/location.dart';
import '../../../domain/entities/tag.dart';

/// Base class for personalization events
abstract class PersonalizationEvent extends Equatable {
  const PersonalizationEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load personalization data
class PersonalizationLoadRequested extends PersonalizationEvent {
  final String userId;
  final String householdId;

  const PersonalizationLoadRequested({
    required this.userId,
    required this.householdId,
  });

  @override
  List<Object?> get props => [userId, householdId];
}

/// Event to update minimum expiry threshold
class MinimumExpiryThresholdUpdateRequested extends PersonalizationEvent {
  final int days;

  const MinimumExpiryThresholdUpdateRequested({required this.days});

  @override
  List<Object?> get props => [days];
}

/// Event to update default location
class DefaultLocationUpdateRequested extends PersonalizationEvent {
  final String locationId;

  const DefaultLocationUpdateRequested({required this.locationId});

  @override
  List<Object?> get props => [locationId];
}

/// Event to update default family member
class DefaultFamilyMemberUpdateRequested extends PersonalizationEvent {
  final String familyMemberId;

  const DefaultFamilyMemberUpdateRequested({required this.familyMemberId});

  @override
  List<Object?> get props => [familyMemberId];
}

/// Event to toggle show expired medicines
class ShowExpiredMedicinesToggleRequested extends PersonalizationEvent {
  final bool show;

  const ShowExpiredMedicinesToggleRequested({required this.show});

  @override
  List<Object?> get props => [show];
}

/// Event to toggle group by location
class GroupByLocationToggleRequested extends PersonalizationEvent {
  final bool groupByLocation;

  const GroupByLocationToggleRequested({required this.groupByLocation});

  @override
  List<Object?> get props => [groupByLocation];
}

/// Event to add favorite category
class FavoriteCategoryAddRequested extends PersonalizationEvent {
  final String category;

  const FavoriteCategoryAddRequested({required this.category});

  @override
  List<Object?> get props => [category];
}

/// Event to remove favorite category
class FavoriteCategoryRemoveRequested extends PersonalizationEvent {
  final String category;

  const FavoriteCategoryRemoveRequested({required this.category});

  @override
  List<Object?> get props => [category];
}

/// Event to create family member
class FamilyMemberCreateRequested extends PersonalizationEvent {
  final FamilyMember member;

  const FamilyMemberCreateRequested({required this.member});

  @override
  List<Object?> get props => [member];
}

/// Event to update family member
class FamilyMemberUpdateRequested extends PersonalizationEvent {
  final FamilyMember member;

  const FamilyMemberUpdateRequested({required this.member});

  @override
  List<Object?> get props => [member];
}

/// Event to delete family member
class FamilyMemberDeleteRequested extends PersonalizationEvent {
  final String memberId;

  const FamilyMemberDeleteRequested({required this.memberId});

  @override
  List<Object?> get props => [memberId];
}

/// Event to create location
class LocationCreateRequested extends PersonalizationEvent {
  final Location location;

  const LocationCreateRequested({required this.location});

  @override
  List<Object?> get props => [location];
}

/// Event to update location
class LocationUpdateRequested extends PersonalizationEvent {
  final Location location;

  const LocationUpdateRequested({required this.location});

  @override
  List<Object?> get props => [location];
}

/// Event to delete location
class LocationDeleteRequested extends PersonalizationEvent {
  final String locationId;

  const LocationDeleteRequested({required this.locationId});

  @override
  List<Object?> get props => [locationId];
}

/// Event to create tag
class TagCreateRequested extends PersonalizationEvent {
  final Tag tag;

  const TagCreateRequested({required this.tag});

  @override
  List<Object?> get props => [tag];
}

/// Event to update tag
class TagUpdateRequested extends PersonalizationEvent {
  final Tag tag;

  const TagUpdateRequested({required this.tag});

  @override
  List<Object?> get props => [tag];
}

/// Event to delete tag
class TagDeleteRequested extends PersonalizationEvent {
  final String tagId;

  const TagDeleteRequested({required this.tagId});

  @override
  List<Object?> get props => [tagId];
}

/// Event to refresh all data
class PersonalizationRefreshRequested extends PersonalizationEvent {
  const PersonalizationRefreshRequested();
}

/// Event to search family members
class FamilyMemberSearchRequested extends PersonalizationEvent {
  final String query;

  const FamilyMemberSearchRequested({required this.query});

  @override
  List<Object?> get props => [query];
}

/// Event to search locations
class LocationSearchRequested extends PersonalizationEvent {
  final String query;

  const LocationSearchRequested({required this.query});

  @override
  List<Object?> get props => [query];
}

/// Event to search tags
class TagSearchRequested extends PersonalizationEvent {
  final String query;

  const TagSearchRequested({required this.query});

  @override
  List<Object?> get props => [query];
}

/// Event to clear search
class SearchClearRequested extends PersonalizationEvent {
  const SearchClearRequested();
}

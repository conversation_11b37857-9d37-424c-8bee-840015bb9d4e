import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../domain/entities/dashboard_stats.dart';
import '../../../domain/repositories/dashboard_repository.dart';
import '../../../domain/usecases/dashboard/get_dashboard_stats_usecase.dart';
import '../../../domain/usecases/dashboard/get_filtered_dashboard_stats_usecase.dart';
import '../../../core/usecases/usecase.dart';
import '../../../core/utils/debug_logger.dart';

// Events
abstract class DashboardEvent extends Equatable {
  const DashboardEvent();
  @override
  List<Object?> get props => [];
}

class DashboardLoadRequested extends DashboardEvent {
  final String householdId;
  const DashboardLoadRequested({required this.householdId});
  @override
  List<Object?> get props => [householdId];
}

class DashboardFilteredLoadRequested extends DashboardEvent {
  final DashboardStatsParams params;
  const DashboardFilteredLoadRequested({required this.params});
  @override
  List<Object?> get props => [params];
}

class DashboardClearRequested extends DashboardEvent {
  const DashboardClearRequested();
}

// States
abstract class DashboardState extends Equatable {
  const DashboardState();
  @override
  List<Object?> get props => [];
}

class DashboardInitial extends DashboardState {
  const DashboardInitial();
}

class DashboardLoading extends DashboardState {
  const DashboardLoading();
}

class DashboardLoaded extends DashboardState {
  final DashboardStats stats;
  final List<MedicineExpiringSoon> expiringMedicines;

  const DashboardLoaded({
    required this.stats,
    this.expiringMedicines = const [],
  });

  @override
  List<Object?> get props => [stats, expiringMedicines];
}

class DashboardError extends DashboardState {
  final String message;
  const DashboardError({required this.message});
  @override
  List<Object?> get props => [message];
}

// BLoC
class DashboardBloc extends Bloc<DashboardEvent, DashboardState> {
  final GetDashboardStatsUseCase getDashboardStatsUseCase;
  final GetFilteredDashboardStatsUseCase getFilteredDashboardStatsUseCase;

  DashboardBloc({
    required this.getDashboardStatsUseCase,
    required this.getFilteredDashboardStatsUseCase,
  }) : super(const DashboardInitial()) {
    on<DashboardLoadRequested>(_onLoadRequested);
    on<DashboardFilteredLoadRequested>(_onFilteredLoadRequested);
    on<DashboardClearRequested>(_onClearRequested);
  }

  Future<void> _onLoadRequested(
    DashboardLoadRequested event,
    Emitter<DashboardState> emit,
  ) async {
    emit(const DashboardLoading());

    try {
      DebugLogger.logBloc('DashboardLoadRequested', data: {
        'householdId': event.householdId,
      });

      final result = await getDashboardStatsUseCase.call(
        HouseholdParams(householdId: event.householdId),
      );

      result.fold(
        (failure) => emit(DashboardError(message: failure.message)),
        (stats) => emit(DashboardLoaded(
          stats: stats,
          expiringMedicines: const [], // TODO: Load expiring medicines
        )),
      );
    } catch (e) {
      emit(DashboardError(message: e.toString()));
    }
  }

  Future<void> _onFilteredLoadRequested(
    DashboardFilteredLoadRequested event,
    Emitter<DashboardState> emit,
  ) async {
    emit(const DashboardLoading());

    try {
      DebugLogger.logBloc('DashboardFilteredLoadRequested', data: {
        'householdId': event.params.householdId,
        'locationId': event.params.locationId,
        'familyMemberId': event.params.familyMemberId,
        'filter': event.params.filter,
        'filterDescription': event.params.filterDescription,
      });

      final result = await getFilteredDashboardStatsUseCase.call(event.params);

      result.fold(
        (failure) => emit(DashboardError(message: failure.message)),
        (stats) {
          DebugLogger.logBloc('Filtered dashboard stats loaded', data: {
            'total': stats.total,
            'expired': stats.expired,
            'expiringSoon': stats.expiringSoon,
            'lowStock': stats.lowStock,
          });

          emit(DashboardLoaded(
            stats: stats,
            expiringMedicines: const [], // TODO: Load expiring medicines
          ));
        },
      );
    } catch (e) {
      emit(DashboardError(message: e.toString()));
    }
  }

  Future<void> _onClearRequested(
    DashboardClearRequested event,
    Emitter<DashboardState> emit,
  ) async {
    emit(const DashboardInitial());
  }
}

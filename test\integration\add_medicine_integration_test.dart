import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mocktail/mocktail.dart';

import 'package:medytrack_mobile_v2/domain/entities/tunisia_medicine.dart';
import 'package:medytrack_mobile_v2/domain/entities/tag.dart';
import 'package:medytrack_mobile_v2/domain/entities/location.dart';
import 'package:medytrack_mobile_v2/domain/entities/family_member.dart';
import 'package:medytrack_mobile_v2/domain/repositories/tunisia_medicine_repository.dart';
import 'package:medytrack_mobile_v2/domain/repositories/tag_repository.dart';
import 'package:medytrack_mobile_v2/domain/repositories/location_repository.dart';
import 'package:medytrack_mobile_v2/domain/repositories/family_member_repository.dart';
import 'package:medytrack_mobile_v2/domain/repositories/medicine_repository.dart';
import 'package:medytrack_mobile_v2/presentation/bloc/add_medicine/add_medicine_bloc.dart';
import 'package:medytrack_mobile_v2/presentation/bloc/add_medicine/add_medicine_event.dart';
import 'package:medytrack_mobile_v2/presentation/pages/medicine/add_medicine_page.dart';
import 'package:medytrack_mobile_v2/core/theme/app_theme.dart';

// Mock classes using mocktail
class MockTunisiaMedicineRepository extends Mock
    implements TunisiaMedicineRepository {}

class MockTagRepository extends Mock implements TagRepository {}

class MockLocationRepository extends Mock implements LocationRepository {}

class MockFamilyMemberRepository extends Mock
    implements FamilyMemberRepository {}

class MockMedicineRepository extends Mock implements MedicineRepository {}

void main() {
  group('AddMedicine Integration Tests', () {
    late MockTunisiaMedicineRepository mockTunisiaMedicineRepository;
    late MockTagRepository mockTagRepository;
    late MockLocationRepository mockLocationRepository;
    late MockFamilyMemberRepository mockFamilyMemberRepository;
    late MockMedicineRepository mockMedicineRepository;
    late AddMedicineBloc addMedicineBloc;

    setUp(() {
      mockTunisiaMedicineRepository = MockTunisiaMedicineRepository();
      mockTagRepository = MockTagRepository();
      mockLocationRepository = MockLocationRepository();
      mockFamilyMemberRepository = MockFamilyMemberRepository();
      mockMedicineRepository = MockMedicineRepository();

      addMedicineBloc = AddMedicineBloc(
        mockTunisiaMedicineRepository,
        mockTagRepository,
        mockLocationRepository,
        mockFamilyMemberRepository,
        mockMedicineRepository,
      );
    });

    tearDown(() {
      addMedicineBloc.close();
    });

    testWidgets('should display search interface in database mode',
        (tester) async {
      // Arrange
      when(() => mockTagRepository.getTagsByCategory(any(), any()))
          .thenAnswer((_) async => []);
      when(() => mockLocationRepository.getHouseholdLocations(any()))
          .thenAnswer((_) => Stream.value([]));
      when(() => mockFamilyMemberRepository.getHouseholdMembers(any()))
          .thenAnswer((_) => Stream.value([]));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: BlocProvider.value(
            value: addMedicineBloc,
            child: const AddMedicinePage(),
          ),
        ),
      );

      addMedicineBloc
          .add(const AddMedicineInitialized(householdId: 'test-household'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Ajouter un médicament'), findsOneWidget);
      expect(find.text('Sélection du médicament'), findsOneWidget);
      expect(find.byType(TextFormField), findsWidgets);
    });

    testWidgets('should perform medicine search and display results',
        (tester) async {
      // Arrange
      final mockMedicines = [
        const TunisiaMedicine(
          id: '1',
          nom: 'Paracétamol 500mg',
          dosage: '500mg',
          forme: 'Comprimé',
          laboratoire: 'Lab Test',
        ),
        const TunisiaMedicine(
          id: '2',
          nom: 'Paracétamol 1000mg',
          dosage: '1000mg',
          forme: 'Comprimé',
          laboratoire: 'Lab Test',
        ),
      ];

      when(() => mockTunisiaMedicineRepository.searchMedicines(any(),
          limit: any(named: 'limit'))).thenAnswer((_) async => mockMedicines);
      when(() => mockTagRepository.getTagsByCategory(any(), any()))
          .thenAnswer((_) async => []);
      when(() => mockLocationRepository.getHouseholdLocations(any()))
          .thenAnswer((_) => Stream.value([]));
      when(() => mockFamilyMemberRepository.getHouseholdMembers(any()))
          .thenAnswer((_) => Stream.value([]));

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: BlocProvider.value(
            value: addMedicineBloc,
            child: const AddMedicinePage(),
          ),
        ),
      );

      addMedicineBloc
          .add(const AddMedicineInitialized(householdId: 'test-household'));
      await tester.pumpAndSettle();

      // Act - Search for medicine
      addMedicineBloc.add(const MedicinesSearched(query: 'Paracétamol'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Paracétamol 500mg'), findsOneWidget);
      expect(find.text('Paracétamol 1000mg'), findsOneWidget);
    });

    testWidgets('should switch to custom mode and show custom fields',
        (tester) async {
      // Arrange
      when(() => mockTagRepository.getTagsByCategory(any(), any()))
          .thenAnswer((_) async => []);
      when(() => mockLocationRepository.getHouseholdLocations(any()))
          .thenAnswer((_) => Stream.value([]));
      when(() => mockFamilyMemberRepository.getHouseholdMembers(any()))
          .thenAnswer((_) => Stream.value([]));

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: BlocProvider.value(
            value: addMedicineBloc,
            child: const AddMedicinePage(),
          ),
        ),
      );

      addMedicineBloc
          .add(const AddMedicineInitialized(householdId: 'test-household'));
      await tester.pumpAndSettle();

      // Act - Toggle to custom mode
      addMedicineBloc.add(const ModeToggled(isCustomMode: true));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Mode personnalisé activé'), findsOneWidget);
      expect(find.text('Informations du médicament'), findsOneWidget);
      expect(find.text('Nom du médicament *'), findsOneWidget);
    });

    testWidgets('should display tag selection interface', (tester) async {
      // Arrange
      final mockTags = [
        Tag(
          id: '1',
          householdId: 'test-household',
          name: 'Antibiotique',
          color: '#FF6B6B',
          category: 'therapeutic',
          createdAt: DateTime.now(),
        ),
        Tag(
          id: '2',
          householdId: 'test-household',
          name: 'Quotidien',
          color: '#2ECC71',
          category: 'usage',
          createdAt: DateTime.now(),
        ),
      ];

      when(() => mockTagRepository.getTagsByCategory(
              'test-household', 'therapeutic'))
          .thenAnswer((_) async => [mockTags[0]]);
      when(() => mockTagRepository.getTagsByCategory('test-household', 'usage'))
          .thenAnswer((_) async => [mockTags[1]]);
      when(() => mockLocationRepository.getHouseholdLocations(any()))
          .thenAnswer((_) => Stream.value([]));
      when(() => mockFamilyMemberRepository.getHouseholdMembers(any()))
          .thenAnswer((_) => Stream.value([]));

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: BlocProvider.value(
            value: addMedicineBloc,
            child: const AddMedicinePage(),
          ),
        ),
      );

      addMedicineBloc
          .add(const AddMedicineInitialized(householdId: 'test-household'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Catégories et étiquettes'), findsOneWidget);
      expect(find.text('Catégories thérapeutiques'), findsOneWidget);
      expect(find.text('Usage et posologie'), findsOneWidget);
      expect(find.text('Antibiotique'), findsOneWidget);
      expect(find.text('Quotidien'), findsOneWidget);
    });

    testWidgets('should display location and family member dropdowns',
        (tester) async {
      // Arrange
      final mockLocations = [
        Location(
          id: '1',
          householdId: 'test-household',
          name: 'Armoire à pharmacie',
          icon: 'medical_services',
          createdAt: DateTime.now(),
        ),
      ];

      final mockMembers = [
        FamilyMember(
          id: '1',
          householdId: 'test-household',
          name: 'John Doe',
          createdAt: DateTime.now(),
        ),
      ];

      when(() => mockTagRepository.getTagsByCategory(any(), any()))
          .thenAnswer((_) async => []);
      when(() => mockLocationRepository.getHouseholdLocations('test-household'))
          .thenAnswer((_) => Stream.value(mockLocations));
      when(() =>
              mockFamilyMemberRepository.getHouseholdMembers('test-household'))
          .thenAnswer((_) => Stream.value(mockMembers));

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: BlocProvider.value(
            value: addMedicineBloc,
            child: const AddMedicinePage(),
          ),
        ),
      );

      addMedicineBloc
          .add(const AddMedicineInitialized(householdId: 'test-household'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Emplacement et membre de la famille'), findsOneWidget);
      expect(find.text('Emplacement'), findsOneWidget);
      expect(find.text('Membre de la famille'), findsOneWidget);
    });

    testWidgets('should validate form and show errors', (tester) async {
      // Arrange
      when(() => mockTagRepository.getTagsByCategory(any(), any()))
          .thenAnswer((_) async => []);
      when(() => mockLocationRepository.getHouseholdLocations(any()))
          .thenAnswer((_) => Stream.value([]));
      when(() => mockFamilyMemberRepository.getHouseholdMembers(any()))
          .thenAnswer((_) => Stream.value([]));

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: BlocProvider.value(
            value: addMedicineBloc,
            child: const AddMedicinePage(),
          ),
        ),
      );

      addMedicineBloc
          .add(const AddMedicineInitialized(householdId: 'test-household'));
      await tester.pumpAndSettle();

      // Act - Try to submit without required fields
      final submitButton = find.text('Ajouter le médicament');
      expect(submitButton, findsOneWidget);

      await tester.tap(submitButton);
      await tester.pumpAndSettle();

      // Assert - Button should be disabled due to validation
      final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(button.onPressed, isNull);
    });
  });
}

import 'package:equatable/equatable.dart';

abstract class MedicineDetailEvent extends Equatable {
  const MedicineDetailEvent();

  @override
  List<Object?> get props => [];
}

/// Load medicine details by ID
class MedicineDetailLoadRequested extends MedicineDetailEvent {
  final String medicineId;

  const MedicineDetailLoadRequested({required this.medicineId});

  @override
  List<Object?> get props => [medicineId];
}

/// Update medicine quantity
class MedicineQuantityUpdated extends MedicineDetailEvent {
  final String medicineId;
  final int newQuantity;

  const MedicineQuantityUpdated({
    required this.medicineId,
    required this.newQuantity,
  });

  @override
  List<Object?> get props => [medicineId, newQuantity];
}

/// Mark medicine as taken
class MedicineTaken extends MedicineDetailEvent {
  final String medicineId;
  final int quantityTaken;

  const MedicineTaken({
    required this.medicineId,
    required this.quantityTaken,
  });

  @override
  List<Object?> get props => [medicineId, quantityTaken];
}

/// Delete medicine
class MedicineDetailDeleteRequested extends MedicineDetailEvent {
  final String medicineId;

  const MedicineDetailDeleteRequested({required this.medicineId});

  @override
  List<Object?> get props => [medicineId];
}

/// Clear medicine detail state
class MedicineDetailClearRequested extends MedicineDetailEvent {
  const MedicineDetailClearRequested();
}

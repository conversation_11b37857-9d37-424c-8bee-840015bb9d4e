import '../../domain/entities/location.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Data model for Location entity
class LocationModel extends Location {
  const LocationModel({
    required super.id,
    required super.householdId,
    required super.name,
    super.description,
    required super.icon,
    required super.createdAt,
    super.updatedAt,
  });

  /// Create from JSON
  factory LocationModel.fromJson(Map<String, dynamic> json) {
    try {
      // Validate required fields and provide defaults for null values
      final id = json['id'] as String? ?? '';
      final householdId = json['household_id'] as String?;
      final name = json['name'] as String?;

      if (householdId == null || householdId.isEmpty) {
        throw Exception('Location household_id cannot be null or empty');
      }
      if (name == null || name.isEmpty) {
        throw Exception('Location name cannot be null or empty');
      }

      // Provide default values for fields that might not exist in database
      final safeIcon =
          'home'; // Default icon since icon is not in database schema
      final safeCreatedAt = json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : DateTime.now();

      return LocationModel(
        id: id,
        householdId: householdId,
        name: name,
        description: json['description'] as String?,
        icon: safeIcon,
        createdAt: safeCreatedAt,
        updatedAt: json['updated_at'] != null
            ? DateTime.parse(json['updated_at'] as String)
            : null,
      );
    } catch (e) {
      throw Exception('Failed to parse Location from JSON: $e. JSON: $json');
    }
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    // Only include fields that actually exist in the database schema
    final currentUser = Supabase.instance.client.auth.currentUser;
    return {
      'household_id': householdId,
      'name': name,
      'description': description,
      'user_id': currentUser?.id, // Track which user created the location
      // Note: family_id, id, and created_at are handled by database
    };
  }

  /// Create from entity
  factory LocationModel.fromEntity(Location entity) {
    return LocationModel(
      id: entity.id,
      householdId: entity.householdId,
      name: entity.name,
      description: entity.description,
      icon: entity.icon,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  /// Convert to entity
  Location toEntity() {
    return Location(
      id: id,
      householdId: householdId,
      name: name,
      description: description,
      icon: icon,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  /// Copy with method
  @override
  LocationModel copyWith({
    String? id,
    String? householdId,
    String? name,
    String? description,
    String? icon,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LocationModel(
      id: id ?? this.id,
      householdId: householdId ?? this.householdId,
      name: name ?? this.name,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

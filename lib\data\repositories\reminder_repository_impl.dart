import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../core/network/network_info.dart';
import '../../domain/entities/reminder.dart';
import '../../domain/repositories/reminder_repository.dart';
import '../datasources/reminder_remote_data_source.dart';
import '../models/reminder_model.dart';

@LazySingleton(as: ReminderRepository)
class ReminderRepositoryImpl implements ReminderRepository {
  final ReminderRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  ReminderRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<Reminder>>> getRemindersForUserMedicine(
      String userMedicineId) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteReminders =
            await remoteDataSource.getRemindersForUserMedicine(userMedicineId);
        return Right(remoteReminders);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      }
    } else {
      return Left(const NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<Reminder>>> getRemindersForMultipleMedicines(
      List<String> userMedicineIds) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteReminders = await remoteDataSource
            .getRemindersForMultipleMedicines(userMedicineIds);
        return Right(remoteReminders);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      }
    } else {
      return Left(const NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Reminder>> addReminder(Reminder reminder) async {
    if (await networkInfo.isConnected) {
      try {
        final reminderModel = ReminderModel.fromEntity(reminder);
        final result = await remoteDataSource.addReminder(reminderModel);
        return Right(result);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      }
    } else {
      return Left(const NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Reminder>> updateReminder(Reminder reminder) async {
    if (await networkInfo.isConnected) {
      try {
        final reminderModel = ReminderModel.fromEntity(reminder);
        final result = await remoteDataSource.updateReminder(reminderModel);
        return Right(result);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      }
    } else {
      return Left(const NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Unit>> deleteReminder(String reminderId) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.deleteReminder(reminderId);
        return const Right(unit);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      }
    } else {
      return Left(const NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<DoseHistory>>> getDoseHistoryForUserMedicine(
      String userMedicineId) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteDoseHistory = await remoteDataSource
            .getDoseHistoryForUserMedicine(userMedicineId);
        return Right(remoteDoseHistory);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      }
    } else {
      return Left(const NetworkFailure('No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Unit>> addDoseHistory(DoseHistory doseHistory) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.addDoseHistory(doseHistory);
        return const Right(unit);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      }
    } else {
      return Left(const NetworkFailure('No internet connection'));
    }
  }
}

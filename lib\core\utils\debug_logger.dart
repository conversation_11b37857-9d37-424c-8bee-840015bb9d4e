import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

/// Comprehensive debug logging utility for tracing data flow
class DebugLogger {
  static const String _tag = 'MedyTrack_Debug';
  static final List<String> _logs = [];
  
  /// Get all logs for display
  static List<String> get logs => List.unmodifiable(_logs);
  
  /// Clear all logs
  static void clearLogs() {
    _logs.clear();
  }
  
  /// Log with timestamp and category
  static void log(String category, String message, {Object? data, StackTrace? stackTrace}) {
    final timestamp = DateTime.now().toIso8601String().substring(11, 23);
    final logMessage = '[$timestamp] [$category] $message';
    
    // Add to internal log list
    _logs.add(logMessage);
    
    // Print to console in debug mode
    if (kDebugMode) {
      print(logMessage);
      if (data != null) {
        print('  Data: $data');
      }
      if (stackTrace != null) {
        print('  Stack: ${stackTrace.toString().split('\n').take(3).join('\n')}');
      }
    }
    
    // Also log to developer console
    developer.log(
      message,
      name: '$_tag.$category',
      time: DateTime.now(),
      error: data,
      stackTrace: stackTrace,
    );
  }
  
  /// Log authentication flow
  static void logAuth(String step, {Object? data}) {
    log('AUTH', step, data: data);
  }
  
  /// Log BLoC events and states
  static void logBloc(String event, {Object? data}) {
    log('BLOC', event, data: data);
  }
  
  /// Log repository operations
  static void logRepository(String operation, {Object? data}) {
    log('REPO', operation, data: data);
  }
  
  /// Log database operations
  static void logDatabase(String query, {Object? data}) {
    log('DB', query, data: data);
  }
  
  /// Log errors with full context
  static void logError(String category, String error, {Object? data, StackTrace? stackTrace}) {
    log('ERROR_$category', error, data: data, stackTrace: stackTrace);
  }
  
  /// Log UI updates
  static void logUI(String component, String update, {Object? data}) {
    log('UI', '$component: $update', data: data);
  }
  
  /// Log data transformations
  static void logTransform(String from, String to, {Object? data}) {
    log('TRANSFORM', '$from -> $to', data: data);
  }
  
  /// Create a detailed audit trail for a specific operation
  static void startAudit(String operation) {
    log('AUDIT_START', '=== Starting audit for: $operation ===');
  }
  
  static void endAudit(String operation, {bool success = true}) {
    log('AUDIT_END', '=== ${success ? 'SUCCESS' : 'FAILED'}: $operation ===');
  }
  
  /// Log method entry and exit
  static void logMethodEntry(String className, String methodName, {Map<String, dynamic>? params}) {
    log('METHOD_ENTRY', '$className.$methodName', data: params);
  }
  
  static void logMethodExit(String className, String methodName, {Object? result}) {
    log('METHOD_EXIT', '$className.$methodName', data: result);
  }
  
  /// Log state changes with before/after
  static void logStateChange(String component, Object? before, Object? after) {
    log('STATE_CHANGE', '$component state changed');
    log('STATE_BEFORE', 'Previous state', data: before);
    log('STATE_AFTER', 'New state', data: after);
  }
  
  /// Export logs as formatted string
  static String exportLogs() {
    return _logs.join('\n');
  }
  
  /// Get recent logs (last N entries)
  static List<String> getRecentLogs(int count) {
    if (_logs.length <= count) return List.from(_logs);
    return _logs.sublist(_logs.length - count);
  }
  
  /// Filter logs by category
  static List<String> getLogsByCategory(String category) {
    return _logs.where((log) => log.contains('[$category]')).toList();
  }
  
  /// Get error logs only
  static List<String> getErrorLogs() {
    return _logs.where((log) => log.contains('[ERROR_')).toList();
  }
  
  /// Log performance metrics
  static void logPerformance(String operation, Duration duration, {Object? data}) {
    log('PERFORMANCE', '$operation took ${duration.inMilliseconds}ms', data: data);
  }
  
  /// Measure and log execution time
  static Future<T> measureExecution<T>(
    String operation,
    Future<T> Function() function, {
    Object? additionalData,
  }) async {
    final stopwatch = Stopwatch()..start();
    try {
      log('PERFORMANCE_START', 'Starting: $operation');
      final result = await function();
      stopwatch.stop();
      logPerformance(operation, stopwatch.elapsed, data: additionalData);
      return result;
    } catch (e, stackTrace) {
      stopwatch.stop();
      logError('PERFORMANCE', 'Failed: $operation after ${stopwatch.elapsed.inMilliseconds}ms', 
               data: e, stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// Log network/API calls
  static void logApiCall(String endpoint, String method, {Object? params, Object? response}) {
    log('API_CALL', '$method $endpoint', data: {'params': params, 'response': response});
  }
  
  /// Log validation results
  static void logValidation(String field, bool isValid, {String? error}) {
    log('VALIDATION', '$field: ${isValid ? 'VALID' : 'INVALID'}', data: error);
  }
}

/// Extension for easy logging in any class
extension DebugLogging on Object {
  void debugLog(String category, String message, {Object? data}) {
    DebugLogger.log(category, '${runtimeType}: $message', data: data);
  }
  
  void debugError(String message, {Object? error, StackTrace? stackTrace}) {
    DebugLogger.logError(runtimeType.toString(), message, data: error, stackTrace: stackTrace);
  }
}

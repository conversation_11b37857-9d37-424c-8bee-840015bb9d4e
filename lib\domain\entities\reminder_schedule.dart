import 'package:equatable/equatable.dart';

/// Unified schedule model to drive the multi-step reminder flow and map to backend fields.
/// Supports: DAILY, WEEKLY, EVERY_X_DAYS, MONTHLY, CUSTOM (specific dates), AS_NEEDED, EVERY_X_HOURS
///
/// Normalization choices:
/// - times are normalized to "HH:mm" strings
/// - daysOfWeek use 1-7 (Mon=1 .. Sun=7)
/// - startDate defaults to today if null when summarizing/saving
/// - endDate optional
enum ReminderScheduleFrequencyType {
  daily,
  weekly,
  everyXDays,
  monthly,
  customDates,
  asNeeded,
  everyXHours,
}

class ReminderSchedule extends Equatable {
  final ReminderScheduleFrequencyType frequencyType;

  // Intervals
  final int? intervalDays; // for everyXDays
  final int? intervalWeeks; // for weekly cadence with interval
  final int? intervalMonths; // for monthly cadence
  final int? intervalHours; // for everyXHours

  // Weekly specifics
  final List<int> daysOfWeek; // 1-7 Mon..Sun

  // Times (HH:mm)
  final List<String> times;

  // Custom dates (localized to user TZ upstream)
  final List<DateTime> specificDates;

  final DateTime? startDate;
  final DateTime? endDate;

  // Dosage
  final double? dosageAmount;
  final String? dosageUnit;

  // Additional notes/instructions
  final String? instructions;

  const ReminderSchedule({
    required this.frequencyType,
    this.intervalDays,
    this.intervalWeeks,
    this.intervalMonths,
    this.intervalHours,
    this.daysOfWeek = const [],
    this.times = const [],
    this.specificDates = const [],
    this.startDate,
    this.endDate,
    this.dosageAmount,
    this.dosageUnit,
    this.instructions,
  });

  ReminderSchedule copyWith({
    ReminderScheduleFrequencyType? frequencyType,
    int? intervalDays,
    int? intervalWeeks,
    int? intervalMonths,
    int? intervalHours,
    List<int>? daysOfWeek,
    List<String>? times,
    List<DateTime>? specificDates,
    DateTime? startDate,
    DateTime? endDate,
    double? dosageAmount,
    String? dosageUnit,
    String? instructions,
    bool clearEndDate = false,
  }) {
    return ReminderSchedule(
      frequencyType: frequencyType ?? this.frequencyType,
      intervalDays: intervalDays ?? this.intervalDays,
      intervalWeeks: intervalWeeks ?? this.intervalWeeks,
      intervalMonths: intervalMonths ?? this.intervalMonths,
      intervalHours: intervalHours ?? this.intervalHours,
      daysOfWeek: daysOfWeek ?? this.daysOfWeek,
      times: times ?? this.times,
      specificDates: specificDates ?? this.specificDates,
      startDate: startDate ?? this.startDate,
      endDate: clearEndDate ? null : (endDate ?? this.endDate),
      dosageAmount: dosageAmount ?? this.dosageAmount,
      dosageUnit: dosageUnit ?? this.dosageUnit,
      instructions: instructions ?? this.instructions,
    );
  }

  // Basic validations according to spec
  List<String> validate() {
    final errors = <String>[];

    // Frequency required
    // (Always set because enum required)

    // Times required except AS_NEEDED and EVERY_X_HOURS may allow no explicit times
    final needsTimes = frequencyType != ReminderScheduleFrequencyType.asNeeded && frequencyType != ReminderScheduleFrequencyType.everyXHours && frequencyType != ReminderScheduleFrequencyType.customDates;
    if (needsTimes && (times.isEmpty)) {
      errors.add('Au moins une heure est requise.');
    }

    // Interval X must be ≥1 where applicable
    if (frequencyType == ReminderScheduleFrequencyType.everyXDays && (intervalDays == null || intervalDays! < 1)) {
      errors.add('L\'intervalle en jours doit être supérieur ou égal à 1.');
    }
    if (frequencyType == ReminderScheduleFrequencyType.weekly && (intervalWeeks != null && intervalWeeks! < 1)) {
      errors.add('L\'intervalle en semaines doit être supérieur ou égal à 1.');
    }
    if (frequencyType == ReminderScheduleFrequencyType.monthly && (intervalMonths != null && intervalMonths! < 1)) {
      errors.add('L\'intervalle en mois doit être supérieur ou égal à 1.');
    }
    if (frequencyType == ReminderScheduleFrequencyType.everyXHours && (intervalHours == null || intervalHours! < 1)) {
      errors.add('L\'intervalle en heures doit être supérieur ou égal à 1.');
    }

    // Weekday selection required for weekly when daysOfWeek-based
    if (frequencyType == ReminderScheduleFrequencyType.weekly && (daysOfWeek.isEmpty)) {
      // Only enforce when using specific days. In our UI we use specific days for weekly mode.
      errors.add('Veuillez sélectionner au moins un jour de la semaine.');
    }

    // Custom dates validation
    if (frequencyType == ReminderScheduleFrequencyType.customDates && specificDates.isEmpty) {
      errors.add('Veuillez sélectionner au moins une date.');
    }

    // start_date ≤ end_date
    if (startDate != null && endDate != null && endDate!.isBefore(startDate!)) {
      errors.add('La date de fin doit être après la date de début.');
    }

    return errors;
  }

  // Human readable summary per spec
  String summary({String? medicineName}) {
    final name = medicineName ?? 'votre médicament';
    final doseText = (dosageAmount != null && dosageUnit != null)
        ? '${_formatAmount(dosageAmount!)} $dosageUnit'
        : 'dose';

    final startTxt = startDate != null ? _formatDate(startDate!) : 'aujourd\'hui';
    final endTxt = endDate != null ? ' jusqu\'au ${_formatDate(endDate!)}' : '';

    String timeList() {
      if (times.isEmpty) return '';
      if (times.length == 1) return ' à ${times.first}';
      return ' à ${times.join(' et ')}';
    }

    switch (frequencyType) {
      case ReminderScheduleFrequencyType.asNeeded:
        return 'Prendre $doseText de $name si nécessaire. Instructions: ${instructions ?? '—'}';
      case ReminderScheduleFrequencyType.everyXHours:
        final h = intervalHours ?? 1;
        return 'Prendre $doseText de $name toutes les $h heures à partir du $startTxt$endTxt. Instructions: ${instructions ?? '—'}';
      case ReminderScheduleFrequencyType.daily:
        return 'Prendre $doseText de $name tous les jours$timeList() à partir du $startTxt$endTxt. Instructions: ${instructions ?? '—'}';
      case ReminderScheduleFrequencyType.everyXDays:
        final d = intervalDays ?? 1;
        return 'Prendre $doseText de $name tous les $d jours$timeList() à partir du $startTxt$endTxt. Instructions: ${instructions ?? '—'}';
      case ReminderScheduleFrequencyType.weekly:
        final weekInt = (intervalWeeks ?? 1);
        final days = daysOfWeek.map(_weekdayShort).join(' & ');
        final cadence = weekInt > 1 ? 'toutes les $weekInt semaines' : 'chaque semaine';
        return 'Prendre $doseText de $name $cadence les $days$timeList() à partir du $startTxt$endTxt. Instructions: ${instructions ?? '—'}';
      case ReminderScheduleFrequencyType.monthly:
        final m = (intervalMonths ?? 1);
        final cadence = m > 1 ? 'tous les $m mois' : 'chaque mois';
        return 'Prendre $doseText de $name $cadence$timeList() à partir du $startTxt$endTxt. Instructions: ${instructions ?? '—'}';
      case ReminderScheduleFrequencyType.customDates:
        final count = specificDates.length;
        final when = count == 1 ? 'à la date sélectionnée' : 'aux $count dates sélectionnées';
        final customTimes = times.isNotEmpty ? ' à ${times.join(' et ')}' : '';
        return 'Prendre $doseText de $name $when$customTimes. Instructions: ${instructions ?? '—'}';
    }
  }

  // Map to existing Reminder fields of this project.
  // Reminder fields reference in lib/domain/entities/reminder.dart
  // - frequencyType string accepted values in current code:
  //   'DAILY', 'WEEKLY', 'HOURLY_INTERVAL', 'SPECIFIC_DATES'
  // We will map:
  //  - daily -> DAILY
  //  - everyXDays -> DAILY with frequencyValue? or use EVERY_X_DAYS as string to align with assumptions.
  //    Since current entity comments accept these values, we prefer emitting:
  //    'EVERY_X_DAYS' when intervalDays != null && != 1.
  //  - weekly (specific days) -> WEEKLY with frequencyDays
  //  - monthly -> MONTHLY (requires backend acceptance; else fallback to recurrence_rule later)
  //  - customDates -> SPECIFIC_DATES and specificDates populated
  //  - asNeeded -> AS_NEEDED
  //  - everyXHours -> HOURLY_INTERVAL with frequencyValue = intervalHours
  //
  // Note: The repository/backend must accept these frequencyType strings; existing code paths already handle DAILY/WEEKLY/HOURLY_INTERVAL/SPECIFIC_DATES.
  // For new tokens (EVERY_X_DAYS, MONTHLY, AS_NEEDED) your backend must support. If not yet, consider translating them to closest supported:
  // - EVERY_X_DAYS -> DAILY + frequencyValue=null and manage via times; or keep as custom field. We emit EVERY_X_DAYS to align with assumptions.
  // - MONTHLY -> treat as SPECIFIC_DATES via generated occurrences (future).
  // - AS_NEEDED -> saved with empty times.
  Map<String, dynamic> toReminderInsertion({
    required String userMedicineId,
    required DateTime nowFallback,
    String? name,
  }) {
    final start = startDate ?? nowFallback;

    String freqString() {
      switch (frequencyType) {
        case ReminderScheduleFrequencyType.daily:
          return 'DAILY';
        case ReminderScheduleFrequencyType.weekly:
          return 'WEEKLY';
        case ReminderScheduleFrequencyType.everyXDays:
          return 'EVERY_X_DAYS';
        case ReminderScheduleFrequencyType.monthly:
          return 'MONTHLY';
        case ReminderScheduleFrequencyType.customDates:
          return 'SPECIFIC_DATES';
        case ReminderScheduleFrequencyType.asNeeded:
          return 'AS_NEEDED';
        case ReminderScheduleFrequencyType.everyXHours:
          return 'HOURLY_INTERVAL';
      }
    }

    int? frequencyValue() {
      switch (frequencyType) {
        case ReminderScheduleFrequencyType.everyXDays:
          return intervalDays;
        case ReminderScheduleFrequencyType.everyXHours:
          return intervalHours;
        case ReminderScheduleFrequencyType.weekly:
        case ReminderScheduleFrequencyType.monthly:
        case ReminderScheduleFrequencyType.daily:
        case ReminderScheduleFrequencyType.customDates:
        case ReminderScheduleFrequencyType.asNeeded:
          return null;
      }
    }

    final mapped = <String, dynamic>{
      'userMedicineId': userMedicineId,
      'name': name,
      'dosageAmount': dosageAmount,
      'dosageUnit': dosageUnit,
      'times': times,
      'frequencyType': freqString(),
      'frequencyValue': frequencyValue(),
      'frequencyDays': frequencyType == ReminderScheduleFrequencyType.weekly ? daysOfWeek : <int>[],
      'specificDates': frequencyType == ReminderScheduleFrequencyType.customDates ? specificDates : <DateTime>[],
      'startDate': start,
      'endDate': endDate,
      'notes': instructions,
      'isActive': true,
      'status': 'active',
    };

    return mapped;
  }

  static String _formatDate(DateTime d) {
    final dd = d.day.toString().padLeft(2, '0');
    final mm = d.month.toString().padLeft(2, '0');
    final yyyy = d.year.toString();
    return '$dd/$mm/$yyyy';
  }

  static String _weekdayShort(int day) {
    switch (day) {
      case 1:
        return 'Lun';
      case 2:
        return 'Mar';
      case 3:
        return 'Mer';
      case 4:
        return 'Jeu';
      case 5:
        return 'Ven';
      case 6:
        return 'Sam';
      case 7:
        return 'Dim';
      default:
        return '';
    }
  }

  static String _formatAmount(double amount) {
    if (amount % 1 == 0) return amount.toInt().toString();
    return amount.toString();
  }

  @override
  List<Object?> get props => [
        frequencyType,
        intervalDays,
        intervalWeeks,
        intervalMonths,
        intervalHours,
        daysOfWeek,
        times,
        specificDates,
        startDate,
        endDate,
        dosageAmount,
        dosageUnit,
        instructions,
      ];
}
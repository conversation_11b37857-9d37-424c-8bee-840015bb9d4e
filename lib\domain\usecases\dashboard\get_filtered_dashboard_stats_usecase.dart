import 'package:dartz/dartz.dart';
import '../../entities/dashboard_stats.dart';
import '../../repositories/dashboard_repository.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';

class GetFilteredDashboardStatsUseCase implements UseCase<DashboardStats, DashboardStatsParams> {
  final DashboardRepository repository;

  GetFilteredDashboardStatsUseCase(this.repository);

  @override
  Future<Either<Failure, DashboardStats>> call(DashboardStatsParams params) async {
    return await repository.getFilteredDashboardStats(params);
  }
}

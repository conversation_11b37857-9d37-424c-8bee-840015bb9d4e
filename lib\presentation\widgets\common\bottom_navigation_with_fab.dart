import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

class BottomNavigationWithFAB extends StatelessWidget {
  final int? currentIndex;
  final Function(int)? onTap;
  final VoidCallback? onFABPressed;

  const BottomNavigationWithFAB({
    super.key,
    this.currentIndex,
    this.onTap,
    this.onFABPressed,
  });

  @override
  Widget build(BuildContext context) {
    final location = GoRouterState.of(context).uri.toString();
    final autoCurrentIndex =
        currentIndex ?? NavigationHelper.getCurrentIndex(location);
    final autoOnTap =
        onTap ?? (index) => NavigationHelper.handleNavigation(context, index);

    return SizedBox(
      height: 80 + 28, // Navigation height + FAB overflow
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Bottom Navigation Bar
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // First item (Home) - takes 1/4 of width
                  Expanded(
                    child: _buildNavItem(
                      context: context,
                      icon: Icons.home,
                      label: 'Accueil',
                      index: 0,
                      isActive: autoCurrentIndex == 0,
                      onTap: autoOnTap,
                    ),
                  ),
                  // Second item (My Medicines) - takes 1/4 of width
                  Expanded(
                    child: _buildNavItem(
                      context: context,
                      icon: Icons.medication,
                      label: 'Mes Médic.',
                      index: 1,
                      isActive: autoCurrentIndex == 1,
                      onTap: autoOnTap,
                    ),
                  ),
                  // Empty space for FAB - fixed width
                  const SizedBox(width: 56),
                  // Third item (Reminders) - takes 1/4 of width
                  Expanded(
                    child: _buildNavItem(
                      context: context,
                      icon: Icons.alarm,
                      label: 'Rappels',
                      index: 2,
                      isActive: autoCurrentIndex == 2,
                      onTap: autoOnTap,
                    ),
                  ),
                  // Fourth item (Settings) - takes 1/4 of width
                  Expanded(
                    child: _buildNavItem(
                      context: context,
                      icon: Icons.settings,
                      label: 'Paramètres',
                      index: 3,
                      isActive: autoCurrentIndex == 3,
                      onTap: autoOnTap,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Floating Action Button - positioned between My Medicines and Reminders
          Positioned(
            top: 0, // Position at the top of the container
            left: MediaQuery.of(context).size.width / 2 -
                28, // Center horizontally (between items 1 and 2)
            child: Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.teal,
                    AppColors.teal.withValues(alpha: 0.8),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.teal.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap:
                      onFABPressed ?? () => _showAddOptionsBottomSheet(context),
                  borderRadius: BorderRadius.circular(28),
                  child: const Icon(
                    Icons.add,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    required IconData icon,
    required String label,
    required int index,
    required bool isActive,
    required Function(int) onTap,
  }) {
    return GestureDetector(
      onTap: () => onTap(index),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isActive ? AppColors.teal : AppColors.grey400,
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: AppTextStyles.labelSmall.copyWith(
                color: isActive ? AppColors.teal : AppColors.grey400,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddOptionsBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.grey300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Title
            Padding(
              padding: const EdgeInsets.all(24),
              child: Text(
                'Ajouter',
                style: AppTextStyles.titleLarge,
              ),
            ),

            // Options
            _buildAddOption(
              context: context,
              icon: Icons.medication,
              title: 'Ajouter un médicament',
              subtitle: 'Ajouter un nouveau médicament à votre pharmacie',
              onTap: () {
                Navigator.of(context).pop();
                context.push('/medicines/add');
              },
            ),
            _buildAddOption(
              context: context,
              icon: Icons.location_on,
              title: 'Ajouter un emplacement',
              subtitle: 'Créer un nouvel emplacement de stockage',
              onTap: () {
                Navigator.of(context).pop();
                context.push('/locations');
              },
            ),
            _buildAddOption(
              context: context,
              icon: Icons.person_add,
              title: 'Ajouter un membre',
              subtitle: 'Ajouter un membre de la famille',
              onTap: () {
                Navigator.of(context).pop();
                context.push('/family');
              },
            ),
            _buildAddOption(
              context: context,
              icon: Icons.add_alarm,
              title: 'Ajouter un rappel',
              subtitle: 'Configurer un rappel de médicament',
              onTap: () {
                Navigator.of(context).pop();
                context.push('/reminders/add');
              },
            ),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildAddOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.teal.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: AppColors.teal,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: AppColors.grey400,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}

/// Navigation helper to handle bottom navigation routing
class NavigationHelper {
  static void handleNavigation(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go('/dashboard');
        break;
      case 1:
        context.go('/medicines/my');
        break;
      case 2:
        context.go('/reminders');
        break;
      case 3:
        context.go('/settings');
        break;
    }
  }

  static int getCurrentIndex(String location) {
    if (location.startsWith('/dashboard') || location == '/') {
      return 0;
    } else if (location.startsWith('/medicines') ||
        location.startsWith('/medicines/add')) {
      return 1;
    } else if (location.startsWith('/reminders')) {
      return 2;
    } else if (location.startsWith('/settings')) {
      return 3;
    }
    return 0; // Default to dashboard
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/medicine.dart';
import '../../../domain/entities/reminder.dart';
import '../../bloc/reminder/reminder_bloc.dart';
import '../../bloc/reminder/reminder_event.dart';
import '../../bloc/reminder/reminder_state.dart';

enum FrequencyType { daily, weekly, hourlyInterval }

class EditReminderPage extends StatefulWidget {
  final String reminderId;
  final Reminder? reminder;
  final Medicine? medicine;

  const EditReminderPage({
    super.key,
    required this.reminderId,
    this.reminder,
    this.medicine,
  });

  @override
  State<EditReminderPage> createState() => _EditReminderPageState();
}

class _EditReminderPageState extends State<EditReminderPage> {
  final _formKey = GlobalKey<FormState>();

  // Form controllers
  final _dosageAmountController = TextEditingController();
  final _notesController = TextEditingController();

  // Form state
  Medicine? _selectedMedicine;
  String _dosageUnit = 'comprimé';
  FrequencyType _frequency = FrequencyType.daily;
  TimeOfDay _reminderTime = TimeOfDay.now();
  DateTime _startDate = DateTime.now();
  DateTime? _endDate;
  List<int> _selectedDays = [1, 2, 3, 4, 5, 6, 7];
  int _intervalHours = 8;
  List<String> _selectedTimeSlots = [];
  TimeOfDay _intervalStartTime = TimeOfDay(hour: 8, minute: 0);
  bool _isActive = true;

  FrequencyType _getFrequencyTypeFromString(String frequencyType) {
    switch (frequencyType) {
      case 'DAILY':
        return FrequencyType.daily;
      case 'WEEKLY':
        return FrequencyType.weekly;
      case 'HOURLY_INTERVAL':
        return FrequencyType.hourlyInterval;
      default:
        return FrequencyType.daily;
    }
  }

  String _getFrequencyTypeString(FrequencyType frequency) {
    switch (frequency) {
      case FrequencyType.daily:
        return 'DAILY';
      case FrequencyType.weekly:
        return 'WEEKLY';
      case FrequencyType.hourlyInterval:
        return 'HOURLY_INTERVAL';
    }
  }

  @override
  void initState() {
    super.initState();
    _initializeFormData();
  }

  void _initializeFormData() {
    if (widget.reminder != null && widget.medicine != null) {
      final reminder = widget.reminder!;
      final medicine = widget.medicine!;

      // Pre-fill form with existing reminder data
      _selectedMedicine = medicine;
      _frequency = _getFrequencyTypeFromString(reminder.frequencyType);
      _isActive = reminder.isActive;

      // Pre-populate dosage and notes
      if (reminder.dosageAmount != null) {
        _dosageAmountController.text = reminder.dosageAmount.toString();
      }
      if (reminder.dosageUnit != null) {
        _dosageUnit = reminder.dosageUnit!;
      }
      if (reminder.notes != null) {
        _notesController.text = reminder.notes!;
      }

      // Set dates
      _startDate = reminder.startDate;
      _endDate = reminder.endDate;

      // Set frequency-specific data
      switch (reminder.frequencyType) {
        case 'DAILY':
          if (reminder.times.isNotEmpty) {
            _selectedTimeSlots = reminder.times;
            final timeParts = reminder.times.first.split(':');
            _reminderTime = TimeOfDay(
              hour: int.parse(timeParts[0]),
              minute: int.parse(timeParts[1]),
            );
          }
          break;
        case 'WEEKLY':
          _selectedDays = reminder.frequencyDays.isNotEmpty
              ? reminder.frequencyDays
              : [1, 2, 3, 4, 5, 6, 7];
          if (reminder.times.isNotEmpty) {
            final timeParts = reminder.times.first.split(':');
            _reminderTime = TimeOfDay(
              hour: int.parse(timeParts[0]),
              minute: int.parse(timeParts[1]),
            );
          }
          break;
        case 'HOURLY_INTERVAL':
          _intervalHours = reminder.frequencyValue ?? 8;
          if (reminder.times.isNotEmpty) {
            final timeParts = reminder.times.first.split(':');
            _intervalStartTime = TimeOfDay(
              hour: int.parse(timeParts[0]),
              minute: int.parse(timeParts[1]),
            );
          }
          break;
      }
    }
  }

  @override
  void dispose() {
    _dosageAmountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      body: Column(
        children: [
          // Header with teal background (consistent with app design)
          Container(
            padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Modifier le rappel',
                    style: AppTextStyles.headlineMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content container with white background
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -4),
                  ),
                ],
              ),
              child: BlocListener<ReminderBloc, ReminderState>(
                listener: (context, state) {
                  if (state is ReminderUpdated) {
                    _showSuccessDialog(context);
                  } else if (state is ReminderError) {
                    _showErrorDialog(context, state.message);
                  }
                },
                child: BlocBuilder<ReminderBloc, ReminderState>(
                  builder: (context, state) {
                    final isLoading = state is ReminderLoading;

                    return Stack(
                      children: [
                        SingleChildScrollView(
                          padding: const EdgeInsets.all(16),
                          child: Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Medicine info (read-only)
                                _buildMedicineInfo(),
                                const SizedBox(height: 16),

                                // Dosage field
                                _buildDosageField(),
                                const SizedBox(height: 16),

                                // Period selection
                                _buildPeriodField(),
                                const SizedBox(height: 16),

                                // Frequency display (simplified for edit)
                                _buildFrequencyInfo(),
                                const SizedBox(height: 16),

                                // Time info
                                _buildTimeInfo(),
                                const SizedBox(height: 16),

                                // Notes field
                                _buildNotesField(),
                                const SizedBox(height: 16),

                                // Active toggle
                                _buildActiveToggle(),
                                const SizedBox(height: 24),

                                // Update button
                                _buildUpdateButton(),
                                const SizedBox(height: 24),
                              ],
                            ),
                          ),
                        ),
                        // Loading overlay
                        if (isLoading)
                          Container(
                            color: Colors.black.withOpacity(0.3),
                            child: Center(
                              child: Container(
                                padding: const EdgeInsets.all(24),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    CircularProgressIndicator(
                                        color: AppColors.teal),
                                    const SizedBox(height: 16),
                                    Text(
                                      'Mise à jour du rappel...',
                                      style: AppTextStyles.bodyMedium.copyWith(
                                        color: AppColors.grey700,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMedicineInfo() {
    if (_selectedMedicine == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.teal.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.teal.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.medication, color: AppColors.teal, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _selectedMedicine!.displayName,
                  style: AppTextStyles.titleMedium.copyWith(
                    color: AppColors.teal,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (_selectedMedicine!.form != null)
                  Text(
                    _selectedMedicine!.form!,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDosageField() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.grey200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Dosage',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.grey800,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: _dosageAmountController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Quantité',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: const EdgeInsets.all(16),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Veuillez entrer une quantité';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                flex: 3,
                child: DropdownButtonFormField<String>(
                  value: _dosageUnit,
                  decoration: InputDecoration(
                    labelText: 'Unité',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: const EdgeInsets.all(16),
                  ),
                  items: ['comprimé', 'gélule', 'goutte', 'ml', 'mg', 'g']
                      .map((unit) => DropdownMenuItem(
                            value: unit,
                            child: Text(unit),
                          ))
                      .toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _dosageUnit = value;
                      });
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodField() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.grey200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Période',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.grey800,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: _startDate,
                      firstDate:
                          DateTime.now().subtract(const Duration(days: 365)),
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                    );
                    if (date != null) {
                      setState(() {
                        _startDate = date;
                      });
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.grey300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Début',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.grey600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${_startDate.day}/${_startDate.month}/${_startDate.year}',
                          style: AppTextStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: InkWell(
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate:
                          _endDate ?? _startDate.add(const Duration(days: 30)),
                      firstDate: _startDate,
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                    );
                    if (date != null) {
                      setState(() {
                        _endDate = date;
                      });
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.grey300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Fin (optionnel)',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.grey600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _endDate != null
                              ? '${_endDate!.day}/${_endDate!.month}/${_endDate!.year}'
                              : 'Non définie',
                          style: AppTextStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.w500,
                            color: _endDate != null
                                ? AppColors.grey800
                                : AppColors.grey500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFrequencyInfo() {
    String frequencyText;
    switch (_frequency) {
      case FrequencyType.daily:
        frequencyText = 'Quotidien';
        break;
      case FrequencyType.weekly:
        frequencyText = 'Hebdomadaire';
        break;
      case FrequencyType.hourlyInterval:
        frequencyText = 'Toutes les $_intervalHours heures';
        break;
    }

    return InkWell(
      onTap: _showFrequencySelector,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.navy.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppColors.navy.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(Icons.repeat, color: AppColors.navy, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Fréquence',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    frequencyText,
                    style: AppTextStyles.titleMedium.copyWith(
                      color: AppColors.navy,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.edit, color: AppColors.navy, size: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeInfo() {
    String timeText;
    switch (_frequency) {
      case FrequencyType.daily:
        timeText = _selectedTimeSlots.isNotEmpty
            ? _selectedTimeSlots.join(', ')
            : _reminderTime.format(context);
        break;
      case FrequencyType.weekly:
        final dayNames =
            _selectedDays.map((day) => _getDayName(day)).join(', ');
        timeText = '$dayNames à ${_reminderTime.format(context)}';
        break;
      case FrequencyType.hourlyInterval:
        timeText = 'Début: ${_intervalStartTime.format(context)}';
        break;
    }

    return InkWell(
      onTap: _showTimeSelector,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.teal.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppColors.teal.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(Icons.access_time, color: AppColors.teal, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Horaires',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    timeText,
                    style: AppTextStyles.titleMedium.copyWith(
                      color: AppColors.teal,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.edit, color: AppColors.teal, size: 20),
          ],
        ),
      ),
    );
  }

  String _getDayName(int day) {
    switch (day) {
      case 1:
        return 'Lun';
      case 2:
        return 'Mar';
      case 3:
        return 'Mer';
      case 4:
        return 'Jeu';
      case 5:
        return 'Ven';
      case 6:
        return 'Sam';
      case 7:
        return 'Dim';
      default:
        return '';
    }
  }

  Widget _buildNotesField() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.grey200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notes (optionnel)',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.grey800,
            ),
          ),
          const SizedBox(height: 12),
          TextFormField(
            controller: _notesController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Ajoutez des notes sur ce rappel...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding: const EdgeInsets.all(16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveToggle() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.grey200),
      ),
      child: Row(
        children: [
          Icon(
            _isActive ? Icons.notifications_active : Icons.notifications_off,
            color: _isActive ? AppColors.success : AppColors.grey500,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Rappel actif',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.grey800,
                  ),
                ),
                Text(
                  _isActive
                      ? 'Le rappel est activé et enverra des notifications'
                      : 'Le rappel est désactivé',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.grey600,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _isActive,
            onChanged: (value) {
              setState(() {
                _isActive = value;
              });
            },
            activeColor: AppColors.teal,
          ),
        ],
      ),
    );
  }

  Widget _buildUpdateButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _updateReminder,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.teal,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.update, size: 20),
            const SizedBox(width: 8),
            Text(
              'Mettre à jour le rappel',
              style: AppTextStyles.titleMedium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _updateReminder() {
    if (!_formKey.currentState!.validate() || _selectedMedicine == null) {
      return;
    }

    // Create updated reminder with all fields including dosage and notes
    final updatedReminder = Reminder(
      id: widget.reminderId,
      userMedicineId: _selectedMedicine!.id,
      name: widget.reminder?.name, // Preserve existing name
      dosageAmount: _dosageAmountController.text.isNotEmpty
          ? double.tryParse(_dosageAmountController.text)
          : null,
      dosageUnit: _dosageUnit,
      frequencyType: _getFrequencyTypeString(_frequency),
      times: _getTimesForFrequency(),
      frequencyDays: _frequency == FrequencyType.weekly ? _selectedDays : [],
      frequencyValue:
          _frequency == FrequencyType.hourlyInterval ? _intervalHours : null,
      specificDates: [],
      startDate: _startDate,
      endDate: _endDate,
      notes: _notesController.text.isNotEmpty ? _notesController.text : null,
      isActive: _isActive,
      status: widget.reminder?.status ?? ReminderStatus.active,
      createdAt: widget.reminder?.createdAt, // Preserve creation time
      updatedAt: DateTime.now(), // Set update time
    );

    // Dispatch update event
    context.read<ReminderBloc>().add(UpdateReminder(updatedReminder));
  }

  List<String> _getTimesForFrequency() {
    switch (_frequency) {
      case FrequencyType.daily:
        return _selectedTimeSlots.isNotEmpty
            ? _selectedTimeSlots
            : [
                '${_reminderTime.hour.toString().padLeft(2, '0')}:${_reminderTime.minute.toString().padLeft(2, '0')}'
              ];
      case FrequencyType.weekly:
        return [
          '${_reminderTime.hour.toString().padLeft(2, '0')}:${_reminderTime.minute.toString().padLeft(2, '0')}'
        ];
      case FrequencyType.hourlyInterval:
        return [
          '${_intervalStartTime.hour.toString().padLeft(2, '0')}:${_intervalStartTime.minute.toString().padLeft(2, '0')}'
        ];
    }
  }

  void _showSuccessDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        icon: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.success.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.check_circle,
            color: AppColors.success,
            size: 32,
          ),
        ),
        title: Text(
          'Rappel mis à jour',
          style: AppTextStyles.titleLarge.copyWith(
            color: AppColors.grey900,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
        content: Text(
          'Votre rappel a été mis à jour avec succès.',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.grey600,
          ),
          textAlign: TextAlign.center,
        ),
        actions: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                context.pop(); // Return to reminders page
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Retour aux rappels',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        icon: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.error.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.error,
            color: AppColors.error,
            size: 32,
          ),
        ),
        title: Text(
          'Erreur',
          style: AppTextStyles.titleLarge.copyWith(
            color: AppColors.grey900,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
        content: Text(
          message,
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.grey600,
          ),
          textAlign: TextAlign.center,
        ),
        actions: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'OK',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showFrequencySelector() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12),
              decoration: BoxDecoration(
                color: AppColors.grey400,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Text(
                'Choisir la fréquence',
                style: AppTextStyles.headlineSmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),

            // Frequency options
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                children: [
                  _buildFrequencyOption(
                    'Quotidien',
                    FrequencyType.daily,
                    Icons.today,
                  ),
                  _buildFrequencyOption(
                    'Hebdomadaire',
                    FrequencyType.weekly,
                    Icons.calendar_view_week,
                  ),
                  _buildFrequencyOption(
                    'Toutes les X heures',
                    FrequencyType.hourlyInterval,
                    Icons.schedule,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFrequencyOption(
      String title, FrequencyType type, IconData icon) {
    final isSelected = _frequency == type;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          setState(() {
            _frequency = type;
          });
          Navigator.of(context).pop();
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected
                ? AppColors.navy.withOpacity(0.1)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? AppColors.navy : AppColors.grey300,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                icon,
                color: isSelected ? AppColors.navy : AppColors.grey600,
                size: 24,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  title,
                  style: AppTextStyles.titleMedium.copyWith(
                    color: isSelected ? AppColors.navy : AppColors.grey800,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  ),
                ),
              ),
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: AppColors.navy,
                  size: 24,
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _showTimeSelector() {
    switch (_frequency) {
      case FrequencyType.daily:
        _showDailyTimeSelector();
        break;
      case FrequencyType.weekly:
        _showWeeklyTimeSelector();
        break;
      case FrequencyType.hourlyInterval:
        _showHourlyIntervalSelector();
        break;
    }
  }

  void _showDailyTimeSelector() async {
    final time = await showTimePicker(
      context: context,
      initialTime: _reminderTime,
    );
    if (time != null) {
      setState(() {
        _reminderTime = time;
      });
    }
  }

  void _showWeeklyTimeSelector() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          height: MediaQuery.of(context).size.height * 0.7,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: AppColors.grey400,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(20),
                child: Text(
                  'Jours et heure',
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              // Days selection
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Jours de la semaine',
                      style: AppTextStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      children: [
                        for (int day = 1; day <= 7; day++)
                          FilterChip(
                            label: Text(_getDayName(day)),
                            selected: _selectedDays.contains(day),
                            onSelected: (selected) {
                              setModalState(() {
                                if (selected) {
                                  _selectedDays.add(day);
                                } else {
                                  _selectedDays.remove(day);
                                }
                              });
                            },
                          ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Time selection
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Heure',
                      style: AppTextStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    InkWell(
                      onTap: () async {
                        final time = await showTimePicker(
                          context: context,
                          initialTime: _reminderTime,
                        );
                        if (time != null) {
                          setModalState(() {
                            _reminderTime = time;
                          });
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.grey300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.access_time, color: AppColors.teal),
                            const SizedBox(width: 12),
                            Text(
                              _reminderTime.format(context),
                              style: AppTextStyles.titleMedium,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const Spacer(),

              // Save button
              Padding(
                padding: const EdgeInsets.all(20),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        // Update the main state
                      });
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.teal,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Confirmer',
                      style: AppTextStyles.titleMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showHourlyIntervalSelector() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          height: MediaQuery.of(context).size.height * 0.5,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: AppColors.grey400,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(20),
                child: Text(
                  'Intervalle horaire',
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              // Interval selection
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Toutes les X heures',
                      style: AppTextStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            initialValue: _intervalHours.toString(),
                            keyboardType: TextInputType.number,
                            decoration: const InputDecoration(
                              labelText: 'Heures',
                              border: OutlineInputBorder(),
                            ),
                            onChanged: (value) {
                              final hours = int.tryParse(value);
                              if (hours != null && hours > 0) {
                                setModalState(() {
                                  _intervalHours = hours;
                                });
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Start time selection
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Heure de début',
                      style: AppTextStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    InkWell(
                      onTap: () async {
                        final time = await showTimePicker(
                          context: context,
                          initialTime: _intervalStartTime,
                        );
                        if (time != null) {
                          setModalState(() {
                            _intervalStartTime = time;
                          });
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.grey300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.access_time, color: AppColors.teal),
                            const SizedBox(width: 12),
                            Text(
                              _intervalStartTime.format(context),
                              style: AppTextStyles.titleMedium,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const Spacer(),

              // Save button
              Padding(
                padding: const EdgeInsets.all(20),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        // Update the main state
                      });
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.teal,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Confirmer',
                      style: AppTextStyles.titleMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../bloc/family_manager/family_manager_state.dart';

class FamilyStatisticsCard extends StatelessWidget {
  final FamilyMemberStatistics statistics;
  final Function(String?)? onRelationshipTap;

  const FamilyStatisticsCard({
    super.key,
    required this.statistics,
    this.onRelationshipTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Aperçu de la famille',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            // Age groups statistics
            Row(
              children: [
                Expanded(
                  child: _buildAgeGroupItem(
                    title: 'Adultes',
                    count: statistics.adults,
                    icon: Icons.person,
                    color: AppColors.teal,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildAgeGroupItem(
                    title: 'Enfants',
                    count: statistics.children,
                    icon: Icons.child_friendly,
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildAgeGroupItem(
                    title: 'Seniors',
                    count: statistics.seniors,
                    icon: Icons.elderly,
                    color: Colors.brown,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Relationships statistics
            if (statistics.relationshipCounts.isNotEmpty) ...[
              Text(
                'Relations familiales',
                style: AppTextStyles.titleSmall.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.grey700,
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: statistics.relationshipCounts.entries
                    .where((entry) => entry.value > 0)
                    .map((entry) => _buildRelationshipChip(
                          relationship: entry.key,
                          count: entry.value,
                        ))
                    .toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAgeGroupItem({
    required String title,
    required int count,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            count.toString(),
            style: AppTextStyles.titleLarge.copyWith(
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.grey600,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildRelationshipChip({
    required String relationship,
    required int count,
  }) {
    final color = _getRelationshipColor(relationship);
    
    return InkWell(
      onTap: () => onRelationshipTap?.call(relationship),
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getRelationshipIcon(relationship),
              size: 16,
              color: color,
            ),
            const SizedBox(width: 6),
            Text(
              _getRelationshipDisplayName(relationship),
              style: AppTextStyles.labelMedium.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                count.toString(),
                style: AppTextStyles.labelSmall.copyWith(
                  color: color,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getRelationshipColor(String relationship) {
    switch (relationship.toLowerCase()) {
      case 'parent':
      case 'père':
      case 'mère':
        return AppColors.teal;
      case 'enfant':
      case 'fils':
      case 'fille':
        return Colors.orange;
      case 'conjoint':
      case 'époux':
      case 'épouse':
        return Colors.pink;
      case 'frère':
      case 'sœur':
        return AppColors.purple;
      case 'grand-parent':
      case 'grand-père':
      case 'grand-mère':
        return Colors.brown;
      case 'petit-enfant':
      case 'petit-fils':
      case 'petite-fille':
        return Colors.amber;
      default:
        return AppColors.grey600;
    }
  }

  IconData _getRelationshipIcon(String relationship) {
    switch (relationship.toLowerCase()) {
      case 'parent':
      case 'père':
      case 'mère':
        return Icons.person;
      case 'enfant':
      case 'fils':
      case 'fille':
        return Icons.child_friendly;
      case 'conjoint':
      case 'époux':
      case 'épouse':
        return Icons.favorite;
      case 'frère':
      case 'sœur':
        return Icons.people;
      case 'grand-parent':
      case 'grand-père':
      case 'grand-mère':
        return Icons.elderly;
      case 'petit-enfant':
      case 'petit-fils':
      case 'petite-fille':
        return Icons.child_care;
      default:
        return Icons.person_outline;
    }
  }

  String _getRelationshipDisplayName(String relationship) {
    switch (relationship.toLowerCase()) {
      case 'parent':
        return 'Parents';
      case 'père':
        return 'Pères';
      case 'mère':
        return 'Mères';
      case 'enfant':
        return 'Enfants';
      case 'fils':
        return 'Fils';
      case 'fille':
        return 'Filles';
      case 'conjoint':
        return 'Conjoints';
      case 'époux':
        return 'Époux';
      case 'épouse':
        return 'Épouses';
      case 'frère':
        return 'Frères';
      case 'sœur':
        return 'Sœurs';
      case 'grand-parent':
        return 'Grands-parents';
      case 'grand-père':
        return 'Grands-pères';
      case 'grand-mère':
        return 'Grands-mères';
      case 'petit-enfant':
        return 'Petits-enfants';
      case 'petit-fils':
        return 'Petits-fils';
      case 'petite-fille':
        return 'Petites-filles';
      default:
        return relationship.isNotEmpty 
            ? relationship[0].toUpperCase() + relationship.substring(1)
            : 'Autres';
    }
  }
}

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:convert';

/// Abstract class for secure storage operations
abstract class SecureStorage {
  Future<void> setString(String key, String value);
  Future<String?> getString(String key);
  Future<void> setObject(String key, Map<String, dynamic> value);
  Future<Map<String, dynamic>?> getObject(String key);
  Future<void> remove(String key);
  Future<void> clear();
  Future<bool> containsKey(String key);
  Future<Map<String, String>> getAll();
}

/// Implementation of SecureStorage using FlutterSecureStorage
class SecureStorageImpl implements SecureStorage {
  final FlutterSecureStorage flutterSecureStorage;

  SecureStorageImpl(this.flutterSecureStorage);

  @override
  Future<void> setString(String key, String value) async {
    await flutterSecureStorage.write(key: key, value: value);
  }

  @override
  Future<String?> getString(String key) async {
    return await flutterSecureStorage.read(key: key);
  }

  @override
  Future<void> setObject(String key, Map<String, dynamic> value) async {
    final jsonString = json.encode(value);
    await flutterSecureStorage.write(key: key, value: jsonString);
  }

  @override
  Future<Map<String, dynamic>?> getObject(String key) async {
    final jsonString = await flutterSecureStorage.read(key: key);
    if (jsonString == null) return null;
    
    try {
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> remove(String key) async {
    await flutterSecureStorage.delete(key: key);
  }

  @override
  Future<void> clear() async {
    await flutterSecureStorage.deleteAll();
  }

  @override
  Future<bool> containsKey(String key) async {
    final value = await flutterSecureStorage.read(key: key);
    return value != null;
  }

  @override
  Future<Map<String, String>> getAll() async {
    return await flutterSecureStorage.readAll();
  }
}

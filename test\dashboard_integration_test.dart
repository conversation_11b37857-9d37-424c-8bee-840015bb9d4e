import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mocktail/mocktail.dart';

import 'package:medytrack_mobile_v2/presentation/pages/dashboard/dashboard_page.dart';
import 'package:medytrack_mobile_v2/presentation/bloc/dashboard/dashboard_bloc.dart';
import 'package:medytrack_mobile_v2/presentation/bloc/auth/auth_bloc.dart';
import 'package:medytrack_mobile_v2/presentation/bloc/auth/auth_state.dart'
    as auth_state;
import 'package:medytrack_mobile_v2/presentation/bloc/medicine/medicine_bloc.dart';
import 'package:medytrack_mobile_v2/domain/entities/user.dart';
import 'package:medytrack_mobile_v2/domain/entities/dashboard_stats.dart';
import 'package:medytrack_mobile_v2/domain/entities/medicine.dart';

// Mock classes
class MockDashboardBloc extends Mock implements DashboardBloc {}

class MockAuthBloc extends Mock implements AuthBloc {}

class MockMedicineBloc extends Mock implements MedicineBloc {}

void main() {
  group('Dashboard Integration Tests', () {
    late MockDashboardBloc mockDashboardBloc;
    late MockAuthBloc mockAuthBloc;
    late MockMedicineBloc mockMedicineBloc;

    setUp(() {
      mockDashboardBloc = MockDashboardBloc();
      mockAuthBloc = MockAuthBloc();
      mockMedicineBloc = MockMedicineBloc();
    });

    testWidgets('should display dashboard with all components',
        (WidgetTester tester) async {
      // Arrange
      const user = User(
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'John Doe',
        householdId: 'test-household',
        householdName: 'Test Household',
      );

      const dashboardStats = DashboardStats(
        total: 25,
        expired: 2,
        expiringSoon: 5,
        lowStock: 3,
        locationCount: 4,
        totalLocations: 6,
        tagCount: 5,
        totalTags: 7,
        locationUsagePercentage: 67,
        tagUsagePercentage: 71,
      );

      final sampleMedicines = [
        Medicine(
          id: '1',
          customName: 'Paracétamol',
          dosage: '500mg',
          quantity: 20,
          householdId: 'test-household',
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
        ),
        Medicine(
          id: '2',
          customName: 'Ibuprofène',
          dosage: '400mg',
          quantity: 5,
          householdId: 'test-household',
          createdAt: DateTime.now().subtract(const Duration(days: 2)),
        ),
      ];

      // Setup mock states
      when(() => mockAuthBloc.state).thenReturn(
        auth_state.AuthAuthenticated(
          user: user,
          householdId: 'test-household',
          householdName: 'Test Household',
        ),
      );

      when(() => mockDashboardBloc.state).thenReturn(
        DashboardLoaded(
          stats: dashboardStats,
          expiringMedicines: const [],
        ),
      );

      when(() => mockMedicineBloc.state).thenReturn(
        MedicineLoaded(medicines: sampleMedicines),
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MultiBlocProvider(
            providers: [
              BlocProvider<AuthBloc>.value(value: mockAuthBloc),
              BlocProvider<DashboardBloc>.value(value: mockDashboardBloc),
              BlocProvider<MedicineBloc>.value(value: mockMedicineBloc),
            ],
            child: const DashboardPage(),
          ),
        ),
      );

      // Assert - Check that all main components are present
      expect(find.byType(DashboardPage), findsOneWidget);

      // Check for user greeting
      expect(find.text('John Doe'), findsOneWidget);

      // Check for statistics cards
      expect(find.text('25'), findsOneWidget); // Total medicines
      expect(find.text('5'), findsOneWidget); // Expiring soon
      expect(find.text('4'), findsOneWidget); // Locations

      // Check for section headers
      expect(find.text('Ajouts récents'), findsOneWidget);
      expect(find.text('Médicaments expirant bientôt'), findsOneWidget);
    });

    testWidgets('should display loading state correctly',
        (WidgetTester tester) async {
      // Arrange
      const user = User(
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'John Doe',
        householdId: 'test-household',
        householdName: 'Test Household',
      );

      when(() => mockAuthBloc.state).thenReturn(
        auth_state.AuthAuthenticated(
          user: user,
          householdId: 'test-household',
          householdName: 'Test Household',
        ),
      );

      when(() => mockDashboardBloc.state).thenReturn(const DashboardLoading());
      when(() => mockMedicineBloc.state).thenReturn(const MedicineLoading());

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MultiBlocProvider(
            providers: [
              BlocProvider<AuthBloc>.value(value: mockAuthBloc),
              BlocProvider<DashboardBloc>.value(value: mockDashboardBloc),
              BlocProvider<MedicineBloc>.value(value: mockMedicineBloc),
            ],
            child: const DashboardPage(),
          ),
        ),
      );

      // Assert
      expect(find.byType(CircularProgressIndicator), findsWidgets);
    });

    testWidgets('should display error state correctly',
        (WidgetTester tester) async {
      // Arrange
      const user = User(
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'John Doe',
        householdId: 'test-household',
        householdName: 'Test Household',
      );

      when(() => mockAuthBloc.state).thenReturn(
        auth_state.AuthAuthenticated(
          user: user,
          householdId: 'test-household',
          householdName: 'Test Household',
        ),
      );

      when(() => mockDashboardBloc.state).thenReturn(
        const DashboardError(message: 'Failed to load dashboard data'),
      );

      when(() => mockMedicineBloc.state).thenReturn(
        const MedicineError(message: 'Failed to load medicines'),
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MultiBlocProvider(
            providers: [
              BlocProvider<AuthBloc>.value(value: mockAuthBloc),
              BlocProvider<DashboardBloc>.value(value: mockDashboardBloc),
              BlocProvider<MedicineBloc>.value(value: mockMedicineBloc),
            ],
            child: const DashboardPage(),
          ),
        ),
      );

      // Assert
      expect(find.text('Erreur de chargement'), findsWidgets);
      expect(find.text('Réessayer'), findsWidgets);
    });

    test('dashboard stats calculations should match web app logic', () {
      // Arrange
      const stats = DashboardStats(
        total: 100,
        expired: 5,
        expiringSoon: 10,
        lowStock: 8,
        locationCount: 4,
        totalLocations: 6,
        tagCount: 5,
        totalTags: 7,
        locationUsagePercentage: 67,
        tagUsagePercentage: 71,
      );

      // Assert - Test calculations match web app logic
      expect(stats.totalWithIssues, 23); // expired + expiring + low stock
      expect(stats.adequate, 77); // total - totalWithIssues
      expect(stats.issuesPercentage, 23.0); // (23/100) * 100
      expect(stats.adequatePercentage, 77.0); // (77/100) * 100
      expect(stats.hasCriticalIssues, true); // expired > 0 || lowStock > 0
      expect(stats.hasWarnings, true); // expiringSoon > 0
      expect(stats.healthStatus, 'critical'); // has critical issues
    });

    test('medicine status color mapping should be consistent', () {
      // Test that medicine status colors match the expected design system
      const testCases = [
        ('expired', 'should be red/error color'),
        ('expiring_soon', 'should be orange/warning color'),
        ('low_stock', 'should be orange/warning color'),
        ('out_of_stock', 'should be red/error color'),
        ('adequate', 'should be green/success color'),
      ];

      for (final testCase in testCases) {
        final status = testCase.$1;
        final description = testCase.$2;

        // This test ensures we have consistent status handling
        expect(status, isA<String>(), reason: 'Status $status $description');
      }
    });
  });
}

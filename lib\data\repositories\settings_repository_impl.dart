import 'package:dartz/dartz.dart';

import 'package:local_auth/local_auth.dart';
import '../../domain/entities/settings.dart';
import '../../domain/repositories/settings_repository.dart';
import '../../core/error/failures.dart';
import '../../core/network/network_info.dart';
import '../datasources/settings_local_data_source.dart';
import '../datasources/settings_remote_data_source.dart';
import '../models/settings_model.dart';

/// Implementation of settings repository
class SettingsRepositoryImpl implements SettingsRepository {
  final SettingsRemoteDataSource remoteDataSource;
  final SettingsLocalDataSource localDataSource;
  final NetworkInfo networkInfo;
  final LocalAuthentication localAuth;

  SettingsRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
    required this.localAuth,
  });

  @override
  Future<Either<Failure, Settings>> getUserSettings(String userId) async {
    try {
      if (await networkInfo.isConnected) {
        // Try to get from remote first
        final remoteSettings = await remoteDataSource.getUserSettings(userId);
        if (remoteSettings != null) {
          // Cache locally
          await localDataSource.saveUserSettings(remoteSettings);
          return Right(remoteSettings.toEntity());
        }
      }

      // Fallback to local storage
      final localSettings = await localDataSource.getUserSettings(userId);
      if (localSettings != null) {
        return Right(localSettings.toEntity());
      }

      // Return default settings if none found
      final defaultSettings = Settings.defaultSettings(
        userId: userId,
        householdId: '', // Will be set when user has household
      );
      return Right(defaultSettings);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Settings>> updateUserSettings(
      Settings settings) async {
    try {
      final settingsModel = SettingsModel.fromEntity(settings);

      // Always save locally first
      await localDataSource.saveUserSettings(settingsModel);

      if (await networkInfo.isConnected) {
        // Try to sync with remote
        final updatedSettings =
            await remoteDataSource.saveUserSettings(settingsModel);
        return Right(updatedSettings.toEntity());
      }

      return Right(settings);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, NotificationSettings>> getNotificationSettings(
      String userId) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteSettings =
            await remoteDataSource.getNotificationSettings(userId);
        if (remoteSettings != null) {
          await localDataSource.saveNotificationSettings(
              userId, remoteSettings);
          return Right(remoteSettings);
        }
      }

      final localSettings =
          await localDataSource.getNotificationSettings(userId);
      if (localSettings != null) {
        return Right(localSettings);
      }

      return const Right(NotificationSettings.defaultSettings());
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, NotificationSettings>> updateNotificationSettings(
    String userId,
    NotificationSettings settings,
  ) async {
    try {
      final settingsModel = NotificationSettingsModel.fromEntity(settings);

      await localDataSource.saveNotificationSettings(userId, settingsModel);

      if (await networkInfo.isConnected) {
        final updatedSettings = await remoteDataSource.saveNotificationSettings(
            userId, settingsModel);
        return Right(updatedSettings);
      }

      return Right(settings);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, AppSettings>> getAppSettings(String userId) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteSettings = await remoteDataSource.getAppSettings(userId);
        if (remoteSettings != null) {
          await localDataSource.saveAppSettings(userId, remoteSettings);
          return Right(remoteSettings);
        }
      }

      final localSettings = await localDataSource.getAppSettings(userId);
      if (localSettings != null) {
        return Right(localSettings);
      }

      return const Right(AppSettings.defaultSettings());
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, AppSettings>> updateAppSettings(
    String userId,
    AppSettings settings,
  ) async {
    try {
      final settingsModel = AppSettingsModel.fromEntity(settings);

      await localDataSource.saveAppSettings(userId, settingsModel);

      if (await networkInfo.isConnected) {
        final updatedSettings =
            await remoteDataSource.saveAppSettings(userId, settingsModel);
        return Right(updatedSettings);
      }

      return Right(settings);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, SecuritySettings>> getSecuritySettings(
      String userId) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteSettings =
            await remoteDataSource.getSecuritySettings(userId);
        if (remoteSettings != null) {
          await localDataSource.saveSecuritySettings(userId, remoteSettings);
          return Right(remoteSettings);
        }
      }

      final localSettings = await localDataSource.getSecuritySettings(userId);
      if (localSettings != null) {
        return Right(localSettings);
      }

      return const Right(SecuritySettings.defaultSettings());
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, SecuritySettings>> updateSecuritySettings(
    String userId,
    SecuritySettings settings,
  ) async {
    try {
      final settingsModel = SecuritySettingsModel.fromEntity(settings);

      await localDataSource.saveSecuritySettings(userId, settingsModel);

      if (await networkInfo.isConnected) {
        final updatedSettings =
            await remoteDataSource.saveSecuritySettings(userId, settingsModel);
        return Right(updatedSettings);
      }

      return Right(settings);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, PersonalizationSettings>> getPersonalizationSettings(
      String userId) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteSettings =
            await remoteDataSource.getPersonalizationSettings(userId);
        if (remoteSettings != null) {
          await localDataSource.savePersonalizationSettings(
              userId, remoteSettings);
          return Right(remoteSettings);
        }
      }

      final localSettings =
          await localDataSource.getPersonalizationSettings(userId);
      if (localSettings != null) {
        return Right(localSettings);
      }

      return const Right(PersonalizationSettings.defaultSettings());
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, PersonalizationSettings>>
      updatePersonalizationSettings(
    String userId,
    PersonalizationSettings settings,
  ) async {
    try {
      final settingsModel = PersonalizationSettingsModel.fromEntity(settings);

      await localDataSource.savePersonalizationSettings(userId, settingsModel);

      if (await networkInfo.isConnected) {
        final updatedSettings = await remoteDataSource
            .savePersonalizationSettings(userId, settingsModel);
        return Right(updatedSettings);
      }

      return Right(settings);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Settings>> resetToDefaults(
      String userId, String householdId) async {
    try {
      final defaultSettings = Settings.defaultSettings(
        userId: userId,
        householdId: householdId,
      );

      final settingsModel = SettingsModel.fromEntity(defaultSettings);

      await localDataSource.saveUserSettings(settingsModel);

      if (await networkInfo.isConnected) {
        final updatedSettings =
            await remoteDataSource.saveUserSettings(settingsModel);
        return Right(updatedSettings.toEntity());
      }

      return Right(defaultSettings);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> exportSettings(String userId) async {
    try {
      if (await networkInfo.isConnected) {
        final jsonData = await remoteDataSource.exportSettings(userId);
        return Right(jsonData);
      } else {
        return const Left(NetworkFailure('No internet connection'));
      }
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Settings>> importSettings(
      String userId, String jsonData) async {
    try {
      if (await networkInfo.isConnected) {
        final settings =
            await remoteDataSource.importSettings(userId, jsonData);
        await localDataSource.saveUserSettings(settings);
        return Right(settings.toEntity());
      } else {
        return const Left(NetworkFailure('No internet connection'));
      }
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> isBiometricAvailable() async {
    try {
      final isAvailable = await localAuth.canCheckBiometrics;
      final availableBiometrics = await localAuth.getAvailableBiometrics();
      return Right(isAvailable && availableBiometrics.isNotEmpty);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> validatePinCode(String pinCode) async {
    try {
      // Basic PIN validation - should be 4-6 digits
      final isValid = RegExp(r'^\d{4,6}$').hasMatch(pinCode);
      return Right(isValid);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> updateExpiryWarningThreshold(
      String userId, int days) async {
    try {
      if (await networkInfo.isConnected) {
        await remoteDataSource.updateExpiryWarningThreshold(userId, days);
        return const Right(null);
      } else {
        return const Left(NetworkFailure('No internet connection'));
      }
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

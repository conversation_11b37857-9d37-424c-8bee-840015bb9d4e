import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/reminder.dart';
import '../../repositories/reminder_repository.dart';

@injectable
class GetBatchRemindersUseCase implements UseCase<List<Reminder>, GetBatchRemindersParams> {
  final ReminderRepository repository;

  GetBatchRemindersUseCase(this.repository);

  @override
  Future<Either<Failure, List<Reminder>>> call(GetBatchRemindersParams params) async {
    return await repository.getRemindersForMultipleMedicines(params.userMedicineIds);
  }
}

class GetBatchRemindersParams {
  final List<String> userMedicineIds;

  GetBatchRemindersParams({required this.userMedicineIds});
}

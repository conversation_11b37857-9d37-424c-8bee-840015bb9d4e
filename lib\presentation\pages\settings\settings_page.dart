import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_event.dart';
import '../../bloc/auth/auth_state.dart' as auth_state;
import '../../bloc/settings/settings_bloc.dart';
import '../../bloc/settings/settings_event.dart';
import '../../bloc/settings/settings_state.dart';

import '../../widgets/settings/settings_section.dart';
import '../../widgets/settings/settings_tile.dart';
import '../../widgets/settings/enhanced_settings_tile.dart';
import '../../widgets/settings/toggle_setting.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  @override
  void initState() {
    super.initState();
    // Load settings when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authState = context.read<AuthBloc>().state;
      if (authState is auth_state.AuthAuthenticated) {
        context.read<SettingsBloc>().add(
              SettingsLoadRequested(userId: authState.user.id),
            );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Integrated header with gradient background
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: _getHeaderGradient(),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(32),
                bottomRight: Radius.circular(32),
              ),
            ),
            child: SafeArea(
              bottom: false,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(20, 20, 20, 40),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Integrated header card
                    _buildIntegratedHeaderCard(),
                  ],
                ),
              ),
            ),
          ),

          Expanded(
            child: BlocBuilder<AuthBloc, auth_state.AuthState>(
              builder: (context, authState) {
                if (authState is! auth_state.AuthAuthenticated) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                return BlocConsumer<SettingsBloc, SettingsState>(
                  listener: (context, settingsState) {
                    if (settingsState is SettingsError) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(settingsState.message),
                          backgroundColor: AppColors.error,
                        ),
                      );
                    } else if (settingsState is SettingsOperationSuccess) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(settingsState.message),
                          backgroundColor: AppColors.success,
                        ),
                      );
                    }
                  },
                  builder: (context, settingsState) {
                    return SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildAccountSection(context, authState.user),
                          const SizedBox(height: 24),
                          _buildPreferencesSection(context, settingsState),
                          const SizedBox(height: 24),
                          _buildPersonalizationSection(context),
                          const SizedBox(height: 24),
                          // Data section hidden as requested
                          // _buildDataSection(context, settingsState),
                          // const SizedBox(height: 24),
                          // Debug section hidden for production release
                          // _buildDebugSection(context),
                          // const SizedBox(height: 24),
                          _buildSupportSection(),
                          const SizedBox(height: 32),
                          _buildLogoutButton(context),
                        ],
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountSection(BuildContext context, dynamic user) {
    return SettingsSection(
      title: 'Compte',
      children: [
        // Profile subsection
        EnhancedSettingsTile(
          icon: Icons.person_outlined,
          title: 'Profil',
          subtitle: user.displayName ?? user.email.split('@').first,
          onTap: () => context.push('/profile'),
        ),
        EnhancedSettingsTile(
          icon: Icons.lock_outlined,
          title: 'Mot de passe',
          subtitle: 'Modifier votre mot de passe',
          onTap: () => context.push('/settings/password'),
        ),
        // Data export removed as requested
      ],
    );
  }

  Widget _buildPreferencesSection(
      BuildContext context, SettingsState settingsState) {
    final settings =
        settingsState is SettingsLoaded ? settingsState.settings : null;
    final isLoading =
        settingsState is SettingsLoading || settingsState is SettingsUpdating;

    return SettingsSection(
      title: 'Préférences',
      children: [
        // Notification and Language sections hidden as requested
        // EnhancedSettingsTile(
        //   icon: Icons.notifications_outlined,
        //   title: 'Notifications',
        //   subtitle: 'Alertes d\'expiration et rappels',
        //   onTap: () => context.push('/settings/notifications'),
        // ),
        // EnhancedSettingsTile(
        //   icon: Icons.language_outlined,
        //   title: 'Langue et région',
        //   subtitle: _getLanguageDisplayName(settings?.app.language ?? 'fr'),
        //   onTap: () => context.push('/settings/language'),
        // ),
        // Apparence section (dark mode) hidden as requested
        // ToggleSetting(
        //   icon: Icons.palette_outlined,
        //   title: 'Mode sombre',
        //   subtitle: 'Utiliser le thème sombre',
        //   value: settings?.app.isDarkMode ?? false,
        //   onChanged: (value) {
        //     if (settings != null) {
        //       context.read<SettingsBloc>().add(
        //             ThemeUpdateRequested(
        //               userId: settings.userId,
        //               isDarkMode: value,
        //             ),
        //           );
        //     }
        //   },
        //   enabled: settings != null,
        //   isLoading: isLoading &&
        //       settingsState is SettingsUpdating &&
        //       settingsState.section == 'app',
        // ),
        // Date format section also hidden as part of preferences cleanup
      ],
    );
  }

  Widget _buildPersonalizationSection(BuildContext context) {
    return SettingsSection(
      title: 'Personnalisation',
      children: [
        EnhancedSettingsTile(
          icon: Icons.family_restroom_outlined,
          title: 'Gestion de Famille',
          subtitle: 'Gérer les membres de votre famille',
          onTap: () => context.push('/family'),
        ),
        EnhancedSettingsTile(
          icon: Icons.location_on_outlined,
          title: 'Emplacements',
          subtitle: 'Gérer les lieux de stockage',
          onTap: () => context.push('/locations'),
        ),
      ],
    );
  }

  Widget _buildDataSection(BuildContext context, SettingsState settingsState) {
    final settings =
        settingsState is SettingsLoaded ? settingsState.settings : null;
    final isLoading =
        settingsState is SettingsLoading || settingsState is SettingsUpdating;

    return SettingsSection(
      title: 'Données',
      children: [
        ToggleSetting(
          icon: Icons.cloud_sync_outlined,
          title: 'Synchronisation automatique',
          subtitle: 'Synchroniser avec le cloud',
          value: settings?.app.autoSync ?? true,
          onChanged: (value) {
            if (settings != null) {
              final updatedApp = settings.app.copyWith(autoSync: value);
              context.read<SettingsBloc>().add(
                    AppSettingsUpdateRequested(
                      userId: settings.userId,
                      settings: updatedApp,
                    ),
                  );
            }
          },
          enabled: settings != null,
          isLoading: isLoading &&
              settingsState is SettingsUpdating &&
              settingsState.section == 'app',
        ),
        EnhancedSettingsTile(
          icon: Icons.cloud_outlined,
          title: 'Données & Sauvegarde',
          subtitle: 'Export, import, synchronisation',
          onTap: () => context.push('/settings/data'),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                settings?.app.autoSync == true
                    ? 'Sync activée'
                    : 'Sync désactivée',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.grey600,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                Icons.chevron_right,
                color: AppColors.grey400,
                size: 20,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDebugSection(BuildContext context) {
    return SettingsSection(
      title: 'Développement',
      children: [
        SettingsTile(
          icon: Icons.bug_report_outlined,
          title: 'Debug & Test',
          subtitle: 'Tester les fonctionnalités principales',
          onTap: () {
            context.push('/debug');
          },
        ),
        SettingsTile(
          icon: Icons.medication_outlined,
          title: 'Medicine Card Debug',
          subtitle: 'Debug status-based theming',
          onTap: () {
            context.push('/debug/medicine-cards');
          },
        ),
        SettingsTile(
          icon: Icons.add_box_outlined,
          title: 'Add Medicine Debug',
          subtitle: 'Test dual-path medicine addition workflow',
          onTap: () {
            context.push('/debug/add-medicine');
          },
        ),
      ],
    );
  }

  Widget _buildSupportSection() {
    return SettingsSection(
      title: 'Support',
      children: [
        SettingsTile(
          icon: Icons.info_outlined,
          title: 'Version de l\'application',
          subtitle: 'v0.3.1+3',
          onTap: null, // No action needed, just display version
        ),
      ],
    );
  }

  Widget _buildLogoutButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () => _showLogoutDialog(context),
        icon: const Icon(Icons.logout),
        label: const Text('Se déconnecter'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.error,
          foregroundColor: AppColors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Se déconnecter'),
        content: const Text(
          'Êtes-vous sûr de vouloir vous déconnecter ?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AuthBloc>().add(AuthSignOutRequested());
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('Se déconnecter'),
          ),
        ],
      ),
    );
  }

  /// Gradient matching the white-blue background style
  LinearGradient _getHeaderGradient() {
    return const LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        Color(0xFFE3F0FF), // Light pastel blue
        Color(0xFFFFFFFF), // White bottom blend
      ],
    );
  }

  /// Integrated header card with title (no back button for main settings page)
  Widget _buildIntegratedHeaderCard() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Text(
        'Paramètres',
        style: AppTextStyles.titleLarge.copyWith(
          color: AppColors.navy,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  // Helper methods - cleaned up unused methods
}

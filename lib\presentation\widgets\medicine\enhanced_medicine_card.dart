import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/medicine.dart';

class EnhancedMedicineCard extends StatelessWidget {
  final Medicine medicine;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final Function(int)? onQuantityChanged;

  const EnhancedMedicineCard({
    super.key,
    required this.medicine,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onTap,
    this.onLongPress,
    this.onEdit,
    this.onDelete,
    this.onQuantityChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isSelected ? 4 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected
            ? BorderSide(color: AppColors.teal, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with selection checkbox and status
              Row(
                children: [
                  // Selection checkbox
                  if (isSelectionMode)
                    Padding(
                      padding: const EdgeInsets.only(right: 12),
                      child: Checkbox(
                        value: isSelected,
                        onChanged: (value) => onTap?.call(),
                        activeColor: AppColors.teal,
                      ),
                    ),

                  // Medicine name and type
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          medicine.displayName,
                          style: AppTextStyles.titleMedium.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (medicine.dosage != null ||
                            medicine.form != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            [medicine.dosage, medicine.form]
                                .where((e) => e != null && e.isNotEmpty)
                                .join(' • '),
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.grey600,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // Status indicator
                  _buildStatusIndicator(),

                  // Action menu (only when not in selection mode)
                  if (!isSelectionMode && (onEdit != null || onDelete != null))
                    PopupMenuButton<String>(
                      icon: Icon(
                        Icons.more_vert,
                        color: AppColors.grey600,
                        size: 20,
                      ),
                      onSelected: (value) {
                        switch (value) {
                          case 'edit':
                            onEdit?.call();
                            break;
                          case 'delete':
                            onDelete?.call();
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        if (onEdit != null)
                          PopupMenuItem<String>(
                            value: 'edit',
                            child: Row(
                              children: [
                                Icon(
                                  Icons.edit_outlined,
                                  size: 18,
                                  color: AppColors.grey600,
                                ),
                                const SizedBox(width: 8),
                                const Text('Modifier'),
                              ],
                            ),
                          ),
                        if (onDelete != null)
                          PopupMenuItem<String>(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(
                                  Icons.delete_outline,
                                  size: 18,
                                  color: AppColors.error,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Supprimer',
                                  style: TextStyle(color: AppColors.error),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                ],
              ),

              const SizedBox(height: 12),

              // Medicine details
              Row(
                children: [
                  // Quantity section
                  Expanded(
                    child: _buildQuantitySection(),
                  ),

                  const SizedBox(width: 16),

                  // Expiration section
                  if (medicine.expiration != null)
                    Expanded(
                      child: _buildExpirationSection(),
                    ),
                ],
              ),

              const SizedBox(height: 12),

              // Location and family member
              Row(
                children: [
                  if (medicine.locationName != null) ...[
                    Icon(
                      Icons.location_on,
                      size: 16,
                      color: AppColors.grey500,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      medicine.locationName!,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.grey600,
                      ),
                    ),
                  ],
                  if (medicine.locationName != null &&
                      medicine.familyMemberName != null)
                    Text(
                      ' • ',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.grey500,
                      ),
                    ),
                  if (medicine.familyMemberName != null) ...[
                    Icon(
                      Icons.person,
                      size: 16,
                      color: AppColors.grey500,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      medicine.familyMemberName!,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.grey600,
                      ),
                    ),
                  ],
                ],
              ),

              // Tags
              if (medicine.tags.isNotEmpty) ...[
                const SizedBox(height: 8),
                Wrap(
                  spacing: 6,
                  runSpacing: 4,
                  children: medicine.tags.take(3).map((tag) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.teal.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        tag,
                        style: AppTextStyles.labelSmall.copyWith(
                          color: AppColors.teal,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusIndicator() {
    final status = medicine.status;
    Color color;
    IconData icon;

    switch (status) {
      case MedicineStatus.expired:
        color = AppColors.error;
        icon = Icons.error;
        break;
      case MedicineStatus.expiringSoon:
        color = AppColors.warning;
        icon = Icons.warning;
        break;
      case MedicineStatus.lowStock:
        color = AppColors.warning;
        icon = Icons.inventory_2;
        break;
      case MedicineStatus.outOfStock:
        color = AppColors.error;
        icon = Icons.remove_circle;
        break;
      case MedicineStatus.normal:
        return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        icon,
        size: 16,
        color: color,
      ),
    );
  }

  Widget _buildQuantitySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quantité',
          style: AppTextStyles.labelSmall.copyWith(
            color: AppColors.grey500,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            // Decrease button
            if (onQuantityChanged != null && medicine.quantity > 0)
              InkWell(
                onTap: () => onQuantityChanged!(medicine.quantity - 1),
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: AppColors.grey200,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.remove,
                    size: 16,
                    color: AppColors.grey600,
                  ),
                ),
              ),

            if (onQuantityChanged != null) const SizedBox(width: 8),

            // Quantity display
            Text(
              '${medicine.quantity}',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: medicine.quantity == 0 ? AppColors.error : null,
              ),
            ),

            if (onQuantityChanged != null) const SizedBox(width: 8),

            // Increase button
            if (onQuantityChanged != null)
              InkWell(
                onTap: () => onQuantityChanged!(medicine.quantity + 1),
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: AppColors.teal.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.add,
                    size: 16,
                    color: AppColors.teal,
                  ),
                ),
              ),
          ],
        ),
        if (medicine.lowStockThreshold > 0 &&
            medicine.quantity <= medicine.lowStockThreshold &&
            medicine.quantity > 0) ...[
          const SizedBox(height: 4),
          Text(
            'Stock faible',
            style: AppTextStyles.labelSmall.copyWith(
              color: AppColors.warning,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildExpirationSection() {
    if (medicine.expiration == null) return const SizedBox.shrink();

    final now = DateTime.now();
    final expiration = medicine.expiration!;
    final isExpired = expiration.isBefore(now);
    final daysUntilExpiration = expiration.difference(now).inDays;

    Color textColor = AppColors.grey600;
    String statusText = '';

    if (isExpired) {
      textColor = AppColors.error;
      statusText = 'Expiré';
    } else if (daysUntilExpiration <= 30) {
      textColor = AppColors.warning;
      statusText = 'Expire bientôt';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Expiration',
          style: AppTextStyles.labelSmall.copyWith(
            color: AppColors.grey500,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          DateFormat('MMM yyyy').format(expiration),
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w500,
            color: textColor,
          ),
        ),
        if (statusText.isNotEmpty) ...[
          const SizedBox(height: 4),
          Text(
            statusText,
            style: AppTextStyles.labelSmall.copyWith(
              color: textColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }
}
